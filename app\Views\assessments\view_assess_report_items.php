<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>



<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">

            <div class="col-sm-6">
                <h1> <i class="fas fa-line-chart"></i> Assessment Report Items</h1>
                <h5 class="m-0"></h5>

            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item ">...</li>
                    <li class="breadcrumb-item">Report Groups</li>
                    <li class="breadcrumb-item active">Report Items</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">
    <div class="row">
        <div class="col-md-12">
            <a href="<?= base_url() ?>view_assess_report_groupings/<?= $assex['ucode'] ?>" class="btn btn-outline-dark "> <i class="fa fa-reply" aria-hidden="true"></i> Back</a>

        </div>
    </div>
    <!-- ./col -->

    <div class="row pt-2">
        <div class="col-md-12 mb-2">
            <ul class="list-group">
                <!--tips: add .list-group-flush to the .list-group to remove some borders and rounded corners-->
                <li class="list-group-item "><b class=" float-left">Plan: </b> <span class=" float-right"> <?= $plan['code'] ?>. <?= $plan['title'] ?> </span> </li>
                <li class="list-group-item "><b class=" float-left">Exercise: </b> <span class=" float-right"><?= $assex['code'] ?>. <?= $assex['title'] ?> </span> </li>
                <li class="list-group-item "><b class=" float-left">Report Group: </b> <span class=" float-right"><?= $report_groups['code'] ?>. <?= $report_groups['title'] ?> </span> </li>
                
            </ul>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info">
                    <i class="fas fa-info-circle    "></i> Items Groups
                </div>
                <div class="card-body p-0 table-responsive">
                    <table class="table text-nowrap">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Title</th>
                                <th width="20%">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($items_groups as $row) : ?>
                                <tr>
                                    <td scope="row">

                                        <?= $row['code'] ?>
                                    </td>
                                    <td scope="row">

                                        <?= $row['title'] ?>
                                    </td>

                                    <td class="">

                                        <?= form_open('create_assess_report_items') ?>
                                        <input type="hidden" name="title" value="<?= $row['title'] ?>">
                                        <input type="hidden" name="code" value="<?= $row['code'] ?>">
                                        <input type="hidden" name="assex_id" value="<?= $assex['id'] ?>">
                                        <input type="hidden" name="report_groups_id" value="<?= $report_groups['id'] ?>">
                                        <input type="hidden" name="items_groups_id" value="<?= $row['id'] ?>">
                                        <input type="hidden" name="plan_id" value="<?= $plan['id'] ?>">
                                        <input type="hidden" name="id" value="<?= $row['id'] ?>">
                                        <button type="submit" class="btn btn-primary btn-sm btn-block">Import <i class=" fa fa-angle-right" aria-hidden="true"></i> </button>
                                        <?= form_close() ?>

                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success">
                    <i class="fa fa-info-circle" aria-hidden="true"></i> Imported Items in Group <b>[<?= $report_groups['code'] ?>]</b>
                </div>
                <div class="card-body p-0">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Title</th>
                                <th width="20%">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data as $row) : ?>
                                <tr>
                                    <td scope="row">

                                        <?= $row['code'] ?>
                                    </td>
                                    <td scope="row">

                                        <?= $row['title'] ?>
                                    </td>

                                    <td>
                                        <?= form_open('delete_assess_report_items') ?>
                                        <input type="hidden" name="id" value="<?= $row['id'] ?>">
                                        <button type="submit" class="btn btn-danger btn-sm btn-block"> <i class="fa fa-times-circle" aria-hidden="true"></i> Remove</button>
                                        <?= form_close() ?>


                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                </div>
                
            </div>
        </div>
        <!-- ./col -->
    </div>
    <!-- ./row -->

</section>

</body>


<?= $this->endSection() ?>