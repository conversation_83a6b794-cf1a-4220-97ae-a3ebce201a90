<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 fw-bold">Dashboard</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Dashboard</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Info boxes -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-3">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3><?= $activePlans ?: 0 ?></h3>
                        <p>Active Plans</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <a href="<?= base_url() ?>assessments_manager" class="small-box-footer">
                        View details <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- /.col -->
            <div class="col-12 col-sm-6 col-md-3">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?= $completedAssessments ?: 0 ?></h3>
                        <p>Completed Assessments</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <a href="<?= base_url() ?>assessments_manager" class="small-box-footer">
                        View details <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- /.col -->
            <div class="col-12 col-sm-6 col-md-3">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3><?= $pendingReviews ?: 0 ?></h3>
                        <p>Pending Reviews</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-hourglass-half"></i>
                    </div>
                    <a href="<?= base_url() ?>assessments_manager" class="small-box-footer">
                        View details <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- /.col -->
            <div class="col-12 col-sm-6 col-md-3">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3><?= $criticalItems ?: 0 ?></h3>
                        <p>Critical Score Items</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <a href="<?= base_url() ?>assessments_manager" class="small-box-footer">
                        View details <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->

        <!-- Main row -->
        <div class="row">
            <!-- Left col -->
            <div class="col-lg-7">
                <!-- Performance Metrics Chart -->
                <div class="card dashboard-chart mb-4">
                    <div class="card-header border-0">
                        <h3 class="card-title fw-bold">
                            <i class="fas fa-chart-line mr-1 text-primary"></i>
                            Performance Metrics
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-sm btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-tool dropdown-toggle" data-toggle="dropdown">
                                    <i class="fas fa-filter"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-right">
                                    <a href="#" class="dropdown-item">Last 30 days</a>
                                    <a href="#" class="dropdown-item">Last 90 days</a>
                                    <a href="#" class="dropdown-item">This year</a>
                                    <div class="dropdown-divider"></div>
                                    <a href="#" class="dropdown-item">Custom range</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-3">
                            <div class="d-flex flex-column">
                                <span class="text-success fw-bold fs-5"><?= end($completionRates) ?>%</span>
                                <span class="text-muted">Current Completion</span>
                            </div>
                            <div class="d-flex flex-column">
                                <span class="text-primary fw-bold fs-5"><?= end($targetRates) ?>%</span>
                                <span class="text-muted">Target Achievement</span>
                            </div>
                            <div class="d-flex flex-column">
                                <span class="text-info fw-bold fs-5"><?= $averageScore ?>/10</span>
                                <span class="text-muted">Average Score</span>
                            </div>
                        </div>
                        <div style="height: 240px">
                            <canvas id="performance-chart"></canvas>
                        </div>
                    </div>
                </div>
                <!-- /.card -->

                <!-- Recent Activity Table -->
                <div class="card">
                    <div class="card-header border-0">
                        <h3 class="card-title fw-bold">
                            <i class="fas fa-history mr-1 text-primary"></i>
                            Recent Activity
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-sm btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover table-striped">
                            <thead>
                                <tr>
                                    <th>Activity</th>
                                    <th>Title</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($recentActivity)): ?>
                                <tr>
                                    <td colspan="4" class="text-center">No recent activity found</td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($recentActivity as $activity): ?>
                                <tr>
                                    <td>
                                        <?php if (isset($activity['plan_id'])): ?>
                                            <i class="fas fa-clipboard-check text-primary me-1"></i> Exercise
                                        <?php else: ?>
                                            <i class="fas fa-file-alt text-info me-1"></i> Plan
                                        <?php endif; ?>
                                    </td>
                                    <td><?= esc($activity['title']) ?></td>
                                    <td>
                                        <?php if ($activity['status'] === 'active'): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php elseif ($activity['status'] === 'pending'): ?>
                                            <span class="badge bg-warning">Pending</span>
                                        <?php elseif ($activity['status'] === 'completed'): ?>
                                            <span class="badge bg-info">Completed</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><?= esc($activity['status']) ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= date('M d, Y', strtotime($activity['created_at'])) ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer text-center">
                        <a href="<?= base_url() ?>assessments_manager" class="text-primary">View All Activity</a>
                    </div>
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col-md-7 -->

            <!-- Right col -->
            <div class="col-lg-5">
                <!-- Assessment Distribution Chart -->
                <div class="card dashboard-chart mb-4">
                    <div class="card-header border-0">
                        <h3 class="card-title fw-bold">
                            <i class="fas fa-chart-pie mr-1 text-primary"></i>
                            Assessment Groups Distribution
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-sm btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body d-flex flex-column align-items-center">
                        <div style="height: 200px; width: 200px">
                            <canvas id="assessment-chart"></canvas>
                        </div>
                        <div class="mt-3 d-flex justify-content-center flex-wrap">
                            <?php foreach ($assessmentGroups as $index => $group): ?>
                                <?php if ($index < count($groupColors)): ?>
                                <div class="d-flex align-items-center me-3 mb-2">
                                    <div style="width: 15px; height: 15px; background-color: <?= $groupColors[$index] ?>;" class="rounded me-2"></div>
                                    <span class="text-sm"><?= esc($group['title']) ?> (<?= round(($group['count'] / $totalGroups) * 100) ?>%)</span>
                                </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                            <?php if (empty($assessmentGroups)): ?>
                            <div class="text-center text-muted">No assessment groups found</div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <!-- /.card -->

                <!-- Quick Task Card -->
                <div class="card mb-4">
                    <div class="card-header border-0">
                        <h3 class="card-title fw-bold">
                            <i class="fas fa-tasks mr-1 text-primary"></i>
                            Active Assessments
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($activeExercises)): ?>
                            <div class="text-center text-muted">No active assessments found</div>
                        <?php else: ?>
                            <?php foreach (array_slice($activeExercises, 0, 3) as $exercise): ?>
                            <div class="d-flex justify-content-between align-items-center border-bottom mb-3 pb-2">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-clipboard-check text-primary me-2"></i>
                                    <span><?= esc($exercise['title']) ?></span>
                                </div>
                                <a href="<?= base_url() ?>open_assess_exercise/<?= $exercise['id'] ?>" class="btn btn-sm btn-outline-primary">Score</a>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer text-center">
                        <a href="<?= base_url() ?>view_assess_exercises/all" class="text-primary">View All Assessments</a>
                    </div>
                </div>
                <!-- /.card -->

                <!-- Calendar Card -->
                <div class="card">
                    <div class="card-header border-0">
                        <h3 class="card-title fw-bold">
                            <i class="fas fa-calendar-alt mr-1 text-primary"></i>
                            Upcoming Deadlines
                        </h3>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <?php if (empty($upcomingExercises)): ?>
                                <div class="list-group-item text-center text-muted">No upcoming deadlines</div>
                            <?php else: ?>
                                <?php foreach ($upcomingExercises as $exercise): ?>
                                    <?php 
                                        $daysLeft = ceil((strtotime($exercise['date_to']) - time()) / (60 * 60 * 24));
                                        $badgeClass = 'bg-info';
                                        $timeText = '1 month left';
                                        
                                        if ($daysLeft <= 3) {
                                            $badgeClass = 'bg-danger';
                                            $timeText = $daysLeft . ' day' . ($daysLeft != 1 ? 's' : '') . ' left';
                                        } elseif ($daysLeft <= 14) {
                                            $badgeClass = 'bg-warning';
                                            $timeText = ceil($daysLeft/7) . ' week' . (ceil($daysLeft/7) != 1 ? 's' : '') . ' left';
                                        } elseif ($daysLeft <= 30) {
                                            $badgeClass = 'bg-info';
                                            $timeText = ceil($daysLeft/7) . ' weeks left';
                                        } else {
                                            $badgeClass = 'bg-secondary';
                                            $timeText = ceil($daysLeft/30) . ' month' . (ceil($daysLeft/30) != 1 ? 's' : '') . ' left';
                                        }
                                    ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1 fw-bold"><?= esc($exercise['title']) ?></h6>
                                            <small class="text-muted"><?= date('M d, Y', strtotime($exercise['date_to'])) ?></small>
                                        </div>
                                        <span class="badge <?= $badgeClass ?>"><?= $timeText ?></span>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-footer text-center">
                        <a href="<?= base_url() ?>view_assess_exercises/all" class="text-primary">View All Assessments</a>
                    </div>
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col-md-5 -->
        </div>
        <!-- /.row -->
    </div><!--/. container-fluid -->
</section>
<!-- /.content -->

<!-- Chart.js -->
<script src="<?= base_url() ?>public/assets/themes/adminlte320/plugins/chart.js/Chart.min.js"></script>

<script>
$(function() {
    // Performance Metrics Chart
    var performanceCtx = document.getElementById('performance-chart').getContext('2d');
    var performanceChart = new Chart(performanceCtx, {
        type: 'line',
        data: {
            labels: <?= json_encode($months) ?>,
            datasets: [{
                label: 'Completion Rate',
                data: <?= json_encode($completionRates) ?>,
                borderColor: '#0d6efd',
                backgroundColor: 'rgba(13, 110, 253, 0.1)',
                pointBackgroundColor: '#0d6efd',
                tension: 0.4,
                fill: true
            }, {
                label: 'Target Achievement',
                data: <?= json_encode($targetRates) ?>,
                borderColor: '#198754',
                backgroundColor: 'transparent',
                pointBackgroundColor: '#198754',
                tension: 0.4,
                borderDash: [5, 5]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    min: 0,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });

    // Assessment Distribution Chart
    var assessmentCtx = document.getElementById('assessment-chart').getContext('2d');
    var assessmentChart = new Chart(assessmentCtx, {
        type: 'doughnut',
        data: {
            labels: <?= json_encode($groupLabels) ?>,
            datasets: [{
                data: <?= json_encode($groupCounts) ?>,
                backgroundColor: <?= json_encode(array_slice($groupColors, 0, count($groupLabels))) ?>
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            cutout: '70%'
        }
    });
});
</script>

<?= $this->endSection() ?>