-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Apr 07, 2024 at 08:35 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `part_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `adx_country`
--

CREATE TABLE `adx_country` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(2) NOT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_country`
--

INSERT INTO `adx_country` (`id`, `name`, `code`, `created_at`) VALUES
(1, 'Papua New Guinea', 'PG', '2023-03-11 10:10:42'),
(2, 'Australia', 'AU', '2023-03-11 10:10:42');

-- --------------------------------------------------------

--
-- Table structure for table `adx_district`
--

CREATE TABLE `adx_district` (
  `id` int(11) NOT NULL,
  `districtcode` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_district`
--

INSERT INTO `adx_district` (`id`, `districtcode`, `name`, `country_id`, `province_id`) VALUES
(1, '', 'Abau', 1, 86),
(2, '', 'Goilala', 1, 86),
(3, '', 'Kairuku-Hiri', 1, 86),
(4, '', 'Rigo', 1, 86),
(5, '', 'Chimbu', 1, 87),
(6, '', 'Gumine', 1, 87),
(7, '', 'Kerowagi', 1, 87),
(8, '', 'Kundiawa-Gembogl', 1, 87),
(9, '', 'Sinasina-Yonggomugl', 1, 87),
(10, '', 'Goroka', 1, 88),
(11, '', 'Kainantu', 1, 88),
(12, '', 'Lufa', 1, 88),
(13, '', 'Obura-Wonenara', 1, 88),
(14, '', 'Okapa', 1, 88),
(15, '', 'Gazelle', 1, 89),
(16, '', 'Kokopo', 1, 89),
(17, '', 'Pomio', 1, 89),
(18, '', 'Rabaul', 1, 89),
(19, '1401', 'Ambunti-Dreikikir', 1, 90),
(20, '1402', 'Angoram', 1, 90),
(21, '1403', 'Maprik', 1, 90),
(22, '1404', 'Wewak', 1, 90),
(23, '1405', 'Yangoru-Saussia', 1, 90),
(24, '', 'Kompiam-Ambum', 1, 91),
(25, '', 'Laiagam-Porgera', 1, 91),
(26, '', 'Wabag', 1, 91),
(27, '', 'Kerema', 1, 92),
(28, '', 'Kikori', 1, 92),
(29, '', 'Kopiago', 1, 92),
(30, '', 'Lake Murray', 1, 92),
(31, '', 'Hela', 1, 93),
(32, '', 'Komo-Margarima', 1, 93),
(33, '', 'Koroba-Lake Kopiago', 1, 93),
(34, '', 'Tari-Pori', 1, 93),
(35, '', 'Anglimp-South Waghi', 1, 94),
(36, '', 'Banz', 1, 94),
(37, '', 'Jimi', 1, 94),
(38, '', 'North Waghi', 1, 94);

-- --------------------------------------------------------

--
-- Table structure for table `adx_llg`
--

CREATE TABLE `adx_llg` (
  `id` int(11) NOT NULL,
  `llgcode` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `adx_province`
--

CREATE TABLE `adx_province` (
  `id` int(11) NOT NULL,
  `provincecode` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_province`
--

INSERT INTO `adx_province` (`id`, `provincecode`, `name`, `country_id`) VALUES
(51, '', 'Australian Capital Territory', 2),
(52, '', 'New South Wales', 2),
(53, '', 'Northern Territory', 2),
(54, '', 'Queensland', 2),
(55, '', 'South Australia', 2),
(56, '', 'Tasmania', 2),
(57, '', 'Victoria', 2),
(58, '', 'Western Australia', 2),
(86, '03', 'Central Province', 1),
(87, '10', 'Chimbu Province', 1),
(88, '11', 'Eastern Highlands Province', 1),
(89, '18', 'East New Britain Province', 1),
(90, '14', 'East Sepik Province', 1),
(91, '08', 'Enga Province', 1),
(92, '02', 'Gulf Province', 1),
(93, '21', 'Hela Province', 1),
(94, '22', 'Jiwaka Province', 1),
(95, '13', 'Madang Province', 1),
(96, '16', 'Manus Province', 1),
(97, '05', 'Milne Bay Province', 1),
(98, '12', 'Morobe Province', 1),
(99, '17', 'New Ireland Province', 1),
(100, '06', 'Oro Province', 1),
(101, '07', 'Southern Highlands Province', 1),
(102, '01', 'Western Province', 1),
(103, '09', 'Western Highlands Province', 1),
(104, '19', 'West New Britain Province', 1),
(105, '20', 'AROB Bougainville', 1);

-- --------------------------------------------------------

--
-- Table structure for table `adx_ward`
--

CREATE TABLE `adx_ward` (
  `id` int(11) NOT NULL,
  `wardcode` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `llg_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `assess_exercise`
--

CREATE TABLE `assess_exercise` (
  `id` int(11) UNSIGNED NOT NULL,
  `ucode` varchar(200) NOT NULL,
  `orgcode` varchar(100) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `code` varchar(100) NOT NULL,
  `title` text NOT NULL,
  `date_from` date DEFAULT NULL,
  `date_to` date DEFAULT NULL,
  `bg_color` varchar(100) NOT NULL,
  `max_rate` varchar(11) NOT NULL,
  `min_rate` varchar(11) NOT NULL,
  `status` varchar(20) NOT NULL,
  `status_at` datetime DEFAULT NULL,
  `status_by` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `create_by` varchar(200) NOT NULL,
  `update_by` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `assess_exercise`
--

INSERT INTO `assess_exercise` (`id`, `ucode`, `orgcode`, `plan_id`, `code`, `title`, `date_from`, `date_to`, `bg_color`, `max_rate`, `min_rate`, `status`, `status_at`, `status_by`, `created_at`, `updated_at`, `create_by`, `update_by`) VALUES
(12, 'ASSEX66108cf7abee91712360695', '2345', 2, 'ASX23451', 'Cool Title', '2024-04-03', '2024-04-19', '', '10', '0', 'active', '2024-04-06 09:44:55', 'Minad', '2024-04-05 23:44:55', '2024-04-06 04:35:41', 'Minad', 'Minad'),
(14, 'ASSEX66108d272dd501712360743', '2345', 2, 'ASX23453', 'Test Event One', '2024-04-04', '2024-04-10', '', '0', '0', 'active', '2024-04-06 09:45:43', 'Minad', '2024-04-05 23:45:43', '2024-04-05 23:45:43', 'Minad', ''),
(15, 'ASSEX66108dae0cdd61712360878', '2345', 2, 'ASX23454', 'Cool Title', '2024-04-03', '2024-04-11', '', '0', '0', 'active', '2024-04-06 09:47:58', 'Minad', '2024-04-05 23:47:58', '2024-04-05 23:47:58', 'Minad', '');

-- --------------------------------------------------------

--
-- Table structure for table `assess_items`
--

CREATE TABLE `assess_items` (
  `id` int(11) UNSIGNED NOT NULL,
  `ucode` varchar(200) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `orgcode` varchar(100) NOT NULL,
  `assess_exercise_id` int(11) NOT NULL,
  `assess_items_groups_id` int(11) NOT NULL,
  `code` varchar(100) NOT NULL,
  `title` text NOT NULL,
  `bg_color` varchar(100) NOT NULL,
  `score` int(11) NOT NULL DEFAULT 0,
  `status` varchar(20) NOT NULL,
  `status_at` datetime DEFAULT NULL,
  `status_by` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `create_by` varchar(200) NOT NULL,
  `update_by` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `assess_items`
--

INSERT INTO `assess_items` (`id`, `ucode`, `plan_id`, `orgcode`, `assess_exercise_id`, `assess_items_groups_id`, `code`, `title`, `bg_color`, `score`, `status`, `status_at`, `status_by`, `created_at`, `updated_at`, `create_by`, `update_by`) VALUES
(1, 'ASSIT66109f572c5411712365399', 2, '2345', 12, 1, 'ST1', 'Land Mobilization', '', 1, 'active', '2024-04-06 11:03:19', 'Minad', '2024-04-06 01:03:19', '2024-04-06 03:22:15', 'Minad', 'Minad'),
(2, 'ASSIT66109f6adadc81712365418', 2, '2345', 12, 1, 'ST2', 'Land Records Management', '', 0, 'active', '2024-04-06 11:03:38', 'Minad', '2024-04-06 01:03:38', '2024-04-06 03:21:25', 'Minad', 'Minad'),
(3, 'ASSIT6610bbd88925d1712372696', 2, '2345', 12, 2, 'FR2', 'New REforestation', '', 3, 'active', '2024-04-06 13:04:56', 'Minad', '2024-04-06 03:04:56', '2024-04-06 04:50:25', 'Minad', 'Minad'),
(4, 'ASSIT6610bbeb132541712372715', 2, '2345', 12, 2, 'FR3', 'Wonderful REforestation efoort', '', 1, 'active', '2024-04-06 13:05:15', 'Minad', '2024-04-06 03:05:15', '2024-04-06 04:50:27', 'Minad', 'Minad'),
(5, 'ASSIT6610bbf7e78291712372727', 2, '2345', 12, 2, 'FR4', 'Cute Reforestaty', '', 4, 'active', '2024-04-06 13:05:27', 'Minad', '2024-04-06 03:05:27', '2024-04-06 04:36:08', 'Minad', 'Minad'),
(6, 'ASSIT6610d2ec90b531712378604', 2, '2345', 12, 3, 'Di1', 'This is one of the item 1', '', 1, 'active', '2024-04-06 14:43:24', 'Minad', '2024-04-06 04:43:24', '2024-04-06 04:43:41', 'Minad', 'Minad'),
(7, 'ASSIT6610d2ec93f2d1712378604', 2, '2345', 12, 3, 'Di2', 'This is one of the item 2', '', 3, 'active', '2024-04-06 14:43:24', 'Minad', '2024-04-06 04:43:24', '2024-04-06 04:43:49', 'Minad', 'Minad'),
(8, 'ASSIT6610d2ec958861712378604', 2, '2345', 12, 3, 'Di3', 'This is one of the item 3', '', 3, 'active', '2024-04-06 14:43:24', 'Minad', '2024-04-06 04:43:24', '2024-04-06 04:43:51', 'Minad', 'Minad'),
(9, 'ASSIT6610d2ec96f1c1712378604', 2, '2345', 12, 3, 'Di4', 'This is one of the item 4', '', 3, 'active', '2024-04-06 14:43:24', 'Minad', '2024-04-06 04:43:24', '2024-04-06 04:43:54', 'Minad', 'Minad'),
(10, 'ASSIT6610d2ec97b411712378604', 2, '2345', 12, 3, 'Di5', 'This is one of the item 5', '', 4, 'active', '2024-04-06 14:43:24', 'Minad', '2024-04-06 04:43:24', '2024-04-06 04:43:56', 'Minad', 'Minad'),
(11, 'ASSIT6610d2ec986061712378604', 2, '2345', 12, 3, 'Di6', 'This is one of the item 6', '', 2, 'active', '2024-04-06 14:43:24', 'Minad', '2024-04-06 04:43:24', '2024-04-06 04:44:22', 'Minad', 'Minad'),
(12, 'ASSIT6610d2ec990d11712378604', 2, '2345', 12, 3, 'Di7', 'This is one of the item 7', '', 3, 'active', '2024-04-06 14:43:24', 'Minad', '2024-04-06 04:43:24', '2024-04-06 04:45:00', 'Minad', 'Minad'),
(13, 'ASSIT6610d2ec9a22b1712378604', 2, '2345', 12, 3, 'Di8', 'This is one of the item 8', '', 4, 'active', '2024-04-06 14:43:24', 'Minad', '2024-04-06 04:43:24', '2024-04-06 04:45:02', 'Minad', 'Minad'),
(14, 'ASSIT6610d2ec9af571712378604', 2, '2345', 12, 3, 'Di9', 'This is one of the item 9', '', 6, 'active', '2024-04-06 14:43:24', 'Minad', '2024-04-06 04:43:24', '2024-04-06 04:45:04', 'Minad', 'Minad'),
(15, 'ASSIT6610d2ec9bc061712378604', 2, '2345', 12, 3, 'Di10', 'This is one of the item 10', '', 10, 'active', '2024-04-06 14:43:24', 'Minad', '2024-04-06 04:43:24', '2024-04-06 04:45:27', 'Minad', 'Minad');

-- --------------------------------------------------------

--
-- Table structure for table `assess_items_groups`
--

CREATE TABLE `assess_items_groups` (
  `id` int(11) UNSIGNED NOT NULL,
  `ucode` varchar(200) NOT NULL,
  `orgcode` varchar(100) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `assess_exercise_id` int(11) NOT NULL,
  `code` varchar(100) NOT NULL,
  `title` text NOT NULL,
  `bg_color` varchar(100) NOT NULL,
  `status` varchar(20) NOT NULL,
  `status_at` datetime DEFAULT NULL,
  `status_by` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `create_by` varchar(200) NOT NULL,
  `update_by` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `assess_items_groups`
--

INSERT INTO `assess_items_groups` (`id`, `ucode`, `orgcode`, `plan_id`, `assess_exercise_id`, `code`, `title`, `bg_color`, `status`, `status_at`, `status_by`, `created_at`, `updated_at`, `create_by`, `update_by`) VALUES
(1, 'IGRP66109f298e9e61712365353', '2345', 2, 12, 'SEC1', 'Lands', '', 'active', '2024-04-06 11:02:33', 'Minad', '2024-04-06 01:02:33', '2024-04-06 01:02:50', 'Minad', 'Minad'),
(2, 'IGRP66109f34732d91712365364', '2345', 2, 12, 'SEC2', 'Forestry', '', 'active', '2024-04-06 11:02:44', 'Minad', '2024-04-06 01:02:44', '2024-04-06 01:02:44', 'Minad', ''),
(3, 'IGRP6610d2769bad61712378486', '2345', 2, 12, 'SEC4', 'Commerce', '', 'active', '2024-04-06 14:41:26', 'Minad', '2024-04-06 04:41:26', '2024-04-06 04:41:26', 'Minad', '');

-- --------------------------------------------------------

--
-- Table structure for table `assess_plans`
--

CREATE TABLE `assess_plans` (
  `id` int(11) UNSIGNED NOT NULL,
  `ucode` varchar(200) NOT NULL,
  `orgcode` varchar(100) NOT NULL,
  `code` varchar(100) NOT NULL,
  `title` text NOT NULL,
  `date_from` date DEFAULT NULL,
  `date_to` date DEFAULT NULL,
  `bg_color` varchar(100) NOT NULL,
  `status` varchar(20) NOT NULL,
  `status_at` datetime DEFAULT NULL,
  `status_by` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `create_by` varchar(200) NOT NULL,
  `update_by` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `assess_plans`
--

INSERT INTO `assess_plans` (`id`, `ucode`, `orgcode`, `code`, `title`, `date_from`, `date_to`, `bg_color`, `status`, `status_at`, `status_by`, `created_at`, `updated_at`, `create_by`, `update_by`) VALUES
(2, 'ASPLN6610835fbfa491712358239', '2345', 'ESPIPDP2028', 'East Sepik Province Integrated Development Plan 2018 - 2028', '2018-12-12', '2028-12-12', '', 'active', '2024-04-06 09:03:59', 'Minad', '2024-04-05 23:03:59', '2024-04-05 23:03:59', 'Minad', '');

-- --------------------------------------------------------

--
-- Table structure for table `assess_report_groups`
--

CREATE TABLE `assess_report_groups` (
  `id` int(11) UNSIGNED NOT NULL,
  `ucode` varchar(200) NOT NULL,
  `orgcode` varchar(100) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `assess_exercise_id` int(11) NOT NULL,
  `code` varchar(100) NOT NULL,
  `title` text NOT NULL,
  `bg_color` varchar(100) NOT NULL,
  `chart_type` varchar(50) NOT NULL,
  `chart_operation` varchar(10) NOT NULL,
  `status` varchar(20) NOT NULL,
  `status_at` datetime DEFAULT NULL,
  `status_by` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `create_by` varchar(200) NOT NULL,
  `update_by` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `assess_report_groups`
--

INSERT INTO `assess_report_groups` (`id`, `ucode`, `orgcode`, `plan_id`, `assess_exercise_id`, `code`, `title`, `bg_color`, `chart_type`, `chart_operation`, `status`, `status_at`, `status_by`, `created_at`, `updated_at`, `create_by`, `update_by`) VALUES
(1, 'RGRP6610aeaeabe0e1712369326', '2345', 2, 12, 'SP1', 'Cooking Rick', '#e2caca', 'barChart', 'negative', 'active', '2024-04-06 12:08:46', 'Minad', '2024-04-06 02:08:46', '2024-04-06 03:14:11', 'Minad', 'Minad'),
(2, 'RGRP6610b51922b6b1712370969', '2345', 2, 12, 'Wan1', 'Wan Toolkit', '#28c394', 'radarChart', 'positive', 'active', '2024-04-06 12:36:09', 'Minad', '2024-04-06 02:36:09', '2024-04-06 03:16:14', 'Minad', 'Minad');

-- --------------------------------------------------------

--
-- Table structure for table `assess_report_items`
--

CREATE TABLE `assess_report_items` (
  `id` int(11) UNSIGNED NOT NULL,
  `ucode` varchar(200) NOT NULL,
  `orgcode` varchar(100) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `assess_exercise_id` int(11) NOT NULL,
  `assess_report_groups_id` int(11) NOT NULL,
  `assess_items_groups_id` varchar(100) NOT NULL,
  `code` varchar(20) NOT NULL,
  `title` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `create_by` varchar(200) NOT NULL,
  `update_by` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `assess_report_items`
--

INSERT INTO `assess_report_items` (`id`, `ucode`, `orgcode`, `plan_id`, `assess_exercise_id`, `assess_report_groups_id`, `assess_items_groups_id`, `code`, `title`, `created_at`, `updated_at`, `create_by`, `update_by`) VALUES
(1, 'ARI6610bc6f13d1a1712372847', '2345', 2, 12, 1, '1', 'SEC1', 'Lands', '2024-04-06 03:07:27', '2024-04-06 03:07:27', 'Minad', ''),
(2, 'ARI6610be2070f771712373280', '2345', 2, 12, 2, '2', 'SEC2', 'Forestry', '2024-04-06 03:14:40', '2024-04-06 03:14:40', 'Minad', '');

-- --------------------------------------------------------

--
-- Table structure for table `dakoii_org`
--

CREATE TABLE `dakoii_org` (
  `id` int(11) UNSIGNED NOT NULL,
  `orgcode` varchar(500) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `addlockprov` varchar(100) NOT NULL,
  `addlockcountry` varchar(100) NOT NULL,
  `orglogo` varchar(200) NOT NULL,
  `is_locationlocked` tinyint(1) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `dakoii_org`
--

INSERT INTO `dakoii_org` (`id`, `orgcode`, `name`, `description`, `addlockprov`, `addlockcountry`, `orglogo`, `is_locationlocked`, `is_active`, `created_at`, `updated_at`) VALUES
(2, '2345', 'Morobe Provincial Administration', 'This is the figure out organization', '12', 'PG', 'http://localhost/ocna/public/uploads/org_logo/2345_1681386431.gif', 0, 1, '2023-03-16 06:49:23', '2023-04-13 11:47:11'),
(3, '49501', 'Cooking', 'This is cooking descript', '', '', 'http://localhost/promis/public/uploads/org_logo/49501_1679908153.jpg', 0, 0, '2023-03-27 09:09:13', '2023-03-27 09:09:13'),
(4, '25492', 'Rico', 'Tekorif', '', '', 'http://localhost/promis/public/uploads/org_logo/25492_1679966568.png', 0, 0, '2023-03-27 09:15:40', '2023-03-28 01:22:48'),
(5, '16807', 'Activate', '', '', '', '', 0, 1, '2023-03-27 09:19:12', '2023-03-27 09:19:12'),
(6, '53874', 'Oepn Org', 'This Oepn thisdfnfsdj', '', '', 'http://localhost/promis/public/uploads/org_logo/53874_1679914956.jpg', 0, 1, '2023-03-27 09:23:18', '2023-03-27 11:02:36'),
(7, '45324', 'Demo 2 Org', '', '', '', '', 0, 1, '2023-04-26 10:07:25', '2023-04-26 10:07:25');

-- --------------------------------------------------------

--
-- Table structure for table `dakoii_users`
--

CREATE TABLE `dakoii_users` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `orgcode` varchar(500) NOT NULL,
  `role` enum('admin','moderator','user') NOT NULL DEFAULT 'user',
  `is_active` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `dakoii_users`
--

INSERT INTO `dakoii_users` (`id`, `name`, `username`, `password`, `orgcode`, `role`, `is_active`, `created_at`, `updated_at`) VALUES
(2, 'Free Kenny', 'fkenny', '$2y$10$A.8jXDJcv/wbzVi3l8bt/OPY6B0FpExgbUg.HOk6Khq9CYvKNQCyK', '', 'admin', 1, '2023-03-16 06:49:23', '2023-03-17 00:32:29');

-- --------------------------------------------------------

--
-- Table structure for table `selection`
--

CREATE TABLE `selection` (
  `id` int(11) NOT NULL,
  `box` varchar(20) NOT NULL,
  `value` varchar(200) NOT NULL,
  `item` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `selection`
--

INSERT INTO `selection` (`id`, `box`, `value`, `item`) VALUES
(19, 'eduqual', '11', 'phd'),
(20, 'eduqual', '12', 'masters'),
(21, 'eduqual', '13', 'post.grad.Diploma'),
(22, 'eduqual', '14', 'post.grad.Certificate'),
(23, 'eduqual', '15', 'undergrad (bachelors)'),
(24, 'eduqual', '16', 'advanced diploma'),
(25, 'eduqual', '17', 'diploma'),
(26, 'eduqual', '18', 'certificate 4'),
(27, 'eduqual', '19', 'trade certificate'),
(28, 'eduqual', '20', 'up.secondary (Gr.12)'),
(29, 'eduqual', '21', 'low.secondary (Gr.10)');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `value` varchar(200) NOT NULL,
  `name` varchar(200) NOT NULL,
  `create_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `value`, `name`, `create_at`) VALUES
(1, 'PG', 'country', '2023-03-11 13:50:34');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) UNSIGNED NOT NULL,
  `orgcode` varchar(100) NOT NULL,
  `name` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','moderator','user') NOT NULL DEFAULT 'user',
  `is_active` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `orgcode`, `name`, `username`, `password`, `role`, `is_active`, `created_at`, `updated_at`) VALUES
(6, '', 'Rc4 Admin', 'admin', '$2y$10$u2WThss1GKD/.cWwQl0Gt.q14X83Gz2jxJcFx1Hge5EZxTLpW6ObC', 'admin', 1, '2023-03-17 03:48:51', '2023-03-17 03:48:51'),
(7, '', 'Photo MAngi', 'foto', '$2y$10$lEV0JqkUUrdmkQ.B27ibB.g77Ai1AkEoj3Y2ZU89IAa1ZaUZ4paTu', 'admin', 0, '2023-03-20 09:21:32', '2023-03-20 09:21:32'),
(8, '', 'Cool Boy', 'cboy', '$2y$10$9834LWBkfvBXjYKWefjFy.zvwZkWtoiLLGKyHr/pxodMusR1GS6fq', 'user', 1, '2023-03-28 01:10:41', '2023-03-28 01:10:41'),
(9, '', 'Fr', 'dsdf', '$2y$10$wxtLnGNDwxbNDWHN/63Ck.G1LflkzHpf3130sHdLwOjE/3y8uGNGG', 'user', 0, '2023-03-28 01:14:11', '2023-03-28 01:14:11'),
(10, '', 'daefw', 'dada', '$2y$10$XxfirPWI8RkYNrqbHide7.i7MQ7HPVUQkEXqedDoLi8s1yq1JbK.G', 'user', 0, '2023-03-28 01:15:58', '2023-03-28 01:15:58'),
(11, '', 'dafedw', 'dadw', '$2y$10$QtyktCRSvsjPPleLhITOGe1ZOBzDjZ2rNhfTNqTbc5oYbwOlAmsQa', 'user', 0, '2023-03-28 01:16:44', '2023-03-28 01:16:44'),
(12, '', 'dasda', 'ssdad', '$2y$10$JPDktb1yW3UQ6T8kkSr0BOGyWhhpSjrLef5h4wLIc.gQr8n5FHYUi', 'user', 0, '2023-03-28 01:19:52', '2023-03-28 01:19:52'),
(13, '2345', 'dafewv', 'daef', '$2y$10$PQ.vKcORr8iGOzRl32qAPupBtr17LYb61nmJIHS8Hz8nlXWAcfr/.', 'user', 0, '2023-03-28 01:28:56', '2023-03-28 01:28:56'),
(14, '2345', 'Minad', 'minad', '$2y$10$Av6cBIw6EvO/h7439n0mfeiAIwlReLmnkncSU4LTj8Iqubc4p2nK2', 'admin', 1, '2023-03-28 01:32:20', '2023-03-28 01:32:20'),
(15, '45324', 'demo2', 'demo2', '$2y$10$FDvv4zA4CPnJqzCRpX1FXefuUWMn/RlBvlcsOnCw20zVyZrdXCT0K', 'admin', 1, '2023-04-26 10:07:45', '2023-04-26 10:07:45');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `adx_country`
--
ALTER TABLE `adx_country`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_district`
--
ALTER TABLE `adx_district`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_llg`
--
ALTER TABLE `adx_llg`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_province`
--
ALTER TABLE `adx_province`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_ward`
--
ALTER TABLE `adx_ward`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `assess_exercise`
--
ALTER TABLE `assess_exercise`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `assess_items`
--
ALTER TABLE `assess_items`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `assess_items_groups`
--
ALTER TABLE `assess_items_groups`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `assess_plans`
--
ALTER TABLE `assess_plans`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `assess_report_groups`
--
ALTER TABLE `assess_report_groups`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `assess_report_items`
--
ALTER TABLE `assess_report_items`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `dakoii_org`
--
ALTER TABLE `dakoii_org`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `dakoii_users`
--
ALTER TABLE `dakoii_users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `selection`
--
ALTER TABLE `selection`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `adx_country`
--
ALTER TABLE `adx_country`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `adx_district`
--
ALTER TABLE `adx_district`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=39;

--
-- AUTO_INCREMENT for table `adx_llg`
--
ALTER TABLE `adx_llg`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `adx_province`
--
ALTER TABLE `adx_province`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=106;

--
-- AUTO_INCREMENT for table `adx_ward`
--
ALTER TABLE `adx_ward`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `assess_exercise`
--
ALTER TABLE `assess_exercise`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `assess_items`
--
ALTER TABLE `assess_items`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `assess_items_groups`
--
ALTER TABLE `assess_items_groups`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `assess_plans`
--
ALTER TABLE `assess_plans`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `assess_report_groups`
--
ALTER TABLE `assess_report_groups`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `assess_report_items`
--
ALTER TABLE `assess_report_items`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `dakoii_org`
--
ALTER TABLE `dakoii_org`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `dakoii_users`
--
ALTER TABLE `dakoii_users`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `selection`
--
ALTER TABLE `selection`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
