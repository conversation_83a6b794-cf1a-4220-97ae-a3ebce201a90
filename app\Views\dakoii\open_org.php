<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<body>
    <div class="container-fluid p-2 ">
        <div class="row p-1" class="">
            <div class=" col-md-11">
                <a href="<?= base_url() ?>ddash" class="float-right"> <i class="fa fa-arrow-circle-left" aria-hidden="true"></i> Back</a>
            </div>
            <div class="col-md-12">
                <?php if (session()->has('error')) : ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle    "></i> <?= session('error') ?>
                    </div>
                <?php endif; ?>
                <?php if (session()->has('success')) : ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check"></i> <?= session('success') ?>
                    </div>
                <?php endif; ?>
            </div>

        </div>
        <div class="row p-1">
            <div class="col-md-8 ">
                <div class="card">
                    <div class="card-header bg-primary text-dark">
                        <h5 class=" float-left"><?= $org['name'] ?> <small class=" font-weight-lighter">(<?= $org['orgcode'] ?>)</small></h5>
                        <!-- Button trigger modal -->
                        <button type="button" class="btn btn-dark float-right" data-toggle="modal" data-target="#edit">
                            Edit
                        </button>
                        <!-- Modal -->
                        <div class="modal fade" id="edit" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header bg-primary">
                                        <h5 class="modal-title"> <i class="fa fa-edit" aria-hidden="true"></i> Edit Organization</h5>
                                        <button type="button" class="close text-dark" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <?= form_open_multipart('deditorg') ?>
                                    <div class="modal-body">

                                        <div class="form-group">
                                            <label for="name" class=" text-light">Organization Code:</label>
                                            <input type="text" class="form-control text-dark" id="name" name="orgcode" value="<?= $org['orgcode'] ?>" readonly>
                                        </div>

                                        <div class="form-group">
                                            <label for="name" class=" text-light">Organization Name:</label>
                                            <input type="text" class="form-control" id="name" name="name" value="<?= $org['name'] ?>" placeholder="Enter organization name" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="username" class=" text-light">Description:</label>
                                            <?= form_textarea('description', $org['description'], ['class' => 'form-control', 'placeholder' => 'Enter Description', 'value' => 'Default Description']) ?>
                                        </div>

                                        <div class="form-group">
                                            <label for="username" class=" text-light">Address Lock Country:</label>
                                            <select name="country" id="country" class="form-control">
                                                <option value="">Select Country</option>
                                                <?php
                                                if (!empty($org['addlockcountry'])) {
                                                ?>
                                                    <option selected value="<?= $set_country['code'] ?>"><?= $set_country['name'] ?></option>
                                                <?php
                                                } else {
                                                ?>
                                                    <option value="<?= $set_country['code'] ?>"><?= $set_country['name'] ?></option>
                                                <?php
                                                }
                                                ?>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="username" class=" text-light">Address Lock Province:</label>
                                            <select name="province" id="province" class="form-control">
                                                <option value="">Select Province</option>
                                                <?php
                                                if (!empty($org['addlockprov'])) {
                                                ?>
                                                    <?php foreach ($get_provinces as $prov) :
                                                        if ($org['addlockprov'] == $prov['provincecode']) :
                                                    ?>
                                                            <option selected value="<?= $prov['provincecode'] ?>"><?= $prov['name'] ?></option>
                                                    <?php
                                                        endif;
                                                    endforeach; ?>
                                                <?php
                                                }
                                                ?>
                                                <?php foreach ($get_provinces as $prov) :
                                                ?>
                                                    <option value="<?= $prov['provincecode'] ?>"><?= $prov['name'] ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="exampleInputFile" class=" text-light">Organization Logo</label>
                                            <div class="input-group">
                                                <div class="custom-file">
                                                    <input type="file" class="custom-file-input" name="org_logo" id="exampleInputFile" accept="image/*">
                                                    <label class="custom-file-label" for="exampleInputFile">Choose Logo</label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="username" class=" text-light">Status:</label>
                                            <select name="status" id="status" class="form-control">
                                                <option value="<?= $org['is_active'] ?>">
                                                    <?php
                                                    if ($org['is_active'] == 1) {
                                                        echo "Active";
                                                    } else {
                                                        echo "Deactive";
                                                    }
                                                    ?>
                                                </option>
                                                <option value="1">Active</option>
                                                <option value="0">Deactive</option>
                                            </select>
                                        </div>

                                    </div>
                                    <div class="modal-footer bg-info">
                                        <input type="hidden" name="id" value="<?= $org['id'] ?>">
                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                        <button type="submit" class="btn btn-dark float-right">Save Changes</button>
                                    </div>
                                    <?= form_close() ?>
                                </div>
                            </div>
                        </div>
                        <!-- /.modal -->


                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <img class="img-thumbnail" src="<?= imgcheck($org['orglogo']) ?>" width="300px" height="300px" alt="">
                            </div>
                            <div class="col-md-8">
                                <p><?= $org['description'] ?></p>
                            </div>
                        </div>
                        <div class="row">

                            <div class="col-md-6">
                                <h5 class="card-title">Address Lock Country</h5>
                                <label for=""><span><?= $org['addlockcountry'] ?></span></label>
                            </div>
                            <div class="col-md-6">
                                <h5 class="card-title">Address Lock Province</h5>
                                <label for=""><span><?= $org['addlockprov'] ?></span></label>
                            </div>
                        </div>

                    </div>
                    <div class="card-footer">
                        <b>Status:</b> <span class=" font-weight-light"><?= $org['is_active'] ?></span>
                    </div>

                </div>


            </div>
            <div class="col-md-4 ">

                <div class="card bg-info ">
                    <div class="card-header text-dark bg-primary">
                        <i class="fas fa-user-cog    "></i>
                        System Administrators
                        <!-- Button trigger modal -->
                        <button type="button" class="btn btn-dark float-right" data-toggle="modal" data-target="#sysadmin">
                            New System Admin
                        </button>


                        <!-- Modal -->
                        <div class="modal fade" id="sysadmin" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content bg-info">
                                    <div class="modal-header bg-primary">
                                        <h5 class="modal-title">Create System Admin</h5>
                                        <button type="button" class="close text-dark" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <?= form_open_multipart('daddadmin') ?>
                                    <div class="modal-body">

                                        <div class="form-group">
                                            <label for="name">Name:</label>
                                            <input type="text" class="form-control" id="name" name="name" placeholder="Enter your name" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="username">Username:</label>
                                            <input type="text" class="form-control" id="username" name="username" placeholder="Enter a username" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="password">Password:</label>
                                            <input type="password" class="form-control" id="password" name="password" placeholder="Enter a password" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="role">Role:</label>
                                            <select class="form-control" id="role" name="role" required>
                                                <option value="user" selected>User</option>
                                                <option value="moderator">Moderator</option>
                                                <option value="admin">Admin</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1">
                                                <label class="form-check-label" for="is_active">Active</label>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="modal-footer bg-dark">
                                        <input type="hidden" name="orgcode" value="<?= $org['orgcode'] ?>">
                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                        <button type="submit" class="btn btn-primary float-right">Create Admin</button>
                                    </div>
                                    <?= form_close() ?>
                                </div>
                            </div>
                        </div>
                        <!-- /.modal -->

                    </div>
                    <div class="card-body p-0">


                        <table class="table table-light">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Name</th>
                                    <th>Username</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($admins as $ur) : ?>
                                    <tr>
                                        <td>
                                            <?= $ur['name'] ?>
                                        </td>
                                        <td>
                                            <?= $ur['username'] ?>
                                        </td>
                                        <td>
                                            <?= $ur['role'] ?>
                                        </td>
                                        <td>
                                            <?= $ur['is_active'] ?>
                                        </td>
                                    </tr>
                                    
                                <?php endforeach; ?>
                            </tbody>

                        </table>


                    </div>

                </div>

            </div>

        </div>
    </div>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.slim.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
</body>



</html>
<?= $this->endSection() ?>