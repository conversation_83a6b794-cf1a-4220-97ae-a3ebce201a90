<?php

namespace App\Controllers;

use App\Models\countryModel;
use App\Models\dakoiiUsersModel;
use App\Models\orgModel;
use App\Models\provinceModel;
use App\Models\usersModel;

class <PERSON>koii extends BaseController
{
    public $session;
    public $dusersModel;
    public $usersModel;
    public $orgModel;
    public $countryModel;
    public $provinceModel;


    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->dusersModel = new dakoiiUsersModel();
        $this->usersModel = new usersModel();
        $this->orgModel = new orgModel();
        $this->countryModel = new countryModel();
        $this->provinceModel = new provinceModel();
    }

    public function index()
    {
        $data['title'] = "Dakoii Admin";
        $data['menu'] = "dlogin";



        echo view('dakoii/login', $data);
    }

    public function login()
    {
        // Check if form has been submitted
        if ($this->request->getMethod() == 'post') {



            // Validate form data
            $rules = [
                'username' => 'required',
                'password' => 'required'
            ];
            if (!$this->validate($rules)) {
                // Display login form with validation errors
                // return view('home/login', ['validation' => $this->validator]);
                $this->session->setTempdata('error', 'Rules Enter Correct Username and Password', 2);
                return redirect()->to(('dakoii'));
            }



            // Retrieve form data
            $username = $this->request->getVar('username');
            $password = $this->request->getVar('password');

            // Check if user exists

            $user = $this->dusersModel->where('username', $username)->first();
            if (!$user) {
                // Display login form with error message
                //return  redirect()->to(current_url());
                $this->session->setTempdata('error', 'Enter Correct Username', 2);
                return redirect()->to('dakoii');

                //return view('home/login', ['error' => 'Invalid username or password']);
            }

            // Check if password is correct
            if (!password_verify($password, $user['password'])) {
                // Display login form with error message
                //redirect()->to(current_url());
                $this->session->setTempdata('error', 'Enter Correct Password', 2);
                return redirect()->to('dakoii');
                //return view('home/login', ['error' => 'Invalid email or password']);
            }

            // Store user data in session
            $this->session->set('name', $user);
            $this->session->set('username', $user);
            $this->session->set('role', $user);

            // Redirect to dashboard
            $this->session->setTempdata('success', 'Login Successful', 2);
            return redirect()->to('ddash');
        } else {

            $this->session->setTempdata('error', 'Error Loging in', 2);
            return redirect()->to('dakoii');
        }
    }

    public function ddash()
    {
        $data['title'] = "DDash";
        $data['menu'] = "ddash";

        $data['dusers'] = $this->dusersModel->findAll();
        $data['admins'] = $this->usersModel->findAll();
        $data['org'] = $this->orgModel->findAll();

        echo view('dakoii/ddash', $data);
    }

    
    //add dakoii admin
    public function adduser()
    {

        //$orgcode = $this->request->getPost('orgcode');
        //form submit
        if ($this->request->getMethod() === 'post' && $this->validate([

            'username' => 'required|is_unique[dakoii_users.username]',
            'password' => 'required'
        ])) {

            $is_active = "0";
            if (!empty($this->request->getVar('is_active'))) {
                $is_active = $this->request->getVar('is_active');
            }

            

            $data = [
                'name' => $this->request->getVar('name'),
                'username' => $this->request->getVar('username'),
                'password' => password_hash($this->request->getVar('password'), PASSWORD_DEFAULT),
                'role' => $this->request->getVar('role'),
                'is_active' => $is_active,
            ];

            $this->dusersModel->insert($data);

            $this->session->setTempdata('success', 'Admin Created', 2);
            return redirect()->to('ddash');
            
        } else {
            $this->session->setTempdata('error', 'Username already exist', 2);
            return redirect()->to('ddash');
        }
    }

    //create org
    public function addorg()
    {

        //form submit
        if ($this->request->getMethod() === 'post' && $this->validate([

            'name' => 'required'
        ])) {

            $orgcode = rand(11111, 99999);
            if (!empty($this->orgModel->where('orgcode', $orgcode)->first())) {
                $orgcode = rand(11111, 99999);
            }


            $data = [
                'orgcode' => $orgcode,
                'name' => $this->request->getVar('name'),
                'description' => $this->request->getVar('description'),
                'is_active' => 1,
            ];

            $this->orgModel->insert($data);

            //======================== FILE UPLOAD=========================

            $logoFile = $this->request->getFile('org_logo');

            if ($logoFile->isValid() && $logoFile->getSize() > 0) {

                $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();

                $logoFile->move(ROOTPATH . 'public/uploads/org_logo/', $newName);

                $data['orglogo'] = base_url() . 'public/uploads/org_logo/' . $newName;

                $getid = $this->orgModel->where('orgcode', $orgcode)->first();
                $this->orgModel->update($getid['id'], $data);
            } elseif (empty($logoFile)) {
                $this->session->setTempdata('error', 'Invalid Logo File', 2);
            }

            $this->session->setTempdata('success', 'Organization Created', 2);
            return redirect()->to(base_url('dopen_org/' . $orgcode));
        } else {
            $this->session->setTempdata('success', 'Enter valid Data', 2);
            return redirect()->to('ddash');
        }
    }

    //view org
    public function open_org($orgcode)
    {
        $data['title'] = "Open Org";
        $data['menu'] = "openorg";

        $data['admins'] = $this->usersModel->where('orgcode', $orgcode)->find();
        $data['org'] = $this->orgModel->where('orgcode', $orgcode)->first();
        $data['set_country'] = $this->countryModel->where('code', COUNTRY_CODE)->first();
        $data['get_provinces'] = $this->provinceModel->where('country_id', $data['set_country']['id'])->orderBy('name', 'asc')->find();

        echo view('dakoii/open_org', $data);
    }

    //update org
    public function editorg()
    {

        //form submit
        if ($this->request->getMethod() === 'post' && $this->validate([

            'name' => 'required'
        ])) {

            $id = $this->request->getVar('id');
            $orgcode = $this->request->getVar('orgcode');

            $addprov = "";
            if (!empty($this->request->getVar('country'))) {
                $addprov = $this->request->getVar('province');
            }


            $data = [
                'name' => $this->request->getVar('name'),
                'description' => $this->request->getVar('description'),
                'addlockcountry' => $this->request->getVar('country'),
                'addlockprov' => $addprov,
                'is_active' => $this->request->getVar('status'),
            ];

            $this->orgModel->update($id, $data);

            //======================== FILE UPLOAD=========================

            $logoFile = $this->request->getFile('org_logo');

            if ($logoFile->isValid() && $logoFile->getSize() > 0) {

                $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();

                $logoFile->move(ROOTPATH . 'public/uploads/org_logo/', $newName);

                $data['orglogo'] = base_url() . 'public/uploads/org_logo/' . $newName;

                $getid = $this->orgModel->where('orgcode', $orgcode)->first();
                $this->orgModel->update($getid['id'], $data);
            } elseif (empty($logoFile)) {
                $this->session->setTempdata('error', 'Invalid Logo File', 2);
            }

            $this->session->setTempdata('success', 'Changes Saved', 2);
            return redirect()->to(base_url('dopen_org/' . $orgcode));
        } else {
            $this->session->setTempdata('success', 'Enter valid Data', 2);
            return redirect()->to('ddash');
        }
    }



    // --- Create Organization Admin
    public function create_admin()
    {

        $orgcode = $this->request->getVar('orgcode');
        //form submit
        if ($this->request->getMethod() === 'post' && $this->validate([

            'username' => 'required|is_unique[users.username]',
            'password' => 'required'

        ])) {

            $is_active = "0";
            if (!empty($this->request->getVar('is_active'))) {
                $is_active = $this->request->getVar('is_active');
            }

            $data = [
                'orgcode' => $this->request->getVar('orgcode'),
                'name' => $this->request->getVar('name'),
                'username' => $this->request->getVar('username'),
                'password' => password_hash($this->request->getVar('password'), PASSWORD_DEFAULT),
                'role' => $this->request->getVar('role'),
                'is_active' => $is_active,
            ];

            $this->usersModel->insert($data);
            $this->session->setTempdata('success', 'Organization Admin Created', 2);
            return redirect()->to('dopen_org/'.$orgcode);
        } else {
            $this->session->setTempdata('error', 'Username already taken', 2);
            return redirect()->to('dopen_org/'.$orgcode);
        }
    }




    public function logout()
    {
        // Destroy the user's session
        $session = session();
        $session->destroy();

        // Redirect to the login page
        return redirect()->to(base_url());
    }
}
