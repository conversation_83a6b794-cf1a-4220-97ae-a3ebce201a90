{"url": "http://localhost/part/index.php/view_assess_reports/ASSEX66108dae0cdd61712360878", "method": "GET", "isAJAX": false, "startTime": **********.422121, "totalTime": 168.2, "totalMemory": "7.590", "segmentDuration": 25, "segmentCount": 7, "CI_VERSION": "4.3.2", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.458398, "duration": 0.*****************}, {"name": "Routing", "component": "Timer", "start": **********.507381, "duration": 0.00041413307189941406}, {"name": "Before Filters", "component": "Timer", "start": **********.513988, "duration": 0.****************}, {"name": "Controller", "component": "Timer", "start": **********.532869, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.532872, "duration": 0.029601097106933594}, {"name": "After Filters", "component": "Timer", "start": **********.590311, "duration": 0.0006668567657470703}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(6 total Queries, 6 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.93 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `ucode` = &#039;ASSEX66108dae0cdd61712360878&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:268", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:656", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Assessments.php:784", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Assessments->view_assess_reports()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Assessments.php:784", "qid": "5a57659173006167aaaca25ea491c80d"}, {"hover": "", "class": "", "duration": "0.7 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_plans`\n<strong>WHERE</strong> `id` = &#039;2&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:268", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:656", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Assessments.php:785", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Assessments->view_assess_reports()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Assessments.php:785", "qid": "e12342d95703ebdf5a02416afa2d7afe"}, {"hover": "", "class": "", "duration": "0.58 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_report_items`\n<strong>WHERE</strong> `assess_exercise_id` = &#039;15&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:203", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:557", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Assessments.php:788", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Assessments->view_assess_reports()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Assessments.php:788", "qid": "04abd7d9e6b9169a5f90519360a18230"}, {"hover": "", "class": "", "duration": "0.59 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_report_groups`\n<strong>WHERE</strong> `id` <strong>IN</strong> (&#039;3&#039;,&#039;3&#039;,&#039;3&#039;,&#039;3&#039;)\n<strong>AND</strong> `assess_exercise_id` = &#039;15&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:203", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:557", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Assessments.php:795", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Assessments->view_assess_reports()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Assessments.php:795", "qid": "c64c42043925de585a92cdd1e35245d3"}, {"hover": "", "class": "", "duration": "0.81 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_items`\n<strong>WHERE</strong> `assess_exercise_id` = &#039;15&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:203", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:557", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Assessments.php:798", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Assessments->view_assess_reports()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Assessments.php:798", "qid": "b868f123312ea4cdb90a9b54d380696d"}, {"hover": "", "class": "", "duration": "0.76 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_items_groups`\n<strong>WHERE</strong> `id` <strong>IN</strong> (&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;23&#039;,&#039;23&#039;,&#039;23&#039;,&#039;23&#039;,&#039;23&#039;,&#039;23&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;)\n<strong>AND</strong> `assess_exercise_id` = &#039;15&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:203", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:557", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Assessments.php:808", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Assessments->view_assess_reports()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Assessments.php:808", "qid": "8fd0dfa4f2a7257b88971f3767b0ddbb"}]}, "badgeValue": 6, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.571141, "duration": "0.002813"}, {"name": "Query", "component": "Database", "start": **********.576039, "duration": "0.000930", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `ucode` = &#039;ASSEX66108dae0cdd61712360878&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.580659, "duration": "0.000702", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_plans`\n<strong>WHERE</strong> `id` = &#039;2&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.581746, "duration": "0.000580", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_report_items`\n<strong>WHERE</strong> `assess_exercise_id` = &#039;15&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;"}, {"name": "Query", "component": "Database", "start": **********.58263, "duration": "0.000587", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_report_groups`\n<strong>WHERE</strong> `id` <strong>IN</strong> (&#039;3&#039;,&#039;3&#039;,&#039;3&#039;,&#039;3&#039;)\n<strong>AND</strong> `assess_exercise_id` = &#039;15&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;"}, {"name": "Query", "component": "Database", "start": **********.583454, "duration": "0.000814", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_items`\n<strong>WHERE</strong> `assess_exercise_id` = &#039;15&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;"}, {"name": "Query", "component": "Database", "start": **********.584691, "duration": "0.000764", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_items_groups`\n<strong>WHERE</strong> `id` <strong>IN</strong> (&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;21&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;22&#039;,&#039;23&#039;,&#039;23&#039;,&#039;23&#039;,&#039;23&#039;,&#039;23&#039;,&#039;23&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;,&#039;61&#039;)\n<strong>AND</strong> `assess_exercise_id` = &#039;15&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "info", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: templates/adminlte/admindash.php", "component": "Views", "start": **********.587951, "duration": 0.0015251636505126953}, {"name": "View: assessments/view_assess_reports.php", "component": "Views", "start": **********.585982, "duration": 0.003979921340942383}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 169 )", "display": {"coreFiles": [{"name": "AutoRouterImproved.php", "path": "SYSTEMPATH\\Router\\AutoRouterImproved.php"}, {"name": "AutoRouterInterface.php", "path": "SYSTEMPATH\\Router\\AutoRouterInterface.php"}, {"name": "AutoloadConfig.php", "path": "SYSTEMPATH\\Config\\AutoloadConfig.php"}, {"name": "Autoloader.php", "path": "SYSTEMPATH\\Autoloader\\Autoloader.php"}, {"name": "BaseBuilder.php", "path": "SYSTEMPATH\\Database\\BaseBuilder.php"}, {"name": "BaseCollector.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php"}, {"name": "BaseConfig.php", "path": "SYSTEMPATH\\Config\\BaseConfig.php"}, {"name": "BaseConnection.php", "path": "SYSTEMPATH\\Database\\BaseConnection.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php"}, {"name": "BaseModel.php", "path": "SYSTEMPATH\\BaseModel.php"}, {"name": "BaseResult.php", "path": "SYSTEMPATH\\Database\\BaseResult.php"}, {"name": "BaseService.php", "path": "SYSTEMPATH\\Config\\BaseService.php"}, {"name": "Builder.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php"}, {"name": "CacheFactory.php", "path": "SYSTEMPATH\\Cache\\CacheFactory.php"}, {"name": "CacheInterface.php", "path": "SYSTEMPATH\\Cache\\CacheInterface.php"}, {"name": "CloneableCookieInterface.php", "path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php"}, {"name": "CodeIgniter.php", "path": "SYSTEMPATH\\CodeIgniter.php"}, {"name": "Common.php", "path": "SYSTEMPATH\\Common.php"}, {"name": "ConditionalTrait.php", "path": "SYSTEMPATH\\Traits\\ConditionalTrait.php"}, {"name": "Config.php", "path": "SYSTEMPATH\\Database\\Config.php"}, {"name": "Connection.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php"}, {"name": "ConnectionInterface.php", "path": "SYSTEMPATH\\Database\\ConnectionInterface.php"}, {"name": "ContentSecurityPolicy.php", "path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php"}, {"name": "Controller.php", "path": "SYSTEMPATH\\Controller.php"}, {"name": "Cookie.php", "path": "SYSTEMPATH\\Cookie\\Cookie.php"}, {"name": "CookieInterface.php", "path": "SYSTEMPATH\\Cookie\\CookieInterface.php"}, {"name": "CookieStore.php", "path": "SYSTEMPATH\\Cookie\\CookieStore.php"}, {"name": "Database.php", "path": "SYSTEMPATH\\Database\\Database.php"}, {"name": "Database.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php"}, {"name": "DebugToolbar.php", "path": "SYSTEMPATH\\Filters\\DebugToolbar.php"}, {"name": "DotEnv.php", "path": "SYSTEMPATH\\Config\\DotEnv.php"}, {"name": "Events.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php"}, {"name": "Events.php", "path": "SYSTEMPATH\\Events\\Events.php"}, {"name": "Exceptions.php", "path": "SYSTEMPATH\\Debug\\Exceptions.php"}, {"name": "Factories.php", "path": "SYSTEMPATH\\Config\\Factories.php"}, {"name": "Factory.php", "path": "SYSTEMPATH\\Config\\Factory.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php"}, {"name": "FileLocator.php", "path": "SYSTEMPATH\\Autoloader\\FileLocator.php"}, {"name": "Files.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php"}, {"name": "FilterInterface.php", "path": "SYSTEMPATH\\Filters\\FilterInterface.php"}, {"name": "Filters.php", "path": "SYSTEMPATH\\Filters\\Filters.php"}, {"name": "FormatRules.php", "path": "SYSTEMPATH\\Validation\\FormatRules.php"}, {"name": "HandlerInterface.php", "path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php"}, {"name": "Header.php", "path": "SYSTEMPATH\\HTTP\\Header.php"}, {"name": "IncomingRequest.php", "path": "SYSTEMPATH\\HTTP\\IncomingRequest.php"}, {"name": "Logger.php", "path": "SYSTEMPATH\\Log\\Logger.php"}, {"name": "Logs.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php"}, {"name": "Message.php", "path": "SYSTEMPATH\\HTTP\\Message.php"}, {"name": "MessageInterface.php", "path": "SYSTEMPATH\\HTTP\\MessageInterface.php"}, {"name": "MessageTrait.php", "path": "SYSTEMPATH\\HTTP\\MessageTrait.php"}, {"name": "Model.php", "path": "SYSTEMPATH\\Model.php"}, {"name": "Modules.php", "path": "SYSTEMPATH\\Modules\\Modules.php"}, {"name": "OutgoingRequest.php", "path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php"}, {"name": "OutgoingRequestInterface.php", "path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php"}, {"name": "Query.php", "path": "SYSTEMPATH\\Database\\Query.php"}, {"name": "QueryInterface.php", "path": "SYSTEMPATH\\Database\\QueryInterface.php"}, {"name": "RendererInterface.php", "path": "SYSTEMPATH\\View\\RendererInterface.php"}, {"name": "Request.php", "path": "SYSTEMPATH\\HTTP\\Request.php"}, {"name": "RequestInterface.php", "path": "SYSTEMPATH\\HTTP\\RequestInterface.php"}, {"name": "RequestTrait.php", "path": "SYSTEMPATH\\HTTP\\RequestTrait.php"}, {"name": "Response.php", "path": "SYSTEMPATH\\HTTP\\Response.php"}, {"name": "ResponseInterface.php", "path": "SYSTEMPATH\\HTTP\\ResponseInterface.php"}, {"name": "ResponseTrait.php", "path": "SYSTEMPATH\\API\\ResponseTrait.php"}, {"name": "ResponseTrait.php", "path": "SYSTEMPATH\\HTTP\\ResponseTrait.php"}, {"name": "Result.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Result.php"}, {"name": "ResultInterface.php", "path": "SYSTEMPATH\\Database\\ResultInterface.php"}, {"name": "RouteCollection.php", "path": "SYSTEMPATH\\Router\\RouteCollection.php"}, {"name": "RouteCollectionInterface.php", "path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php"}, {"name": "Router.php", "path": "SYSTEMPATH\\Router\\Router.php"}, {"name": "RouterInterface.php", "path": "SYSTEMPATH\\Router\\RouterInterface.php"}, {"name": "Routes.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php"}, {"name": "Services.php", "path": "SYSTEMPATH\\Config\\Services.php"}, {"name": "Session.php", "path": "SYSTEMPATH\\Session\\Session.php"}, {"name": "SessionInterface.php", "path": "SYSTEMPATH\\Session\\SessionInterface.php"}, {"name": "Time.php", "path": "SYSTEMPATH\\I18n\\Time.php"}, {"name": "TimeTrait.php", "path": "SYSTEMPATH\\I18n\\TimeTrait.php"}, {"name": "Timer.php", "path": "SYSTEMPATH\\Debug\\Timer.php"}, {"name": "Timers.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php"}, {"name": "Toolbar.php", "path": "SYSTEMPATH\\Debug\\Toolbar.php"}, {"name": "URI.php", "path": "SYSTEMPATH\\HTTP\\URI.php"}, {"name": "UserAgent.php", "path": "SYSTEMPATH\\HTTP\\UserAgent.php"}, {"name": "Validation.php", "path": "SYSTEMPATH\\Validation\\Validation.php"}, {"name": "ValidationInterface.php", "path": "SYSTEMPATH\\Validation\\ValidationInterface.php"}, {"name": "View.php", "path": "SYSTEMPATH\\Config\\View.php"}, {"name": "View.php", "path": "SYSTEMPATH\\View\\View.php"}, {"name": "ViewDecoratorTrait.php", "path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php"}, {"name": "Views.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php"}, {"name": "array_helper.php", "path": "SYSTEMPATH\\Helpers\\array_helper.php"}, {"name": "bootstrap.php", "path": "SYSTEMPATH\\bootstrap.php"}, {"name": "form_helper.php", "path": "SYSTEMPATH\\Helpers\\form_helper.php"}, {"name": "kint_helper.php", "path": "SYSTEMPATH\\Helpers\\kint_helper.php"}, {"name": "url_helper.php", "path": "SYSTEMPATH\\Helpers\\url_helper.php"}], "userFiles": [{"name": "AbstractRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\AbstractRenderer.php"}, {"name": "App.php", "path": "APPPATH\\Config\\App.php"}, {"name": "Assessments.php", "path": "APPPATH\\Controllers\\Assessments.php"}, {"name": "Auth.php", "path": "APPPATH\\Filters\\Auth.php"}, {"name": "Autoload.php", "path": "APPPATH\\Config\\Autoload.php"}, {"name": "BaseController.php", "path": "APPPATH\\Controllers\\BaseController.php"}, {"name": "Cache.php", "path": "APPPATH\\Config\\Cache.php"}, {"name": "ClassLoader.php", "path": "FCPATH\\vendor\\composer\\ClassLoader.php"}, {"name": "CliRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\CliRenderer.php"}, {"name": "Common.php", "path": "APPPATH\\Common.php"}, {"name": "Constants.php", "path": "APPPATH\\Config\\Constants.php"}, {"name": "ContentSecurityPolicy.php", "path": "APPPATH\\Config\\ContentSecurityPolicy.php"}, {"name": "Cookie.php", "path": "APPPATH\\Config\\Cookie.php"}, {"name": "Database.php", "path": "APPPATH\\Config\\Database.php"}, {"name": "Escaper.php", "path": "FCPATH\\vendor\\laminas\\laminas-escaper\\src\\Escaper.php"}, {"name": "Events.php", "path": "APPPATH\\Config\\Events.php"}, {"name": "Exceptions.php", "path": "APPPATH\\Config\\Exceptions.php"}, {"name": "FacadeInterface.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\FacadeInterface.php"}, {"name": "Feature.php", "path": "APPPATH\\Config\\Feature.php"}, {"name": "Filters.php", "path": "APPPATH\\Config\\Filters.php"}, {"name": "Functions.php", "path": "FCPATH\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php"}, {"name": "InstalledVersions.php", "path": "FCPATH\\vendor\\composer\\InstalledVersions.php"}, {"name": "Kint.php", "path": "APPPATH\\Config\\Kint.php"}, {"name": "Kint.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Kint.php"}, {"name": "LogLevel.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LogLevel.php"}, {"name": "Logger.php", "path": "APPPATH\\Config\\Logger.php"}, {"name": "LoggerAwareTrait.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LoggerAwareTrait.php"}, {"name": "LoggerInterface.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LoggerInterface.php"}, {"name": "Modules.php", "path": "APPPATH\\Config\\Modules.php"}, {"name": "Paths.php", "path": "APPPATH\\Config\\Paths.php"}, {"name": "RendererInterface.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\RendererInterface.php"}, {"name": "RichRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\RichRenderer.php"}, {"name": "Routes.php", "path": "APPPATH\\Config\\Routes.php"}, {"name": "Services.php", "path": "APPPATH\\Config\\Services.php"}, {"name": "Session.php", "path": "APPPATH\\Config\\Session.php"}, {"name": "TextRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\TextRenderer.php"}, {"name": "Toolbar.php", "path": "APPPATH\\Config\\Toolbar.php"}, {"name": "UserAgents.php", "path": "APPPATH\\Config\\UserAgents.php"}, {"name": "Utils.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Utils.php"}, {"name": "Validation.php", "path": "APPPATH\\Config\\Validation.php"}, {"name": "View.php", "path": "APPPATH\\Config\\View.php"}, {"name": "admindash.php", "path": "APPPATH\\Views\\templates\\adminlte\\admindash.php"}, {"name": "assess_ExercisesModel.php", "path": "APPPATH\\Models\\assess_ExercisesModel.php"}, {"name": "assess_PlansModel.php", "path": "APPPATH\\Models\\assess_PlansModel.php"}, {"name": "assess_itemsModel.php", "path": "APPPATH\\Models\\assess_itemsModel.php"}, {"name": "assess_items_GroupsModel.php", "path": "APPPATH\\Models\\assess_items_GroupsModel.php"}, {"name": "assess_report_GroupsModel.php", "path": "APPPATH\\Models\\assess_report_GroupsModel.php"}, {"name": "assess_report_itemsModel.php", "path": "APPPATH\\Models\\assess_report_itemsModel.php"}, {"name": "autoload.php", "path": "FCPATH\\vendor\\autoload.php"}, {"name": "autoload_real.php", "path": "FCPATH\\vendor\\composer\\autoload_real.php"}, {"name": "autoload_static.php", "path": "FCPATH\\vendor\\composer\\autoload_static.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-ctype\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-mbstring\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-php80\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-php81\\bootstrap.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-ctype\\bootstrap80.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap80.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-mbstring\\bootstrap80.php"}, {"name": "chart_helper.php", "path": "APPPATH\\Helpers\\chart_helper.php"}, {"name": "deep_copy.php", "path": "FCPATH\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php"}, {"name": "development.php", "path": "APPPATH\\Config\\Boot\\development.php"}, {"name": "function.php", "path": "FCPATH\\vendor\\symfony\\deprecation-contracts\\function.php"}, {"name": "functions.php", "path": "FCPATH\\vendor\\symfony\\string\\Resources\\functions.php"}, {"name": "index.php", "path": "FCPATH\\index.php"}, {"name": "info_helper.php", "path": "APPPATH\\Helpers\\info_helper.php"}, {"name": "init.php", "path": "FCPATH\\vendor\\kint-php\\kint\\init.php"}, {"name": "init_helpers.php", "path": "FCPATH\\vendor\\kint-php\\kint\\init_helpers.php"}, {"name": "installed.php", "path": "FCPATH\\vendor\\composer\\installed.php"}, {"name": "platform_check.php", "path": "FCPATH\\vendor\\composer\\platform_check.php"}, {"name": "usersModel.php", "path": "APPPATH\\Models\\usersModel.php"}, {"name": "view_assess_reports.php", "path": "APPPATH\\Views\\assessments\\view_assess_reports.php"}]}, "badgeValue": 169, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Assessments", "method": "view_assess_reports", "paramCount": 1, "truePCount": 1, "params": [{"name": "$ucode = ", "value": "ASSEX66108dae0cdd61712360878"}]}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Home::login"}, {"method": "GET", "route": "logout", "handler": "\\App\\Controllers\\Home::logout"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Home::about"}, {"method": "GET", "route": "findme/(.*)", "handler": "\\App\\Controllers\\Home::findme/$1"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\Admindash::index"}, {"method": "GET", "route": "employees", "handler": "\\App\\Controllers\\Employees::index"}, {"method": "GET", "route": "edit_employees/(.*)", "handler": "\\App\\Controllers\\Employees::edit_employees/$1"}, {"method": "GET", "route": "profile_employees/(.*)", "handler": "\\App\\Controllers\\Employees::profile_employees/$1"}, {"method": "GET", "route": "emp_dashboard", "handler": "\\App\\Controllers\\Portal::index"}, {"method": "GET", "route": "emp_open_activity/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_activity/$1"}, {"method": "GET", "route": "emp_open_skillcomp/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skillcomp/$1"}, {"method": "GET", "route": "emp_open_feedbacks/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_feedbacks/$1"}, {"method": "GET", "route": "emp_open_feedback_groups/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_feedback_groups/$1"}, {"method": "GET", "route": "emp_open_skills_comp/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skills_comp/$1"}, {"method": "GET", "route": "emp_open_skills_comp_groups/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skills_comp_groups/$1"}, {"method": "GET", "route": "emp_open_skills_comp_employees/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skills_comp_employees/$1"}, {"method": "GET", "route": "plans_manager", "handler": "\\App\\Controllers\\Plans::index"}, {"method": "GET", "route": "view_plans/(.*)", "handler": "\\App\\Controllers\\Plans::view_plans/$1"}, {"method": "GET", "route": "align_plans/(.*)", "handler": "\\App\\Controllers\\Plans::align_plans/$1"}, {"method": "GET", "route": "view_groups/(.*)", "handler": "\\App\\Controllers\\Plans::view_groups/$1"}, {"method": "GET", "route": "view_strategies/(.*)", "handler": "\\App\\Controllers\\Plans::view_strategies/$1"}, {"method": "GET", "route": "view_programs/(.*)", "handler": "\\App\\Controllers\\Plans::view_programs/$1"}, {"method": "GET", "route": "assessments_manager", "handler": "\\App\\Controllers\\Assessments::index"}, {"method": "GET", "route": "view_assess_exercises/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_exercises/$1"}, {"method": "GET", "route": "open_assess_exercise/(.*)", "handler": "\\App\\Controllers\\Assessments::open_assess_exercise/$1"}, {"method": "GET", "route": "view_assess_items_groups/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_items_groups/$1"}, {"method": "GET", "route": "view_assess_items/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_items/$1"}, {"method": "GET", "route": "open_assess_score_group/(.*)", "handler": "\\App\\Controllers\\Assessments::open_assess_score_group/$1"}, {"method": "GET", "route": "open_assess_score_items/(.*)", "handler": "\\App\\Controllers\\Assessments::open_assess_score_items/$1"}, {"method": "GET", "route": "view_assess_report_groupings/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_report_groupings/$1"}, {"method": "GET", "route": "view_assess_report_items/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_report_items/$1"}, {"method": "GET", "route": "view_assess_reports/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_reports/$1"}, {"method": "GET", "route": "view_assess_reports_summary_tables/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_reports_summary_tables/$1"}, {"method": "GET", "route": "view_assess_reports_raw_scores/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_reports_raw_scores/$1"}, {"method": "GET", "route": "activities", "handler": "\\App\\Controllers\\Activities::index"}, {"method": "GET", "route": "new_activity", "handler": "\\App\\Controllers\\Activities::new_activity"}, {"method": "GET", "route": "open_activity/(.*)", "handler": "\\App\\Controllers\\Activities::open_activity/$1"}, {"method": "GET", "route": "open_qualitative_unpack/(.*)", "handler": "\\App\\Controllers\\Activities::open_qualitative_unpack/$1"}, {"method": "GET", "route": "open_qualitative_item/(.*)", "handler": "\\App\\Controllers\\Activities::open_qualitative_item/$1"}, {"method": "GET", "route": "reports_qualitative_dashboard/(.*)", "handler": "\\App\\Controllers\\Reports::reports_qualitative_dashboard/$1"}, {"method": "GET", "route": "da<PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::index"}, {"method": "GET", "route": "dlogout", "handler": "\\App\\Controllers\\Dakoii::logout"}, {"method": "GET", "route": "ddash", "handler": "\\App\\Controllers\\Dakoii::ddash"}, {"method": "GET", "route": "dopen_org/(.*)", "handler": "\\App\\Controllers\\Dakoii::open_org/$1"}, {"method": "GET", "route": "dlist_org", "handler": "\\App\\Controllers\\Dakoii::list_org"}, {"method": "GET", "route": "testa", "handler": "\\App\\Controllers\\Test::index"}, {"method": "GET", "route": "ajax", "handler": "\\App\\Controllers\\Test::ajax"}, {"method": "GET", "route": "ajaxform", "handler": "\\App\\Controllers\\Test::ajaxform"}, {"method": "GET", "route": "testmap", "handler": "\\App\\Controllers\\Test::testmap"}, {"method": "POST", "route": "login", "handler": "\\App\\Controllers\\Home::login"}, {"method": "POST", "route": "dologin", "handler": "\\App\\Controllers\\Home::dologin"}, {"method": "POST", "route": "gofindme", "handler": "\\App\\Controllers\\Home::gofindme"}, {"method": "POST", "route": "open_profile", "handler": "\\App\\Controllers\\Home::open_profile"}, {"method": "POST", "route": "create_employees", "handler": "\\App\\Controllers\\Employees::create_employees"}, {"method": "POST", "route": "update_employees", "handler": "\\App\\Controllers\\Employees::update_employees"}, {"method": "POST", "route": "create_plans", "handler": "\\App\\Controllers\\Plans::create_plans"}, {"method": "POST", "route": "update_plans", "handler": "\\App\\Controllers\\Plans::update_plans"}, {"method": "POST", "route": "delete_plans", "handler": "\\App\\Controllers\\Plans::delete_plans"}, {"method": "POST", "route": "create_plan_groups", "handler": "\\App\\Controllers\\Plans::create_plan_groups"}, {"method": "POST", "route": "update_plan_groups", "handler": "\\App\\Controllers\\Plans::update_plan_groups"}, {"method": "POST", "route": "delete_plan_groups", "handler": "\\App\\Controllers\\Plans::delete_plan_groups"}, {"method": "POST", "route": "create_strategies", "handler": "\\App\\Controllers\\Plans::create_strategies"}, {"method": "POST", "route": "update_strategies", "handler": "\\App\\Controllers\\Plans::update_strategies"}, {"method": "POST", "route": "delete_strategies", "handler": "\\App\\Controllers\\Plans::delete_strategies"}, {"method": "POST", "route": "create_programs", "handler": "\\App\\Controllers\\Plans::create_programs"}, {"method": "POST", "route": "update_programs", "handler": "\\App\\Controllers\\Plans::update_programs"}, {"method": "POST", "route": "delete_programs", "handler": "\\App\\Controllers\\Plans::delete_programs"}, {"method": "POST", "route": "create_pro_act", "handler": "\\App\\Controllers\\Plans::create_pro_act"}, {"method": "POST", "route": "update_pro_act", "handler": "\\App\\Controllers\\Plans::update_pro_act"}, {"method": "POST", "route": "create_assess_plan", "handler": "\\App\\Controllers\\Assessments::create_assess_plan"}, {"method": "POST", "route": "update_assess_plan", "handler": "\\App\\Controllers\\Assessments::update_assess_plan"}, {"method": "POST", "route": "delete_assess_plan", "handler": "\\App\\Controllers\\Assessments::delete_assess_plan"}, {"method": "POST", "route": "create_assessment_exercise", "handler": "\\App\\Controllers\\Assessments::create_assessment_exercise"}, {"method": "POST", "route": "update_assessment_exercise", "handler": "\\App\\Controllers\\Assessments::update_assessment_exercise"}, {"method": "POST", "route": "delete_assessment_exercise", "handler": "\\App\\Controllers\\Assessments::delete_assessment_exercise"}, {"method": "POST", "route": "create_assess_items_group", "handler": "\\App\\Controllers\\Assessments::create_assess_items_group"}, {"method": "POST", "route": "update_assess_items_group", "handler": "\\App\\Controllers\\Assessments::update_assess_items_group"}, {"method": "POST", "route": "delete_assess_items_group", "handler": "\\App\\Controllers\\Assessments::delete_assess_items_group"}, {"method": "POST", "route": "import_assess_items_groups", "handler": "\\App\\Controllers\\Assessments::import_assess_items_groups"}, {"method": "POST", "route": "create_assess_report_group", "handler": "\\App\\Controllers\\Assessments::create_assess_report_group"}, {"method": "POST", "route": "update_assess_report_group", "handler": "\\App\\Controllers\\Assessments::update_assess_report_group"}, {"method": "POST", "route": "delete_assess_report_group", "handler": "\\App\\Controllers\\Assessments::delete_assess_report_group"}, {"method": "POST", "route": "import_assess_items", "handler": "\\App\\Controllers\\Assessments::import_assess_items"}, {"method": "POST", "route": "create_assess_items", "handler": "\\App\\Controllers\\Assessments::create_assess_items"}, {"method": "POST", "route": "update_assess_items", "handler": "\\App\\Controllers\\Assessments::update_assess_items"}, {"method": "POST", "route": "delete_assess_items", "handler": "\\App\\Controllers\\Assessments::delete_assess_items"}, {"method": "POST", "route": "create_assess_report_items", "handler": "\\App\\Controllers\\Assessments::create_assess_report_items"}, {"method": "POST", "route": "delete_assess_report_items", "handler": "\\App\\Controllers\\Assessments::delete_assess_report_items"}, {"method": "POST", "route": "enter_score_items", "handler": "\\App\\Controllers\\Assessments::enter_score_items"}, {"method": "POST", "route": "update_target_rates", "handler": "\\App\\Controllers\\Assessments::update_target_rates"}, {"method": "POST", "route": "import_qualitative_data", "handler": "\\App\\Controllers\\Activities::import_qualitative_data"}, {"method": "POST", "route": "create_qualitative_item", "handler": "\\App\\Controllers\\Activities::create_qualitative_item"}, {"method": "POST", "route": "update_qualitative_item", "handler": "\\App\\Controllers\\Activities::update_qualitative_item"}, {"method": "POST", "route": "delete_qualitative_item", "handler": "\\App\\Controllers\\Activities::delete_qualitative_item"}, {"method": "POST", "route": "bg_qualitative_color", "handler": "\\App\\Controllers\\Activities::bg_qualitative_color"}, {"method": "POST", "route": "qualitative_for_score", "handler": "\\App\\Controllers\\Activities::qualitative_for_score"}, {"method": "POST", "route": "download_qualitative_data", "handler": "\\App\\Controllers\\Activities::download_qualitative_data"}, {"method": "POST", "route": "import_qualitative_score", "handler": "\\App\\Controllers\\Activities::import_qualitative_score"}, {"method": "POST", "route": "create_activity", "handler": "\\App\\Controllers\\Activities::create_activity"}, {"method": "POST", "route": "update_activity", "handler": "\\App\\Controllers\\Activities::update_activity"}, {"method": "POST", "route": "update_activity_status", "handler": "\\App\\Controllers\\Activities::update_activity_status"}, {"method": "POST", "route": "dlogin", "handler": "\\App\\Controllers\\Dakoii::login"}, {"method": "POST", "route": "dad<PERSON>g", "handler": "\\App\\Controllers\\Dakoii::addorg"}, {"method": "POST", "route": "deditorg", "handler": "\\App\\Controllers\\Dakoii::editorg"}, {"method": "POST", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::adduser"}, {"method": "POST", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::create_admin"}, {"method": "POST", "route": "ajax", "handler": "\\App\\Controllers\\Test::ajax"}, {"method": "POST", "route": "ajaxform", "handler": "\\App\\Controllers\\Test::ajaxform"}]}, "badgeValue": 50, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "8.57", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.26", "count": 6}}}, "badgeValue": 7, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.462621, "duration": 0.008572101593017578}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.576981, "duration": 6.4849853515625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.581369, "duration": 5.602836608886719e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.582332, "duration": 3.314018249511719e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.583222, "duration": 3.314018249511719e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.584275, "duration": 3.409385681152344e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.585461, "duration": 3.910064697265625e-05}]}], "vars": {"varData": {"View Data": {"title": "Report Dashboard", "menu": "dashboard", "assex": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (18)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSEX66108dae0cdd61712360878\"<div class=\"access-path\">$value['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"ASX23454\"<div class=\"access-path\">$value['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (22) \"ESPIPDP Review Scoring\"<div class=\"access-path\">$value['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_from</dfn> =&gt; <var>string</var> (10) \"2024-04-03\"<div class=\"access-path\">$value['date_from']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_to</dfn> =&gt; <var>string</var> (10) \"2024-04-11\"<div class=\"access-path\">$value['date_to']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>max_rate</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value['max_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>min_rate</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value['min_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:47:58\"<div class=\"access-path\">$value['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:47:58\"<div class=\"access-path\">$value['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:29:05\"<div class=\"access-path\">$value['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value['update_by']</div></dt></dl></dd></dl></div>", "plan": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (15)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASPLN6610835fbfa491712358239\"<div class=\"access-path\">$value['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (11) \"ESPIPDP2028\"<div class=\"access-path\">$value['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (59) \"East Sepik Province Integrated Development Plan 2018 - 2028\"<div class=\"access-path\">$value['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_from</dfn> =&gt; <var>string</var> (10) \"2018-12-12\"<div class=\"access-path\">$value['date_from']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_to</dfn> =&gt; <var>string</var> (10) \"2028-12-12\"<div class=\"access-path\">$value['date_to']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:03:59\"<div class=\"access-path\">$value['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:03:59\"<div class=\"access-path\">$value['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-04-15 12:36:41\"<div class=\"access-path\">$value['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value['update_by']</div></dt></dl></dd></dl></div>", "report_items": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (4)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (4)</li><li>Contents (4)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>ucode</th><th>orgcode</th><th>plan_id</th><th>assess_exercise_id</th><th>assess_report_groups_id</th><th>assess_items_groups_id</th><th>code</th><th>title</th><th>created_at</th><th>updated_at</th><th>create_by</th><th>update_by</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">8</td><td title=\"string (26)\">ARI6829c644957441747568196</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (1)\">3</td><td title=\"string (2)\">21</td><td title=\"string (5)\">5.1.2</td><td title=\"string (26)\"> Agriculture and Livestock</td><td title=\"string (19)\">2025-05-18 21:36:36</td><td title=\"string (19)\">2025-05-18 21:36:36</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>1</th><td title=\"string (1)\">9</td><td title=\"string (26)\">ARI6829c64819a761747568200</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (1)\">3</td><td title=\"string (2)\">23</td><td title=\"string (5)\">5.1.4</td><td title=\"string (8)\"> Tourism</td><td title=\"string (19)\">2025-05-18 21:36:40</td><td title=\"string (19)\">2025-05-18 21:36:40</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>2</th><td title=\"string (2)\">10</td><td title=\"string (26)\">ARI6829c64c323a01747568204</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (1)\">3</td><td title=\"string (2)\">26</td><td title=\"string (5)\">5.1.7</td><td title=\"string (7)\"> Mining</td><td title=\"string (19)\">2025-05-18 21:36:44</td><td title=\"string (19)\">2025-05-18 21:36:44</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>3</th><td title=\"string (2)\">11</td><td title=\"string (26)\">ARI6829c748a82c21747568456</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (1)\">3</td><td title=\"string (2)\">61</td><td title=\"string (8)\">Sector 6</td><td title=\"string (25)\">Agriculture and lifestock</td><td title=\"string (19)\">2025-05-18 21:40:56</td><td title=\"string (19)\">2025-05-18 21:40:56</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (26) \"ARI6829c644957441747568196\"<div class=\"access-path\">$value[0]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[0]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[0]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_report_groups_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[0]['assess_report_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[0]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.2\"<div class=\"access-path\">$value[0]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (26) \" Agriculture and Livestock\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:36:36\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:36:36\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (26) \"ARI6829c64819a761747568200\"<div class=\"access-path\">$value[1]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[1]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[1]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_report_groups_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[1]['assess_report_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[1]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.4\"<div class=\"access-path\">$value[1]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (8) \" Tourism\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:36:40\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:36:40\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (26) \"ARI6829c64c323a01747568204\"<div class=\"access-path\">$value[2]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[2]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[2]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_report_groups_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['assess_report_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"26\"<div class=\"access-path\">$value[2]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.7\"<div class=\"access-path\">$value[2]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (7) \" Mining\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:36:44\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:36:44\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (26) \"ARI6829c748a82c21747568456\"<div class=\"access-path\">$value[3]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[3]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[3]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[3]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_report_groups_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[3]['assess_report_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[3]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"Sector 6\"<div class=\"access-path\">$value[3]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (25) \"Agriculture and lifestock\"<div class=\"access-path\">$value[3]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:40:56\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:40:56\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['update_by']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "report_groups": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"RGRP6829c62d236c91747568173\"<div class=\"access-path\">$value[0]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[0]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[0]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (4) \"DIP2\"<div class=\"access-path\">$value[0]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (13) \"Cooking Smite\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>bg_color</dfn> =&gt; <var>string</var> (7) \"#000000\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 0, 0, 1)\"></div></div><div class=\"access-path\">$value[0]['bg_color']</div></dt><dd><pre><dfn>black</dfn>\n<dfn>#000</dfn>\n<dfn>#000000</dfn>\n<dfn>rgb(0, 0, 0)</dfn>\n<dfn>hsl(0, 0%, 0%)</dfn>\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>chart_type</dfn> =&gt; <var>string</var> (8) \"barChart\"<div class=\"access-path\">$value[0]['chart_type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>chart_operation</dfn> =&gt; <var>string</var> (8) \"negative\"<div class=\"access-path\">$value[0]['chart_operation']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:36:13\"<div class=\"access-path\">$value[0]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:36:13\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:36:13\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['update_by']</div></dt></dl></dd></dl></dd></dl></div>", "items": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (36)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (36)</li><li>Contents (36)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>ucode</th><th>plan_id</th><th>orgcode</th><th>assess_exercise_id</th><th>assess_items_groups_id</th><th>code</th><th>title</th><th>bg_color</th><th>score</th><th>status</th><th>status_at</th><th>status_by</th><th>created_at</th><th>updated_at</th><th>create_by</th><th>update_by</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">16</td><td title=\"string (28)\">ASSIT6612967c659d31712494204</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">21</td><td title=\"string (6)\">S1.2.1</td><td title=\"string (45)\">Development of a Provincial Agriculture Plan.</td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (19)\">2024-07-30 12:17:27</td><td title=\"string (5)\">Minad</td><td title=\"string (5)\">Minad</td></tr><tr><th>1</th><td title=\"string (2)\">17</td><td title=\"string (28)\">ASSIT6612967c672ec1712494204</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">21</td><td title=\"string (6)\">S1.2.2</td><td title=\"string (51)\">Establishment of relevant Spice &amp; Commodity Boards.</td><td title=\"string (0)\"></td><td title=\"string (1)\">2</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (19)\">2024-07-30 12:17:29</td><td title=\"string (5)\">Minad</td><td title=\"string (5)\">Minad</td></tr><tr><th>2</th><td title=\"string (2)\">18</td><td title=\"string (28)\">ASSIT6612967c685221712494204</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">21</td><td title=\"string (6)\">S1.2.3</td><td title=\"string (117)\">Invite foreign investors to develop large scale rice farming in the Sepik pla...</td><td title=\"string (0)\"></td><td title=\"string (1)\">5</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (19)\">2024-07-30 12:17:32</td><td title=\"string (5)\">Minad</td><td title=\"string (5)\">Minad</td></tr><tr><th>3</th><td title=\"string (2)\">19</td><td title=\"string (28)\">ASSIT6612967c69b241712494204</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">21</td><td title=\"string (6)\">S1.2.4</td><td title=\"string (141)\">Promote cash Crop, Livestock and Wildlife diversification farming and value a...</td><td title=\"string (0)\"></td><td title=\"string (1)\">6</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (19)\">2024-07-30 12:17:34</td><td title=\"string (5)\">Minad</td><td title=\"string (5)\">Minad</td></tr><tr><th>4</th><td title=\"string (2)\">20</td><td title=\"string (28)\">ASSIT6612967c6a6841712494204</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">21</td><td title=\"string (6)\">S1.2.5</td><td title=\"string (87)\">Improve subsistence farming for self-sufficiency, food security and nutrition...</td><td title=\"string (0)\"></td><td title=\"string (1)\">7</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (19)\">2024-07-30 12:17:35</td><td title=\"string (5)\">Minad</td><td title=\"string (5)\">Minad</td></tr><tr><th>5</th><td title=\"string (2)\">21</td><td title=\"string (28)\">ASSIT6612967c6afe31712494204</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">21</td><td title=\"string (6)\">S1.2.6</td><td title=\"string (103)\">Strengthen farmer organizations as agriculture co-operatives, Women in Agricu...</td><td title=\"string (0)\"></td><td title=\"string (1)\">8</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (19)\">2024-07-30 12:17:36</td><td title=\"string (5)\">Minad</td><td title=\"string (5)\">Minad</td></tr><tr><th>6</th><td title=\"string (2)\">22</td><td title=\"string (28)\">ASSIT6612967c6b9a21712494204</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">21</td><td title=\"string (6)\">S1.2.7</td><td title=\"string (87)\">Land acquisition/Land mobilisation and efficient land management for commerci...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>7</th><td title=\"string (2)\">23</td><td title=\"string (28)\">ASSIT6612967c6c2f11712494204</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">21</td><td title=\"string (6)\">S1.2.8</td><td title=\"string (91)\">Develop characteristic estate Agri-business in each District for domestic and...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>8</th><td title=\"string (2)\">24</td><td title=\"string (28)\">ASSIT6612967c6d6531712494204</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">21</td><td title=\"string (6)\">S1.2.9</td><td title=\"string (66)\">Import and adapt technology to increase agricultural productivity.</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>9</th><td title=\"string (2)\">25</td><td title=\"string (28)\">ASSIT6612967c6e2331712494204</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">21</td><td title=\"string (7)\">S1.2.10</td><td title=\"string (100)\">Develop a Provincial Bio-security Response Plan (PBSRP) to protect all the ag...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (19)\">2024-04-07 22:50:04</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>10</th><td title=\"string (2)\">26</td><td title=\"string (28)\">ASSIT661297125d4f31712494354</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">22</td><td title=\"string (8)\">S5.1.3.1</td><td title=\"string (205)\">Develop and manage the Fisheries Sector in the districts and the province aim...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>11</th><td title=\"string (2)\">27</td><td title=\"string (28)\">ASSIT661297125eb911712494354</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">22</td><td title=\"string (8)\">S5.1.3.2</td><td title=\"string (101)\">Develop a Provincial Fisheries Policy and enforce the Fisheries Regulatory fr...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>12</th><td title=\"string (2)\">28</td><td title=\"string (28)\">ASSIT661297125f6541712494354</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">22</td><td title=\"string (8)\">S5.1.3.3</td><td title=\"string (84)\">Development of a strategy to enable small scale farming to access external ma...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>13</th><td title=\"string (2)\">29</td><td title=\"string (28)\">ASSIT661297126084a1712494354</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">22</td><td title=\"string (8)\">S5.1.3.4</td><td title=\"string (98)\">Establishment and operationalization of the East Sepik Fisheries Association ...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>14</th><td title=\"string (2)\">30</td><td title=\"string (28)\">ASSIT6612971261d961712494354</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">22</td><td title=\"string (8)\">S5.1.3.5</td><td title=\"string (72)\">Develop a strategy to encourage the involvement of women in the sector. </td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>15</th><td title=\"string (2)\">31</td><td title=\"string (28)\">ASSIT6612971262a301712494354</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">22</td><td title=\"string (8)\">S5.1.3.6</td><td title=\"UTF-8 string (116)\">Improve Extension, training and Advisory Support to SME&#8217;s and communities inv...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>16</th><td title=\"string (2)\">32</td><td title=\"string (28)\">ASSIT66129712634941712494354</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">22</td><td title=\"string (8)\">S5.1.3.7</td><td title=\"string (83)\">Undertake Fisheries resource survey to update management data base for the se...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>17</th><td title=\"string (2)\">33</td><td title=\"string (28)\">ASSIT66129712646611712494354</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">22</td><td title=\"string (8)\">S5.1.3.8</td><td title=\"string (80)\">Provide adequate hatchery for fingerlings rearing and distribution to farmers.  </td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>18</th><td title=\"string (2)\">34</td><td title=\"string (28)\">ASSIT661297126554e1712494354</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">22</td><td title=\"string (8)\">S5.1.3.9</td><td title=\"string (84)\">Reposition &amp; make accessible to local fishermen Fisheries Aggregation Device ...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>19</th><td title=\"string (2)\">35</td><td title=\"string (28)\">ASSIT661297126604a1712494354</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">22</td><td title=\"string (9)\">S5.1.3.10</td><td title=\"UTF-8 string (139)\">Adequately resource the division of Fisheries through (budgetary, staffing, h...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (19)\">2024-04-07 22:52:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>20</th><td title=\"string (2)\">36</td><td title=\"string (28)\">ASSIT6612986bdd8891712494699</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">23</td><td title=\"string (8)\">S5.1.4.1</td><td title=\"string (47)\"> Strengthen and promote research &amp; development.</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>21</th><td title=\"string (2)\">37</td><td title=\"string (28)\">ASSIT6612986bde9a71712494699</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">23</td><td title=\"string (8)\">S5.1.4.2</td><td title=\"string (49)\"> Improve and strengthen human resources capacity.</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>22</th><td title=\"string (2)\">38</td><td title=\"string (28)\">ASSIT6612986bdf76a1712494699</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">23</td><td title=\"string (8)\">S5.1.4.3</td><td title=\"string (55)\"> Encourage local landowner participation &amp; involvement.</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>23</th><td title=\"string (2)\">39</td><td title=\"string (28)\">ASSIT6612986be0d461712494699</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">23</td><td title=\"string (8)\">S5.1.4.4</td><td title=\"string (38)\"> Market the province as a Destination.</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>24</th><td title=\"string (2)\">40</td><td title=\"string (28)\">ASSIT6612986be18351712494699</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">23</td><td title=\"string (8)\">S5.1.4.5</td><td title=\"string (55)\"> Establish Provincial Tourism Bureaus and Associations.</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>25</th><td title=\"string (2)\">41</td><td title=\"string (28)\">ASSIT6612986be2e511712494699</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">23</td><td title=\"string (8)\">S5.1.4.6</td><td title=\"string (138)\"> Develop and implement a Tourism Standards framework for products such as foo...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (19)\">2024-04-07 22:58:19</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>26</th><td title=\"string (4)\">3935</td><td title=\"string (28)\">ASSIT6829c3dd2abad1747567581</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">61</td><td title=\"string (6)\">S1.6.1</td><td title=\"string (48)\">1. Development of a Provincial Agriculture Plan.</td><td title=\"string (0)\"></td><td title=\"string (1)\">2</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (19)\">2025-05-18 21:29:35</td><td title=\"string (5)\">Minad</td><td title=\"string (5)\">Minad</td></tr><tr><th>27</th><td title=\"string (4)\">3936</td><td title=\"string (28)\">ASSIT6829c3dd2c5ab1747567581</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">61</td><td title=\"string (6)\">S1.6.2</td><td title=\"string (54)\">2. Establishment of relevant Spice &amp; Commodity Boards.</td><td title=\"string (0)\"></td><td title=\"string (1)\">4</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (19)\">2025-05-18 21:28:04</td><td title=\"string (5)\">Minad</td><td title=\"string (5)\">Minad</td></tr><tr><th>28</th><td title=\"string (4)\">3937</td><td title=\"string (28)\">ASSIT6829c3dd2d30e1747567581</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">61</td><td title=\"string (6)\">S1.6.3</td><td title=\"string (120)\">3. Invite foreign investors to develop large scale rice farming in the Sepik ...</td><td title=\"string (0)\"></td><td title=\"string (1)\">2</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (19)\">2025-05-18 21:28:12</td><td title=\"string (5)\">Minad</td><td title=\"string (5)\">Minad</td></tr><tr><th>29</th><td title=\"string (4)\">3938</td><td title=\"string (28)\">ASSIT6829c3dd2dfed1747567581</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">61</td><td title=\"string (6)\">S1.6.4</td><td title=\"string (144)\">4. Promote cash Crop, Livestock and Wildlife diversification farming and valu...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>30</th><td title=\"string (4)\">3939</td><td title=\"string (28)\">ASSIT6829c3dd2ec371747567581</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">61</td><td title=\"string (6)\">S1.6.5</td><td title=\"string (90)\">5. Improve subsistence farming for self-sufficiency, food security and nutrit...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>31</th><td title=\"string (4)\">3940</td><td title=\"string (28)\">ASSIT6829c3dd2fae21747567581</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">61</td><td title=\"string (6)\">S1.6.6</td><td title=\"string (106)\">6. Strengthen farmer organizations as agriculture co-operatives, Women in Agr...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>32</th><td title=\"string (4)\">3941</td><td title=\"string (28)\">ASSIT6829c3dd3074f1747567581</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">61</td><td title=\"string (6)\">S1.6.7</td><td title=\"string (90)\">7. Land acquisition/Land mobilisation and efficient land management for comme...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>33</th><td title=\"string (4)\">3942</td><td title=\"string (28)\">ASSIT6829c3dd3201a1747567581</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">61</td><td title=\"string (6)\">S1.6.8</td><td title=\"string (94)\">8. Develop characteristic estate Agri-business in each District for domestic ...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>34</th><td title=\"string (4)\">3943</td><td title=\"string (28)\">ASSIT6829c3dd332561747567581</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">61</td><td title=\"string (6)\">S1.6.9</td><td title=\"string (69)\">9. Import and adapt technology to increase agricultural productivity.</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>35</th><td title=\"string (4)\">3944</td><td title=\"string (28)\">ASSIT6829c3dd340a91747567581</td><td title=\"string (1)\">2</td><td title=\"string (4)\">2345</td><td title=\"string (2)\">15</td><td title=\"string (2)\">61</td><td title=\"string (7)\">S1.6.10</td><td title=\"string (104)\">10. Develop a Provincial Bio-security Response Plan (PBSRP) to protect all th...</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (19)\">2025-05-18 21:26:21</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612967c659d31712494204\"<div class=\"access-path\">$value[0]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[0]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[0]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[0]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[0]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.2.1\"<div class=\"access-path\">$value[0]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (45) \"Development of a Provincial Agriculture Plan.\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[0]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-07-30 12:17:27\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612967c672ec1712494204\"<div class=\"access-path\">$value[1]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[1]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[1]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[1]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[1]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.2.2\"<div class=\"access-path\">$value[1]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (51) \"Establishment of relevant Spice &amp; Commodity Boards.\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[1]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-07-30 12:17:29\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612967c685221712494204\"<div class=\"access-path\">$value[2]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[2]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[2]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[2]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[2]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.2.3\"<div class=\"access-path\">$value[2]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (117) \"Invite foreign investors to develop large scale rice farming in the Sepik pl...<div class=\"access-path\">$value[2]['title']</div></dt><dd><pre>Invite foreign investors to develop large scale rice farming in the Sepik plains for export and domestic consumption.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[2]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[2]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-07-30 12:17:32\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"19\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612967c69b241712494204\"<div class=\"access-path\">$value[3]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[3]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[3]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[3]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[3]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[3]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.2.4\"<div class=\"access-path\">$value[3]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (141) \"Promote cash Crop, Livestock and Wildlife diversification farming and value ...<div class=\"access-path\">$value[3]['title']</div></dt><dd><pre>Promote cash Crop, Livestock and Wildlife diversification farming and value added market orientated farming for export and reduce in imports.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[3]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[3]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-07-30 12:17:34\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"20\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612967c6a6841712494204\"<div class=\"access-path\">$value[4]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[4]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[4]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[4]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[4]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[4]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.2.5\"<div class=\"access-path\">$value[4]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (87) \"Improve subsistence farming for self-sufficiency, food security and nutritio...<div class=\"access-path\">$value[4]['title']</div></dt><dd><pre>Improve subsistence farming for self-sufficiency, food security and nutritional status.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[4]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[4]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-07-30 12:17:35\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612967c6afe31712494204\"<div class=\"access-path\">$value[5]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[5]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[5]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[5]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[5]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[5]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.2.6\"<div class=\"access-path\">$value[5]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (103) \"Strengthen farmer organizations as agriculture co-operatives, Women in Agric...<div class=\"access-path\">$value[5]['title']</div></dt><dd><pre>Strengthen farmer organizations as agriculture co-operatives, Women in Agriculture and Land Owner ILGs.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[5]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[5]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[5]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-07-30 12:17:36\"<div class=\"access-path\">$value[5]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612967c6b9a21712494204\"<div class=\"access-path\">$value[6]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[6]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[6]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[6]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[6]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[6]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.2.7\"<div class=\"access-path\">$value[6]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (87) \"Land acquisition/Land mobilisation and efficient land management for commerc...<div class=\"access-path\">$value[6]['title']</div></dt><dd><pre>Land acquisition/Land mobilisation and efficient land management for commercial farming\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[6]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[6]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[6]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[6]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[6]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[7]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612967c6c2f11712494204\"<div class=\"access-path\">$value[7]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[7]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[7]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[7]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[7]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[7]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.2.8\"<div class=\"access-path\">$value[7]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (91) \"Develop characteristic estate Agri-business in each District for domestic an...<div class=\"access-path\">$value[7]['title']</div></dt><dd><pre>Develop characteristic estate Agri-business in each District for domestic and export market\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[7]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[7]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[7]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[7]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[7]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[7]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[7]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"24\"<div class=\"access-path\">$value[8]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612967c6d6531712494204\"<div class=\"access-path\">$value[8]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[8]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[8]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[8]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[8]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[8]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.2.9\"<div class=\"access-path\">$value[8]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (66) \"Import and adapt technology to increase agricultural productivity.\"<div class=\"access-path\">$value[8]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[8]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[8]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[8]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[8]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[8]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[8]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[8]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"25\"<div class=\"access-path\">$value[9]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612967c6e2331712494204\"<div class=\"access-path\">$value[9]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[9]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[9]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[9]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[9]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[9]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (7) \"S1.2.10\"<div class=\"access-path\">$value[9]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (100) \"Develop a Provincial Bio-security Response Plan (PBSRP) to protect all the a...<div class=\"access-path\">$value[9]['title']</div></dt><dd><pre>Develop a Provincial Bio-security Response Plan (PBSRP) to protect all the agroindustry commodities.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[9]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[9]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[9]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[9]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[9]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:50:04\"<div class=\"access-path\">$value[9]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[9]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>10</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[10]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"26\"<div class=\"access-path\">$value[10]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT661297125d4f31712494354\"<div class=\"access-path\">$value[10]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[10]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[10]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[10]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[10]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[10]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.3.1\"<div class=\"access-path\">$value[10]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (205) \"Develop and manage the Fisheries Sector in the districts and the province ai...<div class=\"access-path\">$value[10]['title']</div></dt><dd><pre>Develop and manage the Fisheries Sector in the districts and the province aimed at encouraging, and incentivizing local fishermen and aqua in land fish farmers and fishermen to be engaged in the sector.   \n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[10]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[10]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[10]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[10]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[10]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[10]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[10]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>11</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[11]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"27\"<div class=\"access-path\">$value[11]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT661297125eb911712494354\"<div class=\"access-path\">$value[11]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[11]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[11]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[11]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[11]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[11]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.3.2\"<div class=\"access-path\">$value[11]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (101) \"Develop a Provincial Fisheries Policy and enforce the Fisheries Regulatory f...<div class=\"access-path\">$value[11]['title']</div></dt><dd><pre>Develop a Provincial Fisheries Policy and enforce the Fisheries Regulatory framework for the province\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[11]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[11]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[11]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[11]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[11]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[11]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[11]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>12</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[12]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[12]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT661297125f6541712494354\"<div class=\"access-path\">$value[12]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[12]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[12]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[12]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[12]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[12]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.3.3\"<div class=\"access-path\">$value[12]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (84) \"Development of a strategy to enable small scale farming to access external m...<div class=\"access-path\">$value[12]['title']</div></dt><dd><pre>Development of a strategy to enable small scale farming to access external markets. \n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[12]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[12]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[12]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[12]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[12]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[12]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[12]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>13</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[13]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[13]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT661297126084a1712494354\"<div class=\"access-path\">$value[13]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[13]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[13]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[13]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[13]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[13]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.3.4\"<div class=\"access-path\">$value[13]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (98) \"Establishment and operationalization of the East Sepik Fisheries Association...<div class=\"access-path\">$value[13]['title']</div></dt><dd><pre>Establishment and operationalization of the East Sepik Fisheries Association Cooperative Limited. \n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[13]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[13]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[13]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[13]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[13]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[13]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[13]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[13]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[13]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>14</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[14]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[14]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612971261d961712494354\"<div class=\"access-path\">$value[14]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[14]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[14]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[14]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[14]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[14]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.3.5\"<div class=\"access-path\">$value[14]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (72) \"Develop a strategy to encourage the involvement of women in the sector. \"<div class=\"access-path\">$value[14]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[14]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[14]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[14]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[14]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[14]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[14]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[14]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[14]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[14]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>15</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[15]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"31\"<div class=\"access-path\">$value[15]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612971262a301712494354\"<div class=\"access-path\">$value[15]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[15]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[15]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[15]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[15]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[15]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.3.6\"<div class=\"access-path\">$value[15]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (116) \"Improve Extension, training and Advisory Support to SME&#8217;s and communities in...<div class=\"access-path\">$value[15]['title']</div></dt><dd><pre>Improve Extension, training and Advisory Support to SME&#8217;s and communities involved in fisheries in all districts. \n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[15]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[15]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[15]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[15]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[15]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[15]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[15]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[15]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[15]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>16</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[16]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"32\"<div class=\"access-path\">$value[16]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT66129712634941712494354\"<div class=\"access-path\">$value[16]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[16]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[16]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[16]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[16]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[16]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.3.7\"<div class=\"access-path\">$value[16]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (83) \"Undertake Fisheries resource survey to update management data base for the s...<div class=\"access-path\">$value[16]['title']</div></dt><dd><pre>Undertake Fisheries resource survey to update management data base for the sector. \n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[16]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[16]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[16]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[16]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[16]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[16]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[16]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[16]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[16]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>17</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[17]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"33\"<div class=\"access-path\">$value[17]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT66129712646611712494354\"<div class=\"access-path\">$value[17]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[17]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[17]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[17]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[17]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[17]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.3.8\"<div class=\"access-path\">$value[17]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (80) \"Provide adequate hatchery for fingerlings rearing and distribution to farmer...<div class=\"access-path\">$value[17]['title']</div></dt><dd><pre>Provide adequate hatchery for fingerlings rearing and distribution to farmers.  \n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[17]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[17]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[17]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[17]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[17]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[17]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[17]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[17]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[17]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>18</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[18]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"34\"<div class=\"access-path\">$value[18]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT661297126554e1712494354\"<div class=\"access-path\">$value[18]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[18]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[18]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[18]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[18]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[18]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.3.9\"<div class=\"access-path\">$value[18]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (84) \"Reposition &amp; make accessible to local fishermen Fisheries Aggregation Device...<div class=\"access-path\">$value[18]['title']</div></dt><dd><pre>Reposition &amp; make accessible to local fishermen Fisheries Aggregation Device (FAD). \n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[18]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[18]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[18]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[18]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[18]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[18]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[18]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[18]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[18]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>19</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[19]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"35\"<div class=\"access-path\">$value[19]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT661297126604a1712494354\"<div class=\"access-path\">$value[19]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[19]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[19]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[19]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[19]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[19]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (9) \"S5.1.3.10\"<div class=\"access-path\">$value[19]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (139) \"Adequately resource the division of Fisheries through (budgetary, staffing, ...<div class=\"access-path\">$value[19]['title']</div></dt><dd><pre>Adequately resource the division of Fisheries through (budgetary, staffing, housing, equipment&#8217;s) to enable it to function effectively.  \n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[19]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[19]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[19]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[19]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[19]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[19]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:52:34\"<div class=\"access-path\">$value[19]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[19]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[19]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>20</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[20]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"36\"<div class=\"access-path\">$value[20]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612986bdd8891712494699\"<div class=\"access-path\">$value[20]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[20]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[20]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[20]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[20]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[20]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.4.1\"<div class=\"access-path\">$value[20]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (47) \" Strengthen and promote research &amp; development.\"<div class=\"access-path\">$value[20]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[20]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[20]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[20]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[20]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[20]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[20]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[20]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[20]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[20]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>21</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[21]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"37\"<div class=\"access-path\">$value[21]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612986bde9a71712494699\"<div class=\"access-path\">$value[21]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[21]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[21]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[21]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[21]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[21]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.4.2\"<div class=\"access-path\">$value[21]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (49) \" Improve and strengthen human resources capacity.\"<div class=\"access-path\">$value[21]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[21]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[21]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[21]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[21]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[21]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[21]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[21]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[21]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[21]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>22</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[22]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"38\"<div class=\"access-path\">$value[22]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612986bdf76a1712494699\"<div class=\"access-path\">$value[22]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[22]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[22]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[22]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[22]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[22]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.4.3\"<div class=\"access-path\">$value[22]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (55) \" Encourage local landowner participation &amp; involvement.\"<div class=\"access-path\">$value[22]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[22]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[22]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[22]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[22]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[22]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[22]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[22]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[22]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[22]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>23</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[23]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"39\"<div class=\"access-path\">$value[23]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612986be0d461712494699\"<div class=\"access-path\">$value[23]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[23]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[23]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[23]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[23]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[23]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.4.4\"<div class=\"access-path\">$value[23]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (38) \" Market the province as a Destination.\"<div class=\"access-path\">$value[23]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[23]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[23]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[23]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[23]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[23]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[23]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[23]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[23]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[23]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>24</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[24]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"40\"<div class=\"access-path\">$value[24]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612986be18351712494699\"<div class=\"access-path\">$value[24]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[24]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[24]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[24]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[24]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[24]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.4.5\"<div class=\"access-path\">$value[24]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (55) \" Establish Provincial Tourism Bureaus and Associations.\"<div class=\"access-path\">$value[24]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[24]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[24]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[24]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[24]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[24]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[24]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[24]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[24]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[24]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>25</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[25]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"41\"<div class=\"access-path\">$value[25]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6612986be2e511712494699\"<div class=\"access-path\">$value[25]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[25]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[25]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[25]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[25]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[25]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"S5.1.4.6\"<div class=\"access-path\">$value[25]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (138) \" Develop and implement a Tourism Standards framework for products such as fo...<div class=\"access-path\">$value[25]['title']</div></dt><dd><pre> Develop and implement a Tourism Standards framework for products such as food, hospitality, travel and security for the tourism industry.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[25]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[25]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[25]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[25]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[25]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[25]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:58:19\"<div class=\"access-path\">$value[25]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[25]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[25]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>26</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[26]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (4) \"3935\"<div class=\"access-path\">$value[26]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6829c3dd2abad1747567581\"<div class=\"access-path\">$value[26]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[26]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[26]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[26]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[26]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[26]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.6.1\"<div class=\"access-path\">$value[26]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (48) \"1. Development of a Provincial Agriculture Plan.\"<div class=\"access-path\">$value[26]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[26]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[26]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[26]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[26]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[26]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[26]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:29:35\"<div class=\"access-path\">$value[26]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[26]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[26]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>27</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[27]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (4) \"3936\"<div class=\"access-path\">$value[27]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6829c3dd2c5ab1747567581\"<div class=\"access-path\">$value[27]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[27]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[27]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[27]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[27]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[27]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.6.2\"<div class=\"access-path\">$value[27]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (54) \"2. Establishment of relevant Spice &amp; Commodity Boards.\"<div class=\"access-path\">$value[27]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[27]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[27]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[27]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[27]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[27]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[27]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:28:04\"<div class=\"access-path\">$value[27]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[27]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[27]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>28</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[28]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (4) \"3937\"<div class=\"access-path\">$value[28]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6829c3dd2d30e1747567581\"<div class=\"access-path\">$value[28]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[28]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[28]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[28]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[28]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[28]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.6.3\"<div class=\"access-path\">$value[28]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (120) \"3. Invite foreign investors to develop large scale rice farming in the Sepik...<div class=\"access-path\">$value[28]['title']</div></dt><dd><pre>3. Invite foreign investors to develop large scale rice farming in the Sepik plains for export and domestic consumption.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[28]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[28]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[28]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[28]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[28]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[28]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:28:12\"<div class=\"access-path\">$value[28]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[28]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[28]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>29</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[29]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (4) \"3938\"<div class=\"access-path\">$value[29]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6829c3dd2dfed1747567581\"<div class=\"access-path\">$value[29]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[29]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[29]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[29]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[29]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[29]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.6.4\"<div class=\"access-path\">$value[29]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (144) \"4. Promote cash Crop, Livestock and Wildlife diversification farming and val...<div class=\"access-path\">$value[29]['title']</div></dt><dd><pre>4. Promote cash Crop, Livestock and Wildlife diversification farming and value added market orientated farming for export and reduce in imports.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[29]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[29]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[29]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[29]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[29]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[29]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[29]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[29]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[29]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>30</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[30]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (4) \"3939\"<div class=\"access-path\">$value[30]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6829c3dd2ec371747567581\"<div class=\"access-path\">$value[30]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[30]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[30]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[30]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[30]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[30]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.6.5\"<div class=\"access-path\">$value[30]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (90) \"5. Improve subsistence farming for self-sufficiency, food security and nutri...<div class=\"access-path\">$value[30]['title']</div></dt><dd><pre>5. Improve subsistence farming for self-sufficiency, food security and nutritional status.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[30]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[30]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[30]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[30]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[30]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[30]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[30]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[30]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[30]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>31</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[31]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (4) \"3940\"<div class=\"access-path\">$value[31]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6829c3dd2fae21747567581\"<div class=\"access-path\">$value[31]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[31]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[31]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[31]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[31]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[31]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.6.6\"<div class=\"access-path\">$value[31]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (106) \"6. Strengthen farmer organizations as agriculture co-operatives, Women in Ag...<div class=\"access-path\">$value[31]['title']</div></dt><dd><pre>6. Strengthen farmer organizations as agriculture co-operatives, Women in Agriculture and Land Owner ILGs.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[31]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[31]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[31]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[31]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[31]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[31]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[31]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[31]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[31]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>32</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[32]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (4) \"3941\"<div class=\"access-path\">$value[32]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6829c3dd3074f1747567581\"<div class=\"access-path\">$value[32]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[32]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[32]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[32]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[32]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[32]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.6.7\"<div class=\"access-path\">$value[32]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (90) \"7. Land acquisition/Land mobilisation and efficient land management for comm...<div class=\"access-path\">$value[32]['title']</div></dt><dd><pre>7. Land acquisition/Land mobilisation and efficient land management for commercial farming\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[32]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[32]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[32]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[32]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[32]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[32]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[32]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[32]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[32]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>33</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[33]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (4) \"3942\"<div class=\"access-path\">$value[33]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6829c3dd3201a1747567581\"<div class=\"access-path\">$value[33]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[33]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[33]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[33]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[33]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[33]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.6.8\"<div class=\"access-path\">$value[33]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (94) \"8. Develop characteristic estate Agri-business in each District for domestic...<div class=\"access-path\">$value[33]['title']</div></dt><dd><pre>8. Develop characteristic estate Agri-business in each District for domestic and export market\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[33]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[33]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[33]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[33]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[33]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[33]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[33]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[33]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[33]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>34</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[34]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (4) \"3943\"<div class=\"access-path\">$value[34]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6829c3dd332561747567581\"<div class=\"access-path\">$value[34]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[34]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[34]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[34]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[34]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[34]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"S1.6.9\"<div class=\"access-path\">$value[34]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (69) \"9. Import and adapt technology to increase agricultural productivity.\"<div class=\"access-path\">$value[34]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[34]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[34]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[34]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[34]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[34]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[34]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[34]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[34]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[34]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>35</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[35]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (4) \"3944\"<div class=\"access-path\">$value[35]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSIT6829c3dd340a91747567581\"<div class=\"access-path\">$value[35]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[35]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[35]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[35]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[35]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_items_groups_id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[35]['assess_items_groups_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (7) \"S1.6.10\"<div class=\"access-path\">$value[35]['code']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>title</dfn> =&gt; <var>string</var> (104) \"10. Develop a Provincial Bio-security Response Plan (PBSRP) to protect all t...<div class=\"access-path\">$value[35]['title']</div></dt><dd><pre>10. Develop a Provincial Bio-security Response Plan (PBSRP) to protect all the agroindustry commodities.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[35]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>score</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[35]['score']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[35]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[35]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[35]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[35]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:26:21\"<div class=\"access-path\">$value[35]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[35]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[35]['update_by']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "items_groups": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (4)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (4)</li><li>Contents (4)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>ucode</th><th>orgcode</th><th>plan_id</th><th>assess_exercise_id</th><th>code</th><th>title</th><th>bg_color</th><th>status</th><th>status_at</th><th>status_by</th><th>created_at</th><th>updated_at</th><th>create_by</th><th>update_by</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">21</td><td title=\"string (27)\">IGRP66129316e3f9c1712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.1.2</td><td title=\"string (26)\"> Agriculture and Livestock</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>1</th><td title=\"string (2)\">22</td><td title=\"string (27)\">IGRP66129316e4aa41712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.1.3</td><td title=\"string (10)\"> Fisheries</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>2</th><td title=\"string (2)\">23</td><td title=\"string (27)\">IGRP66129316e6c051712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.1.4</td><td title=\"string (8)\"> Tourism</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>3</th><td title=\"string (2)\">61</td><td title=\"string (27)\">IGRP6829c3527093b1747567442</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (8)\">Sector 6</td><td title=\"string (25)\">Agriculture and lifestock</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e3f9c1712493334\"<div class=\"access-path\">$value[0]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[0]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[0]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.2\"<div class=\"access-path\">$value[0]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (26) \" Agriculture and Livestock\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[0]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e4aa41712493334\"<div class=\"access-path\">$value[1]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[1]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[1]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.3\"<div class=\"access-path\">$value[1]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (10) \" Fisheries\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[1]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e6c051712493334\"<div class=\"access-path\">$value[2]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[2]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[2]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.4\"<div class=\"access-path\">$value[2]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (8) \" Tourism\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[2]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP6829c3527093b1747567442\"<div class=\"access-path\">$value[3]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[3]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[3]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[3]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"Sector 6\"<div class=\"access-path\">$value[3]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (25) \"Agriculture and lifestock\"<div class=\"access-path\">$value[3]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[3]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['update_by']</div></dt></dl></dd></dl></li></ul></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>1747568456</pre>", "_ci_previous_url": "http://localhost/part/index.php/view_assess_reports_summary_tables/ASSEX66108dae0cdd61712360878", "username": "minad", "name": "<PERSON><PERSON>", "role": "admin", "status": "1", "orgname": "Morobe Provincial Administration", "orglogo": "http://localhost/ocna/public/uploads/org_logo/2345_1681386431.gif", "orgcode": "2345", "orgcountry": "PG", "orgprov": "12"}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost/part/view_assess_reports_summary_tables/ASSEX66108dae0cdd61712360878", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9", "Cookie": "ci_session=80jp0rp2fbg424jl6b28hbmdr586ooor"}, "cookies": {"ci_session": "80jp0rp2fbg424jl6b28hbmdr586ooor"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.3.2", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/part", "timezone": "UTC", "locale": "en", "cspEnabled": false}}