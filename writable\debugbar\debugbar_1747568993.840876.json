{"url": "http://localhost/part/index.php/logout", "method": "GET", "isAJAX": false, "startTime": **********.659233, "totalTime": 146.9, "totalMemory": "6.158", "segmentDuration": 25, "segmentCount": 6, "CI_VERSION": "4.3.2", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.69658, "duration": 0.*****************}, {"name": "Routing", "component": "Timer", "start": **********.74011, "duration": 7.009506225585938e-05}, {"name": "Before Filters", "component": "Timer", "start": **********.744308, "duration": 0.022021055221557617}, {"name": "Controller", "component": "Timer", "start": **********.766336, "duration": 0.039750099182128906}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.766339, "duration": 0.037242889404296875}, {"name": "After Filters", "component": "Timer", "start": **********.806114, "duration": 0.0008289813995361328}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(0 total Query, 0  unique across 1 Connection)", "display": {"queries": []}, "badgeValue": 0, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": 0, "duration": "0.000000"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "info", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 154 )", "display": {"coreFiles": [{"name": "AutoRouterImproved.php", "path": "SYSTEMPATH\\Router\\AutoRouterImproved.php"}, {"name": "AutoRouterInterface.php", "path": "SYSTEMPATH\\Router\\AutoRouterInterface.php"}, {"name": "AutoloadConfig.php", "path": "SYSTEMPATH\\Config\\AutoloadConfig.php"}, {"name": "Autoloader.php", "path": "SYSTEMPATH\\Autoloader\\Autoloader.php"}, {"name": "BaseCollector.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php"}, {"name": "BaseConfig.php", "path": "SYSTEMPATH\\Config\\BaseConfig.php"}, {"name": "BaseConnection.php", "path": "SYSTEMPATH\\Database\\BaseConnection.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php"}, {"name": "BaseModel.php", "path": "SYSTEMPATH\\BaseModel.php"}, {"name": "BaseService.php", "path": "SYSTEMPATH\\Config\\BaseService.php"}, {"name": "CacheFactory.php", "path": "SYSTEMPATH\\Cache\\CacheFactory.php"}, {"name": "CacheInterface.php", "path": "SYSTEMPATH\\Cache\\CacheInterface.php"}, {"name": "CloneableCookieInterface.php", "path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php"}, {"name": "CodeIgniter.php", "path": "SYSTEMPATH\\CodeIgniter.php"}, {"name": "Common.php", "path": "SYSTEMPATH\\Common.php"}, {"name": "Config.php", "path": "SYSTEMPATH\\Database\\Config.php"}, {"name": "Connection.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php"}, {"name": "ConnectionInterface.php", "path": "SYSTEMPATH\\Database\\ConnectionInterface.php"}, {"name": "ContentSecurityPolicy.php", "path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php"}, {"name": "Controller.php", "path": "SYSTEMPATH\\Controller.php"}, {"name": "Cookie.php", "path": "SYSTEMPATH\\Cookie\\Cookie.php"}, {"name": "CookieInterface.php", "path": "SYSTEMPATH\\Cookie\\CookieInterface.php"}, {"name": "CookieStore.php", "path": "SYSTEMPATH\\Cookie\\CookieStore.php"}, {"name": "Database.php", "path": "SYSTEMPATH\\Database\\Database.php"}, {"name": "Database.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php"}, {"name": "DebugToolbar.php", "path": "SYSTEMPATH\\Filters\\DebugToolbar.php"}, {"name": "DotEnv.php", "path": "SYSTEMPATH\\Config\\DotEnv.php"}, {"name": "Events.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php"}, {"name": "Events.php", "path": "SYSTEMPATH\\Events\\Events.php"}, {"name": "Exceptions.php", "path": "SYSTEMPATH\\Debug\\Exceptions.php"}, {"name": "Factories.php", "path": "SYSTEMPATH\\Config\\Factories.php"}, {"name": "Factory.php", "path": "SYSTEMPATH\\Config\\Factory.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php"}, {"name": "FileLocator.php", "path": "SYSTEMPATH\\Autoloader\\FileLocator.php"}, {"name": "Files.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php"}, {"name": "FilterInterface.php", "path": "SYSTEMPATH\\Filters\\FilterInterface.php"}, {"name": "Filters.php", "path": "SYSTEMPATH\\Filters\\Filters.php"}, {"name": "FormatRules.php", "path": "SYSTEMPATH\\Validation\\FormatRules.php"}, {"name": "HandlerInterface.php", "path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php"}, {"name": "Header.php", "path": "SYSTEMPATH\\HTTP\\Header.php"}, {"name": "IncomingRequest.php", "path": "SYSTEMPATH\\HTTP\\IncomingRequest.php"}, {"name": "Logger.php", "path": "SYSTEMPATH\\Log\\Logger.php"}, {"name": "Logs.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php"}, {"name": "Message.php", "path": "SYSTEMPATH\\HTTP\\Message.php"}, {"name": "MessageInterface.php", "path": "SYSTEMPATH\\HTTP\\MessageInterface.php"}, {"name": "MessageTrait.php", "path": "SYSTEMPATH\\HTTP\\MessageTrait.php"}, {"name": "Model.php", "path": "SYSTEMPATH\\Model.php"}, {"name": "Modules.php", "path": "SYSTEMPATH\\Modules\\Modules.php"}, {"name": "OutgoingRequest.php", "path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php"}, {"name": "OutgoingRequestInterface.php", "path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php"}, {"name": "RedirectResponse.php", "path": "SYSTEMPATH\\HTTP\\RedirectResponse.php"}, {"name": "RendererInterface.php", "path": "SYSTEMPATH\\View\\RendererInterface.php"}, {"name": "Request.php", "path": "SYSTEMPATH\\HTTP\\Request.php"}, {"name": "RequestInterface.php", "path": "SYSTEMPATH\\HTTP\\RequestInterface.php"}, {"name": "RequestTrait.php", "path": "SYSTEMPATH\\HTTP\\RequestTrait.php"}, {"name": "Response.php", "path": "SYSTEMPATH\\HTTP\\Response.php"}, {"name": "ResponseInterface.php", "path": "SYSTEMPATH\\HTTP\\ResponseInterface.php"}, {"name": "ResponseTrait.php", "path": "SYSTEMPATH\\API\\ResponseTrait.php"}, {"name": "ResponseTrait.php", "path": "SYSTEMPATH\\HTTP\\ResponseTrait.php"}, {"name": "RouteCollection.php", "path": "SYSTEMPATH\\Router\\RouteCollection.php"}, {"name": "RouteCollectionInterface.php", "path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php"}, {"name": "Router.php", "path": "SYSTEMPATH\\Router\\Router.php"}, {"name": "RouterInterface.php", "path": "SYSTEMPATH\\Router\\RouterInterface.php"}, {"name": "Routes.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php"}, {"name": "Services.php", "path": "SYSTEMPATH\\Config\\Services.php"}, {"name": "Session.php", "path": "SYSTEMPATH\\Session\\Session.php"}, {"name": "SessionInterface.php", "path": "SYSTEMPATH\\Session\\SessionInterface.php"}, {"name": "Time.php", "path": "SYSTEMPATH\\I18n\\Time.php"}, {"name": "TimeTrait.php", "path": "SYSTEMPATH\\I18n\\TimeTrait.php"}, {"name": "Timer.php", "path": "SYSTEMPATH\\Debug\\Timer.php"}, {"name": "Timers.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php"}, {"name": "Toolbar.php", "path": "SYSTEMPATH\\Debug\\Toolbar.php"}, {"name": "URI.php", "path": "SYSTEMPATH\\HTTP\\URI.php"}, {"name": "UserAgent.php", "path": "SYSTEMPATH\\HTTP\\UserAgent.php"}, {"name": "Validation.php", "path": "SYSTEMPATH\\Validation\\Validation.php"}, {"name": "ValidationInterface.php", "path": "SYSTEMPATH\\Validation\\ValidationInterface.php"}, {"name": "View.php", "path": "SYSTEMPATH\\Config\\View.php"}, {"name": "View.php", "path": "SYSTEMPATH\\View\\View.php"}, {"name": "ViewDecoratorTrait.php", "path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php"}, {"name": "Views.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php"}, {"name": "array_helper.php", "path": "SYSTEMPATH\\Helpers\\array_helper.php"}, {"name": "bootstrap.php", "path": "SYSTEMPATH\\bootstrap.php"}, {"name": "form_helper.php", "path": "SYSTEMPATH\\Helpers\\form_helper.php"}, {"name": "kint_helper.php", "path": "SYSTEMPATH\\Helpers\\kint_helper.php"}, {"name": "url_helper.php", "path": "SYSTEMPATH\\Helpers\\url_helper.php"}], "userFiles": [{"name": "AbstractRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\AbstractRenderer.php"}, {"name": "App.php", "path": "APPPATH\\Config\\App.php"}, {"name": "Auth.php", "path": "APPPATH\\Filters\\Auth.php"}, {"name": "Autoload.php", "path": "APPPATH\\Config\\Autoload.php"}, {"name": "BaseController.php", "path": "APPPATH\\Controllers\\BaseController.php"}, {"name": "Cache.php", "path": "APPPATH\\Config\\Cache.php"}, {"name": "ClassLoader.php", "path": "FCPATH\\vendor\\composer\\ClassLoader.php"}, {"name": "CliRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\CliRenderer.php"}, {"name": "Common.php", "path": "APPPATH\\Common.php"}, {"name": "Constants.php", "path": "APPPATH\\Config\\Constants.php"}, {"name": "ContentSecurityPolicy.php", "path": "APPPATH\\Config\\ContentSecurityPolicy.php"}, {"name": "Cookie.php", "path": "APPPATH\\Config\\Cookie.php"}, {"name": "Database.php", "path": "APPPATH\\Config\\Database.php"}, {"name": "Escaper.php", "path": "FCPATH\\vendor\\laminas\\laminas-escaper\\src\\Escaper.php"}, {"name": "Events.php", "path": "APPPATH\\Config\\Events.php"}, {"name": "Exceptions.php", "path": "APPPATH\\Config\\Exceptions.php"}, {"name": "FacadeInterface.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\FacadeInterface.php"}, {"name": "Feature.php", "path": "APPPATH\\Config\\Feature.php"}, {"name": "Filters.php", "path": "APPPATH\\Config\\Filters.php"}, {"name": "Functions.php", "path": "FCPATH\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php"}, {"name": "Home.php", "path": "APPPATH\\Controllers\\Home.php"}, {"name": "InstalledVersions.php", "path": "FCPATH\\vendor\\composer\\InstalledVersions.php"}, {"name": "Kint.php", "path": "APPPATH\\Config\\Kint.php"}, {"name": "Kint.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Kint.php"}, {"name": "LogLevel.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LogLevel.php"}, {"name": "Logger.php", "path": "APPPATH\\Config\\Logger.php"}, {"name": "LoggerAwareTrait.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LoggerAwareTrait.php"}, {"name": "LoggerInterface.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LoggerInterface.php"}, {"name": "Modules.php", "path": "APPPATH\\Config\\Modules.php"}, {"name": "Paths.php", "path": "APPPATH\\Config\\Paths.php"}, {"name": "RendererInterface.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\RendererInterface.php"}, {"name": "RichRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\RichRenderer.php"}, {"name": "Routes.php", "path": "APPPATH\\Config\\Routes.php"}, {"name": "Services.php", "path": "APPPATH\\Config\\Services.php"}, {"name": "Session.php", "path": "APPPATH\\Config\\Session.php"}, {"name": "TextRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\TextRenderer.php"}, {"name": "Toolbar.php", "path": "APPPATH\\Config\\Toolbar.php"}, {"name": "UserAgents.php", "path": "APPPATH\\Config\\UserAgents.php"}, {"name": "Utils.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Utils.php"}, {"name": "Validation.php", "path": "APPPATH\\Config\\Validation.php"}, {"name": "View.php", "path": "APPPATH\\Config\\View.php"}, {"name": "autoload.php", "path": "FCPATH\\vendor\\autoload.php"}, {"name": "autoload_real.php", "path": "FCPATH\\vendor\\composer\\autoload_real.php"}, {"name": "autoload_static.php", "path": "FCPATH\\vendor\\composer\\autoload_static.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-ctype\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-mbstring\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-php80\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-php81\\bootstrap.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-ctype\\bootstrap80.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap80.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-mbstring\\bootstrap80.php"}, {"name": "deep_copy.php", "path": "FCPATH\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php"}, {"name": "development.php", "path": "APPPATH\\Config\\Boot\\development.php"}, {"name": "function.php", "path": "FCPATH\\vendor\\symfony\\deprecation-contracts\\function.php"}, {"name": "functions.php", "path": "FCPATH\\vendor\\symfony\\string\\Resources\\functions.php"}, {"name": "index.php", "path": "FCPATH\\index.php"}, {"name": "info_helper.php", "path": "APPPATH\\Helpers\\info_helper.php"}, {"name": "init.php", "path": "FCPATH\\vendor\\kint-php\\kint\\init.php"}, {"name": "init_helpers.php", "path": "FCPATH\\vendor\\kint-php\\kint\\init_helpers.php"}, {"name": "installed.php", "path": "FCPATH\\vendor\\composer\\installed.php"}, {"name": "orgModel.php", "path": "APPPATH\\Models\\orgModel.php"}, {"name": "platform_check.php", "path": "FCPATH\\vendor\\composer\\platform_check.php"}, {"name": "usersModel.php", "path": "APPPATH\\Models\\usersModel.php"}]}, "badgeValue": 154, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Home", "method": "logout", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Home::login"}, {"method": "GET", "route": "logout", "handler": "\\App\\Controllers\\Home::logout"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Home::about"}, {"method": "GET", "route": "findme/(.*)", "handler": "\\App\\Controllers\\Home::findme/$1"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\Admindash::index"}, {"method": "GET", "route": "employees", "handler": "\\App\\Controllers\\Employees::index"}, {"method": "GET", "route": "edit_employees/(.*)", "handler": "\\App\\Controllers\\Employees::edit_employees/$1"}, {"method": "GET", "route": "profile_employees/(.*)", "handler": "\\App\\Controllers\\Employees::profile_employees/$1"}, {"method": "GET", "route": "emp_dashboard", "handler": "\\App\\Controllers\\Portal::index"}, {"method": "GET", "route": "emp_open_activity/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_activity/$1"}, {"method": "GET", "route": "emp_open_skillcomp/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skillcomp/$1"}, {"method": "GET", "route": "emp_open_feedbacks/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_feedbacks/$1"}, {"method": "GET", "route": "emp_open_feedback_groups/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_feedback_groups/$1"}, {"method": "GET", "route": "emp_open_skills_comp/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skills_comp/$1"}, {"method": "GET", "route": "emp_open_skills_comp_groups/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skills_comp_groups/$1"}, {"method": "GET", "route": "emp_open_skills_comp_employees/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skills_comp_employees/$1"}, {"method": "GET", "route": "plans_manager", "handler": "\\App\\Controllers\\Plans::index"}, {"method": "GET", "route": "view_plans/(.*)", "handler": "\\App\\Controllers\\Plans::view_plans/$1"}, {"method": "GET", "route": "align_plans/(.*)", "handler": "\\App\\Controllers\\Plans::align_plans/$1"}, {"method": "GET", "route": "view_groups/(.*)", "handler": "\\App\\Controllers\\Plans::view_groups/$1"}, {"method": "GET", "route": "view_strategies/(.*)", "handler": "\\App\\Controllers\\Plans::view_strategies/$1"}, {"method": "GET", "route": "view_programs/(.*)", "handler": "\\App\\Controllers\\Plans::view_programs/$1"}, {"method": "GET", "route": "assessments_manager", "handler": "\\App\\Controllers\\Assessments::index"}, {"method": "GET", "route": "view_assess_exercises/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_exercises/$1"}, {"method": "GET", "route": "open_assess_exercise/(.*)", "handler": "\\App\\Controllers\\Assessments::open_assess_exercise/$1"}, {"method": "GET", "route": "view_assess_items_groups/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_items_groups/$1"}, {"method": "GET", "route": "view_assess_items/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_items/$1"}, {"method": "GET", "route": "open_assess_score_group/(.*)", "handler": "\\App\\Controllers\\Assessments::open_assess_score_group/$1"}, {"method": "GET", "route": "open_assess_score_items/(.*)", "handler": "\\App\\Controllers\\Assessments::open_assess_score_items/$1"}, {"method": "GET", "route": "view_assess_report_groupings/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_report_groupings/$1"}, {"method": "GET", "route": "view_assess_report_items/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_report_items/$1"}, {"method": "GET", "route": "view_assess_reports/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_reports/$1"}, {"method": "GET", "route": "view_assess_reports_summary_tables/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_reports_summary_tables/$1"}, {"method": "GET", "route": "view_assess_reports_raw_scores/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_reports_raw_scores/$1"}, {"method": "GET", "route": "activities", "handler": "\\App\\Controllers\\Activities::index"}, {"method": "GET", "route": "new_activity", "handler": "\\App\\Controllers\\Activities::new_activity"}, {"method": "GET", "route": "open_activity/(.*)", "handler": "\\App\\Controllers\\Activities::open_activity/$1"}, {"method": "GET", "route": "open_qualitative_unpack/(.*)", "handler": "\\App\\Controllers\\Activities::open_qualitative_unpack/$1"}, {"method": "GET", "route": "open_qualitative_item/(.*)", "handler": "\\App\\Controllers\\Activities::open_qualitative_item/$1"}, {"method": "GET", "route": "reports_qualitative_dashboard/(.*)", "handler": "\\App\\Controllers\\Reports::reports_qualitative_dashboard/$1"}, {"method": "GET", "route": "da<PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::index"}, {"method": "GET", "route": "dlogout", "handler": "\\App\\Controllers\\Dakoii::logout"}, {"method": "GET", "route": "ddash", "handler": "\\App\\Controllers\\Dakoii::ddash"}, {"method": "GET", "route": "dopen_org/(.*)", "handler": "\\App\\Controllers\\Dakoii::open_org/$1"}, {"method": "GET", "route": "dlist_org", "handler": "\\App\\Controllers\\Dakoii::list_org"}, {"method": "GET", "route": "testa", "handler": "\\App\\Controllers\\Test::index"}, {"method": "GET", "route": "ajax", "handler": "\\App\\Controllers\\Test::ajax"}, {"method": "GET", "route": "ajaxform", "handler": "\\App\\Controllers\\Test::ajaxform"}, {"method": "GET", "route": "testmap", "handler": "\\App\\Controllers\\Test::testmap"}, {"method": "POST", "route": "login", "handler": "\\App\\Controllers\\Home::login"}, {"method": "POST", "route": "dologin", "handler": "\\App\\Controllers\\Home::dologin"}, {"method": "POST", "route": "gofindme", "handler": "\\App\\Controllers\\Home::gofindme"}, {"method": "POST", "route": "open_profile", "handler": "\\App\\Controllers\\Home::open_profile"}, {"method": "POST", "route": "create_employees", "handler": "\\App\\Controllers\\Employees::create_employees"}, {"method": "POST", "route": "update_employees", "handler": "\\App\\Controllers\\Employees::update_employees"}, {"method": "POST", "route": "create_plans", "handler": "\\App\\Controllers\\Plans::create_plans"}, {"method": "POST", "route": "update_plans", "handler": "\\App\\Controllers\\Plans::update_plans"}, {"method": "POST", "route": "delete_plans", "handler": "\\App\\Controllers\\Plans::delete_plans"}, {"method": "POST", "route": "create_plan_groups", "handler": "\\App\\Controllers\\Plans::create_plan_groups"}, {"method": "POST", "route": "update_plan_groups", "handler": "\\App\\Controllers\\Plans::update_plan_groups"}, {"method": "POST", "route": "delete_plan_groups", "handler": "\\App\\Controllers\\Plans::delete_plan_groups"}, {"method": "POST", "route": "create_strategies", "handler": "\\App\\Controllers\\Plans::create_strategies"}, {"method": "POST", "route": "update_strategies", "handler": "\\App\\Controllers\\Plans::update_strategies"}, {"method": "POST", "route": "delete_strategies", "handler": "\\App\\Controllers\\Plans::delete_strategies"}, {"method": "POST", "route": "create_programs", "handler": "\\App\\Controllers\\Plans::create_programs"}, {"method": "POST", "route": "update_programs", "handler": "\\App\\Controllers\\Plans::update_programs"}, {"method": "POST", "route": "delete_programs", "handler": "\\App\\Controllers\\Plans::delete_programs"}, {"method": "POST", "route": "create_pro_act", "handler": "\\App\\Controllers\\Plans::create_pro_act"}, {"method": "POST", "route": "update_pro_act", "handler": "\\App\\Controllers\\Plans::update_pro_act"}, {"method": "POST", "route": "create_assess_plan", "handler": "\\App\\Controllers\\Assessments::create_assess_plan"}, {"method": "POST", "route": "update_assess_plan", "handler": "\\App\\Controllers\\Assessments::update_assess_plan"}, {"method": "POST", "route": "delete_assess_plan", "handler": "\\App\\Controllers\\Assessments::delete_assess_plan"}, {"method": "POST", "route": "create_assessment_exercise", "handler": "\\App\\Controllers\\Assessments::create_assessment_exercise"}, {"method": "POST", "route": "update_assessment_exercise", "handler": "\\App\\Controllers\\Assessments::update_assessment_exercise"}, {"method": "POST", "route": "delete_assessment_exercise", "handler": "\\App\\Controllers\\Assessments::delete_assessment_exercise"}, {"method": "POST", "route": "create_assess_items_group", "handler": "\\App\\Controllers\\Assessments::create_assess_items_group"}, {"method": "POST", "route": "update_assess_items_group", "handler": "\\App\\Controllers\\Assessments::update_assess_items_group"}, {"method": "POST", "route": "delete_assess_items_group", "handler": "\\App\\Controllers\\Assessments::delete_assess_items_group"}, {"method": "POST", "route": "import_assess_items_groups", "handler": "\\App\\Controllers\\Assessments::import_assess_items_groups"}, {"method": "POST", "route": "create_assess_report_group", "handler": "\\App\\Controllers\\Assessments::create_assess_report_group"}, {"method": "POST", "route": "update_assess_report_group", "handler": "\\App\\Controllers\\Assessments::update_assess_report_group"}, {"method": "POST", "route": "delete_assess_report_group", "handler": "\\App\\Controllers\\Assessments::delete_assess_report_group"}, {"method": "POST", "route": "import_assess_items", "handler": "\\App\\Controllers\\Assessments::import_assess_items"}, {"method": "POST", "route": "create_assess_items", "handler": "\\App\\Controllers\\Assessments::create_assess_items"}, {"method": "POST", "route": "update_assess_items", "handler": "\\App\\Controllers\\Assessments::update_assess_items"}, {"method": "POST", "route": "delete_assess_items", "handler": "\\App\\Controllers\\Assessments::delete_assess_items"}, {"method": "POST", "route": "create_assess_report_items", "handler": "\\App\\Controllers\\Assessments::create_assess_report_items"}, {"method": "POST", "route": "delete_assess_report_items", "handler": "\\App\\Controllers\\Assessments::delete_assess_report_items"}, {"method": "POST", "route": "enter_score_items", "handler": "\\App\\Controllers\\Assessments::enter_score_items"}, {"method": "POST", "route": "update_target_rates", "handler": "\\App\\Controllers\\Assessments::update_target_rates"}, {"method": "POST", "route": "import_qualitative_data", "handler": "\\App\\Controllers\\Activities::import_qualitative_data"}, {"method": "POST", "route": "create_qualitative_item", "handler": "\\App\\Controllers\\Activities::create_qualitative_item"}, {"method": "POST", "route": "update_qualitative_item", "handler": "\\App\\Controllers\\Activities::update_qualitative_item"}, {"method": "POST", "route": "delete_qualitative_item", "handler": "\\App\\Controllers\\Activities::delete_qualitative_item"}, {"method": "POST", "route": "bg_qualitative_color", "handler": "\\App\\Controllers\\Activities::bg_qualitative_color"}, {"method": "POST", "route": "qualitative_for_score", "handler": "\\App\\Controllers\\Activities::qualitative_for_score"}, {"method": "POST", "route": "download_qualitative_data", "handler": "\\App\\Controllers\\Activities::download_qualitative_data"}, {"method": "POST", "route": "import_qualitative_score", "handler": "\\App\\Controllers\\Activities::import_qualitative_score"}, {"method": "POST", "route": "create_activity", "handler": "\\App\\Controllers\\Activities::create_activity"}, {"method": "POST", "route": "update_activity", "handler": "\\App\\Controllers\\Activities::update_activity"}, {"method": "POST", "route": "update_activity_status", "handler": "\\App\\Controllers\\Activities::update_activity_status"}, {"method": "POST", "route": "dlogin", "handler": "\\App\\Controllers\\Dakoii::login"}, {"method": "POST", "route": "dad<PERSON>g", "handler": "\\App\\Controllers\\Dakoii::addorg"}, {"method": "POST", "route": "deditorg", "handler": "\\App\\Controllers\\Dakoii::editorg"}, {"method": "POST", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::adduser"}, {"method": "POST", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::create_admin"}, {"method": "POST", "route": "ajax", "handler": "\\App\\Controllers\\Test::ajax"}, {"method": "POST", "route": "ajaxform", "handler": "\\App\\Controllers\\Test::ajaxform"}]}, "badgeValue": 50, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "9.10", "count": 1}}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.700745, "duration": 0.009104013442993164}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1747568952</pre>", "_ci_previous_url": "http://localhost/part/index.php/view_assess_items_groups/ASSEX66108dae0cdd61712360878", "username": "minad", "name": "<PERSON><PERSON>", "role": "admin", "status": "1", "orgname": "Morobe Provincial Administration", "orglogo": "http://localhost/ocna/public/uploads/org_logo/2345_1681386431.gif", "orgcode": "2345", "orgcountry": "PG", "orgprov": "12"}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost/part/view_assess_items_groups/ASSEX66108dae0cdd61712360878", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9", "Cookie": "ci_session=8fl94eu55k3qmm2k1us1ib645u219bl1"}, "cookies": {"ci_session": "8fl94eu55k3qmm2k1us1ib645u219bl1"}, "request": "HTTP/1.1", "response": {"statusCode": 307, "reason": "Temporary Redirect", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8", "Location": "http://localhost/part/"}}}, "config": {"ciVersion": "4.3.2", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/part", "timezone": "UTC", "locale": "en", "cspEnabled": false}}