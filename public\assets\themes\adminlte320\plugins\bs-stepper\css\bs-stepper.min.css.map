{"version": 3, "sources": ["..\\..\\src\\css\\bs-stepper.css", "dist\\css\\bs-stepper.css"], "names": [], "mappings": "AAAA;;;;AAMA,0BACE,QAAA,mBAAA,QAAA,YACA,cAAA,KAAA,UAAA,KACA,eAAA,OAAA,YAAA,OACA,cAAA,OAAA,gBAAA,OACA,QAAA,KACA,UAAA,KACA,YAAA,IACA,YAAA,IACA,MAAA,QACA,WAAA,OACA,gBAAA,KACA,YAAA,OACA,eAAA,OACA,oBAAA,KAAA,iBAAA,KAAA,gBAAA,KAAA,YAAA,KACA,iBAAA,YACA,OAAA,KACA,cAAA,OACA,WAAA,iBAAA,KAAA,QAAA,CAAA,MAAA,KAAA,SAGF,wDACE,OAAA,QCWF,mCDRA,mCAEE,eAAA,KACA,QAAA,IAGF,gCACE,MAAA,QACA,QAAA,EAGF,gCACE,gBAAA,KACA,iBAAA,gBAGF,yBACE,0BACE,mBAAA,OAAA,eAAA,OACA,QAAA,MAIJ,kBACE,QAAA,aACA,OAAA,OAGF,mBACE,QAAA,YAAA,QAAA,KACA,eAAA,OAAA,YAAA,OAGF,yBACE,mBACE,OAAA,EAAA,MACA,WAAA,QCeJ,kBDXA,iBAEE,SAAA,EAAA,EAAA,KAAA,KAAA,EAAA,EAAA,KACA,UAAA,IACA,WAAA,IACA,OAAA,KACA,iBAAA,gBAGF,yBCaE,kBDZA,iBAEE,wBAAA,KAAA,WAAA,MAIJ,mBACE,QAAA,mBAAA,QAAA,YACA,mBAAA,OAAA,cAAA,OACA,cAAA,OAAA,gBAAA,OACA,MAAA,IACA,OAAA,IACA,QAAA,KAAA,EACA,OAAA,OACA,YAAA,IACA,MAAA,KACA,iBAAA,QACA,cAAA,IAGF,2BACE,iBAAA,QAGF,oBACE,QAAA,EAAA,KAAA,KAGF,yBACE,oBACE,QAAA,GAIJ,qBACE,QAAA,YAAA,QAAA,KAGF,wCACE,mBAAA,OAAA,eAAA,OACA,eAAA,QAAA,YAAA,QACA,OAAA,EAGF,sCCmBA,8BDjBE,QAAA,MAGF,iDCmBA,yCDjBE,QAAA,MACA,WAAA,OCsBF,gCDnBA,4BAEE,QAAA,KAGF,0BCmBA,sBDjBE,WAAA,OACA,oBAAA,IACA,oBAAA,QCsBF,iCDnBA,6BAEE,WAAA,QACA,QAAA,ECsBF,uCDnBA,mCAEE,QAAA,MACA,WAAA,QCsBF,oCDnBA,gCAEE,QAAA,MAGF,0DCmBA,kDDjBE,QAAA,KAGF,8CCmBA,sCDjBE,WAAA", "sourcesContent": ["/*!\r\n * bsStepper v1.7.0 (https://github.com/<PERSON>-<PERSON>/bs-stepper)\r\n * Copyright 2018 - 2019 <PERSON>-<PERSON> <<EMAIL>>\r\n * Licensed under MIT (https://github.com/<PERSON>-<PERSON>/bs-stepper/blob/master/LICENSE)\r\n */\r\n\r\n.bs-stepper .step-trigger {\r\n  display: inline-flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px;\r\n  font-size: 1rem;\r\n  font-weight: 700;\r\n  line-height: 1.5;\r\n  color: #6c757d;\r\n  text-align: center;\r\n  text-decoration: none;\r\n  white-space: nowrap;\r\n  vertical-align: middle;\r\n  user-select: none;\r\n  background-color: transparent;\r\n  border: none;\r\n  border-radius: .25rem;\r\n  transition: background-color .15s ease-out, color .15s ease-out;\r\n}\r\n\r\n.bs-stepper .step-trigger:not(:disabled):not(.disabled) {\r\n  cursor: pointer;\r\n}\r\n\r\n.bs-stepper .step-trigger:disabled,\r\n.bs-stepper .step-trigger.disabled {\r\n  pointer-events: none;\r\n  opacity: .65;\r\n}\r\n\r\n.bs-stepper .step-trigger:focus {\r\n  color: #007bff;\r\n  outline: none;\r\n}\r\n\r\n.bs-stepper .step-trigger:hover {\r\n  text-decoration: none;\r\n  background-color: rgba(0, 0, 0, .06);\r\n}\r\n\r\n@media (max-width: 520px) {\r\n  .bs-stepper .step-trigger {\r\n    flex-direction: column;\r\n    padding: 10px;\r\n  }\r\n}\r\n\r\n.bs-stepper-label {\r\n  display: inline-block;\r\n  margin: .25rem;\r\n}\r\n\r\n.bs-stepper-header {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n@media (max-width: 520px) {\r\n  .bs-stepper-header {\r\n    margin: 0 -10px;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.bs-stepper-line,\r\n.bs-stepper .line {\r\n  flex: 1 0 32px;\r\n  min-width: 1px;\r\n  min-height: 1px;\r\n  margin: auto;\r\n  background-color: rgba(0, 0, 0, .12);\r\n}\r\n\r\n@media (max-width: 400px) {\r\n  .bs-stepper-line,\r\n  .bs-stepper .line {\r\n    flex-basis: 20px;\r\n  }\r\n}\r\n\r\n.bs-stepper-circle {\r\n  display: inline-flex;\r\n  align-content: center;\r\n  justify-content: center;\r\n  width: 2em;\r\n  height: 2em;\r\n  padding: .5em 0;\r\n  margin: .25rem;\r\n  line-height: 1em;\r\n  color: #fff;\r\n  background-color: #6c757d;\r\n  border-radius: 1em;\r\n}\r\n\r\n.active .bs-stepper-circle {\r\n  background-color: #007bff;\r\n}\r\n\r\n.bs-stepper-content {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n@media (max-width: 520px) {\r\n  .bs-stepper-content {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.bs-stepper.vertical {\r\n  display: flex;\r\n}\r\n\r\n.bs-stepper.vertical .bs-stepper-header {\r\n  flex-direction: column;\r\n  align-items: stretch;\r\n  margin: 0;\r\n}\r\n\r\n.bs-stepper.vertical .bs-stepper-pane,\r\n.bs-stepper.vertical .content {\r\n  display: block;\r\n}\r\n\r\n.bs-stepper.vertical .bs-stepper-pane:not(.fade),\r\n.bs-stepper.vertical .content:not(.fade) {\r\n  display: block;\r\n  visibility: hidden;\r\n}\r\n\r\n.bs-stepper-pane:not(.fade),\r\n.bs-stepper .content:not(.fade) {\r\n  display: none;\r\n}\r\n\r\n.bs-stepper .content.fade,\r\n.bs-stepper-pane.fade {\r\n  visibility: hidden;\r\n  transition-duration: .3s;\r\n  transition-property: opacity;\r\n}\r\n\r\n.bs-stepper-pane.fade.active,\r\n.bs-stepper .content.fade.active {\r\n  visibility: visible;\r\n  opacity: 1;\r\n}\r\n\r\n.bs-stepper-pane.active:not(.fade),\r\n.bs-stepper .content.active:not(.fade) {\r\n  display: block;\r\n  visibility: visible;\r\n}\r\n\r\n.bs-stepper-pane.dstepper-block,\r\n.bs-stepper .content.dstepper-block {\r\n  display: block;\r\n}\r\n\r\n.bs-stepper:not(.vertical) .bs-stepper-pane.dstepper-none,\r\n.bs-stepper:not(.vertical) .content.dstepper-none {\r\n  display: none;\r\n}\r\n\r\n.vertical .bs-stepper-pane.fade.dstepper-none,\r\n.vertical .content.fade.dstepper-none {\r\n  visibility: hidden;\r\n}\r\n", "/*!\r\n * bsStepper v1.7.0 (https://github.com/<PERSON>-<PERSON>/bs-stepper)\r\n * Copyright 2018 - 2019 Johann-<PERSON> <<EMAIL>>\r\n * Licensed under MIT (https://github.com/<PERSON>-<PERSON>/bs-stepper/blob/master/LICENSE)\r\n */\r\n\r\n.bs-stepper .step-trigger {\r\n  display: -ms-inline-flexbox;\r\n  display: inline-flex;\r\n  -ms-flex-wrap: wrap;\r\n      flex-wrap: wrap;\r\n  -ms-flex-align: center;\r\n      align-items: center;\r\n  -ms-flex-pack: center;\r\n      justify-content: center;\r\n  padding: 20px;\r\n  font-size: 1rem;\r\n  font-weight: 700;\r\n  line-height: 1.5;\r\n  color: #6c757d;\r\n  text-align: center;\r\n  text-decoration: none;\r\n  white-space: nowrap;\r\n  vertical-align: middle;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n  background-color: transparent;\r\n  border: none;\r\n  border-radius: .25rem;\r\n  transition: background-color .15s ease-out, color .15s ease-out;\r\n}\r\n\r\n.bs-stepper .step-trigger:not(:disabled):not(.disabled) {\r\n  cursor: pointer;\r\n}\r\n\r\n.bs-stepper .step-trigger:disabled,\r\n.bs-stepper .step-trigger.disabled {\r\n  pointer-events: none;\r\n  opacity: .65;\r\n}\r\n\r\n.bs-stepper .step-trigger:focus {\r\n  color: #007bff;\r\n  outline: none;\r\n}\r\n\r\n.bs-stepper .step-trigger:hover {\r\n  text-decoration: none;\r\n  background-color: rgba(0, 0, 0, .06);\r\n}\r\n\r\n@media (max-width: 520px) {\r\n  .bs-stepper .step-trigger {\r\n    -ms-flex-direction: column;\r\n        flex-direction: column;\r\n    padding: 10px;\r\n  }\r\n}\r\n\r\n.bs-stepper-label {\r\n  display: inline-block;\r\n  margin: .25rem;\r\n}\r\n\r\n.bs-stepper-header {\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -ms-flex-align: center;\r\n      align-items: center;\r\n}\r\n\r\n@media (max-width: 520px) {\r\n  .bs-stepper-header {\r\n    margin: 0 -10px;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.bs-stepper-line,\r\n.bs-stepper .line {\r\n  -ms-flex: 1 0 32px;\r\n      flex: 1 0 32px;\r\n  min-width: 1px;\r\n  min-height: 1px;\r\n  margin: auto;\r\n  background-color: rgba(0, 0, 0, .12);\r\n}\r\n\r\n@media (max-width: 400px) {\r\n  .bs-stepper-line,\r\n  .bs-stepper .line {\r\n    -ms-flex-preferred-size: 20px;\r\n        flex-basis: 20px;\r\n  }\r\n}\r\n\r\n.bs-stepper-circle {\r\n  display: -ms-inline-flexbox;\r\n  display: inline-flex;\r\n  -ms-flex-line-pack: center;\r\n      align-content: center;\r\n  -ms-flex-pack: center;\r\n      justify-content: center;\r\n  width: 2em;\r\n  height: 2em;\r\n  padding: .5em 0;\r\n  margin: .25rem;\r\n  line-height: 1em;\r\n  color: #fff;\r\n  background-color: #6c757d;\r\n  border-radius: 1em;\r\n}\r\n\r\n.active .bs-stepper-circle {\r\n  background-color: #007bff;\r\n}\r\n\r\n.bs-stepper-content {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n@media (max-width: 520px) {\r\n  .bs-stepper-content {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.bs-stepper.vertical {\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n}\r\n\r\n.bs-stepper.vertical .bs-stepper-header {\r\n  -ms-flex-direction: column;\r\n      flex-direction: column;\r\n  -ms-flex-align: stretch;\r\n      align-items: stretch;\r\n  margin: 0;\r\n}\r\n\r\n.bs-stepper.vertical .bs-stepper-pane,\r\n.bs-stepper.vertical .content {\r\n  display: block;\r\n}\r\n\r\n.bs-stepper.vertical .bs-stepper-pane:not(.fade),\r\n.bs-stepper.vertical .content:not(.fade) {\r\n  display: block;\r\n  visibility: hidden;\r\n}\r\n\r\n.bs-stepper-pane:not(.fade),\r\n.bs-stepper .content:not(.fade) {\r\n  display: none;\r\n}\r\n\r\n.bs-stepper .content.fade,\r\n.bs-stepper-pane.fade {\r\n  visibility: hidden;\r\n  transition-duration: .3s;\r\n  transition-property: opacity;\r\n}\r\n\r\n.bs-stepper-pane.fade.active,\r\n.bs-stepper .content.fade.active {\r\n  visibility: visible;\r\n  opacity: 1;\r\n}\r\n\r\n.bs-stepper-pane.active:not(.fade),\r\n.bs-stepper .content.active:not(.fade) {\r\n  display: block;\r\n  visibility: visible;\r\n}\r\n\r\n.bs-stepper-pane.dstepper-block,\r\n.bs-stepper .content.dstepper-block {\r\n  display: block;\r\n}\r\n\r\n.bs-stepper:not(.vertical) .bs-stepper-pane.dstepper-none,\r\n.bs-stepper:not(.vertical) .content.dstepper-none {\r\n  display: none;\r\n}\r\n\r\n.vertical .bs-stepper-pane.fade.dstepper-none,\r\n.vertical .content.fade.dstepper-none {\r\n  visibility: hidden;\r\n}\r\n\r\n/*# sourceMappingURL=bs-stepper.css.map */"]}