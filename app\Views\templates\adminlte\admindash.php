<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><?= $title ?></title>

  <link rel="shortcut icon" type="image/x-icon" href="<?= base_url() ?>public/assets/system_img/favicon.ico">

  <!-- Google Fonts - Keep only essential fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="<?= base_url() ?>public/assets/themes/adminlte320/plugins/fontawesome-free/css/all.min.css">
  
  <!-- AdminLTE Core CSS (includes Bootstrap) -->
  <link rel="stylesheet" href="<?= base_url() ?>public/assets/themes/adminlte320/dist/css/adminlte.min.css">

  <!-- Minimal required plugins -->
  <link rel="stylesheet" href="<?= base_url() ?>public/assets/themes/adminlte320/plugins/toastr/toastr.min.css">
  <link rel="stylesheet" href="<?= base_url() ?>public/assets/themes/adminlte320/plugins/select2/css/select2.min.css">
  <link rel="stylesheet" href="<?= base_url() ?>public/assets/themes/adminlte320/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">

  <!-- jQuery -->
  <script src="<?= base_url() ?>public/assets/themes/adminlte320/plugins/jquery/jquery.min.js"></script>
  
  <!-- Bootstrap Bundle JS -->
  <script src="<?= base_url() ?>public/assets/themes/adminlte320/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
  
  <!-- AdminLTE App -->
  <script src="<?= base_url() ?>public/assets/themes/adminlte320/dist/js/adminlte.min.js"></script>
  
  <!-- Minimal required plugins -->
  <script src="<?= base_url() ?>public/assets/themes/adminlte320/plugins/toastr/toastr.min.js"></script>
  <script src="<?= base_url() ?>public/assets/themes/adminlte320/plugins/select2/js/select2.full.min.js"></script>
  <script src="<?= base_url() ?>public/assets/themes/adminlte320/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
</head>

<body class="hold-transition sidebar-mini">
  <div class="wrapper">
    <?php if (session()->has('error')) : ?>
      <span class="toastrDefaultError"></span>
    <?php endif; ?>
    <?php if (session()->has('success')) : ?>
      <span class="toastrDefaultSuccess"></span>
    <?php endif; ?>

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
      <!-- Left navbar links -->
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
        <li class="nav-item d-none d-sm-inline-block">
          <a href="<?= base_url() ?>dashboard" class="nav-link">
            <i class="fas fa-building mr-2"></i>
            <?= session('orgcode') ?> - <?= session('orgname') ?>
          </a>
        </li>
      </ul>

      <!-- Right navbar links -->
      <ul class="navbar-nav ml-auto">
        <li class="nav-item dropdown">
          <a class="nav-link" data-toggle="dropdown" href="#">
            <i class="far fa-bell"></i>
            <span class="badge badge-warning navbar-badge">3</span>
          </a>
          <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
            <span class="dropdown-item dropdown-header">3 Notifications</span>
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item">
              <i class="fas fa-file-alt mr-2"></i> New assessment ready
              <span class="float-right text-muted text-sm">3 mins</span>
            </a>
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item">
              <i class="fas fa-chart-pie mr-2"></i> Report completed
              <span class="float-right text-muted text-sm">12 hours</span>
            </a>
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item">
              <i class="fas fa-users mr-2"></i> New user joined
              <span class="float-right text-muted text-sm">2 days</span>
            </a>
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item dropdown-footer">See All Notifications</a>
          </div>
        </li>
        
        <li class="nav-item">
          <a class="nav-link" data-widget="fullscreen" href="#" role="button">
            <i class="fas fa-expand-arrows-alt"></i>
          </a>
        </li>
        
        <li class="nav-item dropdown user-menu">
          <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <img src="<?= base_url() ?>public/assets/system_img/no-users-img.png" class="user-image img-circle elevation-1" alt="User Image">
            <span class="d-none d-md-inline"><?= session('name') ?></span>
          </a>
          <ul class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
            <!-- User image -->
            <li class="user-header bg-primary">
              <img src="<?= base_url() ?>public/assets/system_img/no-users-img.png" class="img-circle elevation-2" alt="User Image">
              <p>
                <?= session('name') ?>
                <small>Member since Nov. 2023</small>
              </p>
            </li>
            <!-- Menu Footer-->
            <li class="user-footer">
              <a href="#" class="btn btn-default btn-flat">Profile</a>
              <a href="<?= base_url() ?>logout" class="btn btn-danger btn-flat float-right">Sign out</a>
            </li>
          </ul>
        </li>
      </ul>
    </nav>
    <!-- /.navbar -->

    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
      <!-- Brand Logo -->
      <a href="#" class="brand-link">
        <img src="<?= imgcheck(session('orglogo')) ?>" alt="org logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light"><?= session('orgcode') ?></span>
      </a>

      <!-- Sidebar -->
      <div class="sidebar">
        <!-- Sidebar user panel (optional) -->
        <div class="user-panel mt-3 pb-3 mb-3 d-flex">
          <div class="image">
            <img src="<?= base_url() ?>public/assets/system_img/no-users-img.png" class="img-circle elevation-2" alt="User Image">
          </div>
          <div class="info">
            <a href="#" class="d-block"><?= session('name') ?></a>
          </div>
        </div>

        <!-- Sidebar Menu -->
        <nav class="mt-2">
          <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
            <!-- Dashboard Menu Item -->
            <li class="nav-item">
              <?php $active = "";
              if ($menu == "dashboard") {
                $active = "active";
              } ?>
              <a href="<?= base_url() ?>dashboard" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-tachometer-alt"></i>
                <p>Dashboard</p>
              </a>
            </li>
            
            <!-- Assessment Manager Menu Item -->
            <li class="nav-item">
              <?php $active = "";
              if ($menu == "assessment" || $menu == "assessments_manager") {
                $active = "active";
              } ?>
              <a href="<?= base_url() ?>assessments_manager" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-clipboard-check"></i>
                <p>Assessment Manager</p>
              </a>
            </li>
            
           
           
            
            <!-- Logout Menu Item -->
            <li class="nav-item mt-3">
              <a href="<?= base_url() ?>logout" class="nav-link bg-danger">
                <i class="nav-icon fas fa-sign-out-alt"></i>
                <p>Log Out (<?= session('username') ?>)</p>
              </a>
            </li>
          </ul>
        </nav>
        <!-- /.sidebar-menu -->
      </div>
      <!-- /.sidebar -->
    </aside>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
      <?= $this->renderSection('content') ?>
    </div>
    <!-- /.content-wrapper -->

    <footer class="main-footer">
      <div class="float-right d-none d-sm-block">
        <b><?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?></b>
      </div>
      <strong>Copyright &copy; 2024 <a href="https://www.dakoiims.com">Dakoii Systems</a></strong>
      All rights reserved.
    </footer>

    <!-- Control Sidebar -->
    <aside class="control-sidebar control-sidebar-dark">
      <!-- Control sidebar content goes here -->
    </aside>
    <!-- /.control-sidebar -->
  </div>
  <!-- ./wrapper -->

  <?= $this->renderSection('calendar'); ?>
</body>

<script>
  // Basic initialization
  $(function() {
    bsCustomFileInput.init();
    
    // Toastr notifications
    $('.toastrDefaultSuccess').show(function() {
      toastr.success('<?= session('success') ?>')
    });
    
    $('.toastrDefaultError').show(function() {
      toastr.error('<?= session('error') ?>')
    });
    
    // Select2
    $('.select2').select2();
  });
</script>

</html>