<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark"><i class="fas fa-chart-line mr-2"></i>Assessment Report Dashboard</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard"><i class="fas fa-home"></i></a></li>
                    <li class="breadcrumb-item">Report Groups</li>
                    <li class="breadcrumb-item active">Report Items</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<!-- /.content-header -->

<section class="content">
    <div class="container-fluid">
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="<?= base_url() ?>open_assess_exercise/<?= $assex['ucode'] ?>" class="btn btn-outline-secondary">
                        <i class="fa fa-arrow-left mr-1"></i> Back to Exercise
                    </a>
                    <div class="btn-group">
                        <a href="<?= base_url() ?>view_assess_reports_summary_tables/<?= $assex['ucode'] ?>" class="btn btn-info">
                            <i class="fas fa-table mr-1"></i> Summary Tables
                        </a>
                        <a href="<?= base_url() ?>view_assess_reports_raw_scores/<?= $assex['ucode'] ?>" class="btn btn-info">
                            <i class="fas fa-scroll mr-1"></i> Raw Scores
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assessment Info Card - Redesigned with typography focus -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card bg-gradient-light">
                    <div class="card-body py-4">
                        <div class="row align-items-center">
                            <div class="col-md-4 text-center text-md-right border-right">
                                <h2 class="text-primary font-weight-bold mb-0" style="font-family: 'Poppins', sans-serif; letter-spacing: -0.5px;">
                                    Assessment Information
                                </h2>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-calendar-check mr-1"></i> <?= date('F j, Y') ?>
                                </p>
                            </div>
                            <div class="col-md-4 pl-md-4">
                                <div class="d-flex mb-2">
                                    <div class="mr-3">
                                        <span class="bg-primary p-2 rounded-circle">
                                            <i class="fas fa-clipboard-list text-white"></i>
                                        </span>
                                    </div>
                                    <div>
                                        <h6 class="text-uppercase text-muted mb-1" style="font-size: 0.8rem; letter-spacing: 1px;">PLAN</h6>
                                        <h5 class="font-weight-bold mb-0" style="font-size: 1.1rem;"><?= $plan['code'] ?>. <?= $plan['title'] ?></h5>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 pl-md-4">
                                <div class="d-flex mb-2">
                                    <div class="mr-3">
                                        <span class="bg-info p-2 rounded-circle">
                                            <i class="fas fa-tasks text-white"></i>
                                        </span>
                                    </div>
                                    <div>
                                        <h6 class="text-uppercase text-muted mb-1" style="font-size: 0.8rem; letter-spacing: 1px;">EXERCISE</h6>
                                        <h5 class="font-weight-bold mb-0" style="font-size: 1.1rem;"><?= $assex['code'] ?>. <?= $assex['title'] ?></h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overall Performance Summary -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-chart-pie mr-1"></i> Performance Summary</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- ====================================== DATA CALCULATIONS ======================================= -->
                            <?php
                            $item_targets_total = $item_achieved_total = 0;
                            $item_labels = $item_targets = $item_achieved = $item_gaps = "";
                            foreach ($items as $item) {
                                $item_labels .= "'" . $item['code'] . "',";
                                $item_targets .= $assex['max_rate'] . ",";
                                $item_achieved .= $item['score'] . ",";
                                $item_gaps .= - ($assex['max_rate'] - $item['score']) . ",";
                                $item_targets_total += $assex['max_rate'];
                                $item_achieved_total += $item['score'];
                            }
                            //calcuate average totals
                            $targets_total_avg = $item_targets_total / count($items);
                            $achieved_total_avg = $item_achieved_total / count($items);
                            //calculate average gaps
                            $gap_avg = $targets_total_avg - $achieved_total_avg;
                            $gap_pc_avg = (($gap_avg / $targets_total_avg) * 100);
                            $achieved_pc_avg = (($achieved_total_avg / $targets_total_avg) * 100);
                            ?>

                            <div class="col-lg-3 col-6">
                                <div class="small-box bg-success">
                                    <div class="inner">
                                        <h3><?= round($achieved_pc_avg, 1) ?>%</h3>
                                        <p>Achieved</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-6">
                                <div class="small-box bg-warning">
                                    <div class="inner">
                                        <h3><?= round($gap_pc_avg, 1) ?>%</h3>
                                        <p>Yet To Achieve</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-hourglass-half"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-6">
                                <div class="small-box bg-info">
                                    <div class="inner">
                                        <h3><?= $item_achieved_total ?></h3>
                                        <p>Points Achieved</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-6">
                                <div class="small-box bg-primary">
                                    <div class="inner">
                                        <h3><?= $item_targets_total ?></h3>
                                        <p>Total Target Points</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-bullseye"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overall Charts Section -->
        <div class="row" id="overall_charts">
            <div class="col-md-12">
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-chart-bar mr-1"></i> Overall Performance Analytics</h3>
                        <div class="card-tools">
                            <button class="btn btn-tool" id="btn_overall_charts">
                                <i class="fas fa-download"></i> Export Charts
                            </button>
                            <?php copychart_toimage('overall_charts', 'btn_overall_charts') ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-7">
                                <div class="card shadow-sm" id="all_radarChart">
                                    <div class="card-header bg-gradient-primary">
                                        <h5 class="card-title">
                                            <i class="fas fa-radar mr-1"></i> Gap Analysis Radar Chart
                                        </h5>
                                        <div class="card-tools">
                                            <button class="btn btn-tool" id="btn_all_radarChart">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <?php copychart_toimage('all_radarChart', 'btn_all_radarChart') ?>
                                        </div>
                                    </div>
                                    <div class="card-body p-0">
                                        <canvas width="400" height="300" id="myradar_gap_chart"></canvas>
                                        <?php chart_radar('myradar_gap_chart', $item_labels, $item_targets, $item_achieved) ?>
                                    </div>
                                    <div class="card-footer bg-light">
                                        <div class="row">
                                            <div class="col-md-4 text-center">
                                                <h4 class="text-success"><?= round($achieved_pc_avg, 1) ?>%</h4>
                                                <small class="text-muted">Achieved</small>
                                            </div>
                                            <div class="col-md-4 text-center">
                                                <h4 class="text-warning"><?= round($gap_pc_avg, 1) ?>%</h4>
                                                <small class="text-muted">Yet To Achieve</small>
                                            </div>
                                            <div class="col-md-4 text-center">
                                                <h4 class="text-info"><?= $item_achieved_total ?>/<?= $item_targets_total ?></h4>
                                                <small class="text-muted">Points: Achieved/Target</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div class="row">
                                    <div class="col-md-12 mb-4">
                                        <div class="card shadow-sm" id="all_pieChart">
                                            <div class="card-header bg-gradient-primary">
                                                <h5 class="card-title">
                                                    <i class="fas fa-chart-pie mr-1"></i> Achievement Breakdown
                                                </h5>
                                                <div class="card-tools">
                                                    <button class="btn btn-tool" id="btn_all_pieChart">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <?php copychart_toimage('all_pieChart', 'btn_all_pieChart') ?>
                                                </div>
                                            </div>
                                            <div class="card-body text-center p-2">
                                                <svg id="pieChart2" height="250"></svg>
                                                <?= chart_pie("pieChart2", $achieved_total_avg, $gap_avg) ?>
                                            </div>
                                            <div class="card-footer bg-light d-flex justify-content-between">
                                                <span>
                                                    <span class="badge badge-success p-2"></span>
                                                    <span class="ml-1"><?= round($achieved_pc_avg) ?>% Achieved</span>
                                                </span>
                                                <span>
                                                    <span class="badge badge-warning p-2"></span>
                                                    <span class="ml-1"><?= round($gap_pc_avg) ?>% Yet To Achieve</span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="card shadow-sm" id="all_barChart">
                                            <div class="card-header bg-gradient-primary">
                                                <h5 class="card-title">
                                                    <i class="fas fa-chart-bar mr-1"></i> Achievement Comparison
                                                </h5>
                                                <div class="card-tools">
                                                    <button class="btn btn-tool" id="btn_all_barChart">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <?php copychart_toimage('all_barChart', 'btn_all_barChart') ?>
                                                </div>
                                            </div>
                                            <div class="card-body p-2">
                                                <canvas id="overall_stacked" height="200"></canvas>
                                                <?php chart_stacked_bar('overall_stacked', $item_labels, $item_achieved, $item_gaps) ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Item Groups Charts Section -->
        <div class="row" id="item_groups_charts">
            <div class="col-md-12">
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-layer-group mr-1"></i> Item Groups Analysis</h3>
                        <div class="card-tools">
                            <button class="btn btn-tool" id="btn_item_groups_charts">
                                <i class="fas fa-download"></i> Export Charts
                            </button>
                            <?php copychart_toimage('item_groups_charts', 'btn_item_groups_charts') ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php
                            $items_groups_labels = $items_groups_achieved = $items_groups_toachieve = "";

                            foreach ($items_groups as $itgroup) :
                                $achieved_point = $toachieve_point = $target_point = $achieved_pc = $toachieve_pc  = 0;
                                $achieved = $toachieve = "";
                                //calculate achieve & yet to achieve
                                foreach ($items as $item) {
                                    if ($itgroup['id'] == $item['assess_items_groups_id']) {
                                        $achieved_point += $item['score'];
                                        $target_point += $assex['max_rate'];
                                        $toachieve_point +=  $assex['max_rate'] - $item['score'];
                                    }
                                }

                                $achieved_pc = ($achieved_point / $target_point) * 100;
                                $toachieve_pc = 100 - $achieved_pc;

                                //data for staked bar chart
                                $items_groups_labels .= "'" . $itgroup['code'] . "',";
                                $items_groups_achieved .= $achieved_point . ",";
                                $items_groups_toachieve .= $target_point . ",";
                            ?>
                                <div class="col-md-4 mb-4">
                                    <div class="card shadow-sm h-100" id="igroup_chart<?= $itgroup['id'] ?>">
                                        <div class="card-header bg-gradient-info">
                                            <h5 class="card-title">
                                                <?= $itgroup['code'] ?>. <?= $itgroup['title'] ?>
                                            </h5>
                                            <div class="card-tools">
                                                <button class="btn btn-tool" id="btn_igroup_chart<?= $itgroup['id'] ?>">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                <?php copychart_toimage("igroup_chart" . $itgroup['id'], "btn_igroup_chart" . $itgroup['id']) ?>
                                            </div>
                                        </div>
                                        <div class="card-body p-3">
                                            <div class="text-center mb-3">
                                                <?php $itgroup_id = 'pie' . $itgroup['id']  ?>
                                                <svg id="<?= $itgroup_id ?>" height="180"></svg>
                                                <?php chart_pie($itgroup_id, $achieved_point, $toachieve_point) ?>
                                            </div>
                                            <div class="progress-group">
                                                <span class="progress-text">Achievement Progress</span>
                                                <span class="float-right"><b><?= $achieved_point ?></b>/<?= $target_point ?></span>
                                                <div class="progress progress-sm">
                                                    <div class="progress-bar bg-success" style="width: <?= round($achieved_pc) ?>%"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-light d-flex justify-content-between">
                                            <span class="text-success">
                                                <i class="fas fa-check-circle mr-1"></i>
                                                <?= round($achieved_pc) ?>%
                                            </span>
                                            <span class="text-warning">
                                                <i class="fas fa-hourglass-half mr-1"></i>
                                                <?= round($toachieve_pc) ?>%
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overall Groups Chart -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card card-primary card-outline" id="all_itgroups_barchart">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-chart-bar mr-1"></i> Group Comparison Analysis</h3>
                        <div class="card-tools">
                            <button class="btn btn-tool" id="btn_all_itgroups_barchart">
                                <i class="fas fa-download"></i> Export Chart
                            </button>
                            <?php copychart_toimage("all_itgroups_barchart", "btn_all_itgroups_barchart") ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="overall_itgroups_barchart" height="250"></canvas>
                        <?php chart_stacked_bar('overall_itgroups_barchart', $items_groups_labels, $items_groups_achieved, $items_groups_toachieve) ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Add recommendations section -->
<section class="content">
    <div class="container-fluid mb-4">
        <div class="card card-primary card-outline">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-lightbulb mr-1"></i> Recommendations</h3>
                <div class="card-tools">
                    <button id="aiAnalysisBtn" class="btn btn-info" onclick="runAiAnalysis()">
                        <i class="fas fa-robot mr-1"></i> AI Analysis
                        <span id="aiButtonSpinner" class="spinner-border spinner-border-sm ml-1" role="status" style="display: none;">
                            <span class="sr-only">Loading...</span>
                        </span>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="callout callout-info">
                            <h5><i class="fas fa-info-circle mr-1"></i> Key Observations</h5>
                            <p>Based on the assessment results, here are some key observations and recommendations:</p>
                        </div>

                        <?php if ($gap_pc_avg > 30): ?>
                            <div class="alert alert-warning">
                                <h5><i class="icon fas fa-exclamation-triangle"></i> Improvement Needed</h5>
                                <p>There's a significant gap (<?= round($gap_pc_avg) ?>%) between the target and achieved scores.
                                    Focus on improving the lower-performing areas highlighted in the charts above.</p>
                            </div>
                        <?php elseif ($gap_pc_avg > 10): ?>
                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> Good Progress</h5>
                                <p>You're making good progress with a gap of <?= round($gap_pc_avg) ?>%.
                                    Continue working on the areas that need improvement to reach your targets.</p>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-success">
                                <h5><i class="icon fas fa-check"></i> Excellent Performance</h5>
                                <p>Excellent work! You've achieved <?= round($achieved_pc_avg) ?>% of the targets.
                                    Maintain this performance and focus on continuous improvement.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div> <!--  -->

</section>

<section class="content">
    <div class="container-fluid">
        <!-- AI Analysis Results Container -->
        <div id="aiAnalysisResults" class="mt-3" style="display: none;">
            <div class="card card-outline card-info">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-robot mr-1"></i> AI Analysis Results
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" onclick="document.getElementById('aiAnalysisResults').style.display = 'none';">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="aiLoadingIndicator" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-3 text-muted">Analyzing data with AI...</p>
                    </div>
                    <div id="aiContent" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>
</section>



<!-- AI Analysis Implementation -->
<script>
    // Global function for the AI Analysis button
    function runAiAnalysis() {
        // Show results container and loading indicator
        document.getElementById('aiAnalysisResults').style.display = 'block';
        document.getElementById('aiLoadingIndicator').style.display = 'block';
        document.getElementById('aiContent').style.display = 'none';

        // Scroll to results
        document.getElementById('aiAnalysisResults').scrollIntoView({
            behavior: 'smooth'
        });

        // Show spinner on button
        document.getElementById('aiButtonSpinner').style.display = 'inline-block';

        // Disable button
        document.getElementById('aiAnalysisBtn').disabled = true;

        // Prepare the API request
        const apiKey = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
        const apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=' + apiKey;

        // Create a comprehensive prompt with more data
        const promptText = `Please provide a professional analysis of this assessment:

Plan: <?= isset($plan['code']) ? addslashes($plan['code']) : "" ?>. <?= isset($plan['title']) ? addslashes($plan['title']) : "" ?>
Exercise: <?= isset($assex['code']) ? addslashes($assex['code']) : "" ?>. <?= isset($assex['title']) ? addslashes($assex['title']) : "" ?>

Performance Summary:
- Achieved: <?= round($achieved_pc_avg, 1) ?>%
- Yet to Achieve: <?= round($gap_pc_avg, 1) ?>%
- Points Achieved: <?= $item_achieved_total ?> out of <?= $item_targets_total ?>

Please provide:
1. A detailed analysis of strengths and weaknesses
2. Specific recommendations for improvement areas
3. Prioritized action items to address the gaps
4. Expected outcomes if recommendations are followed`;

        // Create request data
        const requestData = {
            contents: [{
                parts: [{
                    text: promptText
                }]
            }]
        };

        // Use XMLHttpRequest
        const xhr = new XMLHttpRequest();
        xhr.open('POST', apiUrl, true);
        xhr.setRequestHeader('Content-Type', 'application/json');

        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                // Reset button state
                document.getElementById('aiButtonSpinner').style.display = 'none';
                document.getElementById('aiAnalysisBtn').disabled = false;

                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);

                        // Display results
                        if (response.candidates && response.candidates[0] && response.candidates[0].content) {
                            const text = response.candidates[0].content.parts[0].text;
                            displayFormattedResults(text);
                        } else {
                            displayError("Unexpected response format from the AI service.");
                        }
                    } catch (error) {
                        displayError("Error parsing response: " + error.message);
                    }
                } else {
                    let errorMsg = "Request failed with status: " + xhr.status;

                    if (xhr.responseText) {
                        try {
                            const errorJson = JSON.parse(xhr.responseText);
                            if (errorJson.error && errorJson.error.message) {
                                errorMsg = errorJson.error.message;
                            }
                        } catch (e) {
                            // Use the status text if we can't parse the error
                        }
                    }

                    displayError(errorMsg);
                }
            }
        };

        xhr.onerror = function() {
            document.getElementById('aiButtonSpinner').style.display = 'none';
            document.getElementById('aiAnalysisBtn').disabled = false;
            displayError("Network error occurred. Please check your internet connection.");
        };

        // Send request
        try {
            xhr.send(JSON.stringify(requestData));
        } catch (error) {
            document.getElementById('aiButtonSpinner').style.display = 'none';
            document.getElementById('aiAnalysisBtn').disabled = false;
            displayError("Error sending request: " + error.message);
        }
    }

    // Function to display formatted results
    function displayFormattedResults(text) {
        // Hide loading indicator
        document.getElementById('aiLoadingIndicator').style.display = 'none';

        // Format markdown to HTML
        let formattedText = formatMarkdown(text);

        // Insert formatted text
        const aiContent = document.getElementById('aiContent');
        aiContent.innerHTML = formattedText;
        aiContent.style.display = 'block';
    }

    // Function to display error
    function displayError(message) {
        // Hide loading indicator
        document.getElementById('aiLoadingIndicator').style.display = 'none';

        // Show error message
        const aiContent = document.getElementById('aiContent');
        aiContent.innerHTML = `
        <div class="alert alert-danger">
            <h5><i class="icon fas fa-exclamation-triangle"></i> Error</h5>
            <p>${message}</p>
        </div>
    `;
        aiContent.style.display = 'block';
    }

    // Function to format markdown to HTML
    function formatMarkdown(text) {
        // Create sections for each main part
        const sections = [{
                title: 'Analysis',
                icon: 'chart-line',
                pattern: /strengths and weaknesses/i
            },
            {
                title: 'Recommendations',
                icon: 'lightbulb',
                pattern: /recommendations/i
            },
            {
                title: 'Action Items',
                icon: 'tasks',
                pattern: /action items|prioritized/i
            },
            {
                title: 'Expected Outcomes',
                icon: 'bullseye',
                pattern: /expected outcomes/i
            }
        ];

        // Initial processing of markdown
        let html = '';
        let currentSection = null;

        // Process the text line by line
        const lines = text.split('\n');
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            // Check for section headers
            if (line.match(/^#+\s/)) {
                // Check if this is a section we recognize
                for (const section of sections) {
                    if (line.match(section.pattern)) {
                        currentSection = section;
                        html += `
                        <div class="card card-outline card-primary mb-3">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-${section.icon} mr-2"></i> ${section.title}
                                </h3>
                            </div>
                            <div class="card-body">
                    `;
                        break;
                    }
                }

                // If not a recognized section, just add as regular heading
                if (!currentSection || !line.match(currentSection.pattern)) {
                    const level = (line.match(/^#+/) || [''])[0].length;
                    const text = line.replace(/^#+\s+/, '');
                    html += `<h${level+2} class="mt-3 mb-2">${text}</h${level+2}>`;
                }
            }
            // Process list items with special styling
            else if (line.match(/^-\s+/)) {
                const listItem = line.replace(/^-\s+/, '');
                html += `<div class="d-flex mb-2">
                <div class="mr-2"><i class="fas fa-angle-right text-primary"></i></div>
                <div>${listItem}</div>
            </div>`;
            }
            // Process numbered list items
            else if (line.match(/^\d+\.\s+/)) {
                const listItem = line.replace(/^\d+\.\s+/, '');
                const number = (line.match(/^\d+/) || [''])[0];
                html += `<div class="d-flex mb-2">
                <div class="mr-2 bg-info text-white rounded-circle" style="width: 24px; height: 24px; text-align: center; line-height: 24px;">${number}</div>
                <div class="mt-1">${listItem}</div>
            </div>`;
            }
            // Check for closing a section
            else if (line === '' && currentSection) {
                // Check if the next non-empty line is a header
                let nextNonEmptyLine = '';
                for (let j = i + 1; j < lines.length; j++) {
                    if (lines[j].trim() !== '') {
                        nextNonEmptyLine = lines[j];
                        break;
                    }
                }

                // If next non-empty line is a heading, close the current section
                if (nextNonEmptyLine.match(/^#+\s/)) {
                    html += `</div></div>`;
                    currentSection = null;
                } else {
                    html += `<p></p>`;
                }
            }
            // Regular paragraph
            else if (line !== '') {
                html += `<p>${line}</p>`;
            }
        }

        // Close any open section
        if (currentSection) {
            html += `</div></div>`;
        }

        // Bold and italic formatting
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

        return `<div class="ai-analysis">${html}</div>`;
    }
</script>

<?= $this->endSection(); ?>