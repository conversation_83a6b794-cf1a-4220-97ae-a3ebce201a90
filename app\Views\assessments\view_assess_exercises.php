<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark"><i class="fas fa-clipboard-check mr-2"></i>Assessment Exercises</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard"><i class="fas fa-home"></i></a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>assessments_manager">Assessment Manager</a></li>
                    <li class="breadcrumb-item active">Exercises</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<!-- /.content-header -->

<section class="content">
    <div class="container-fluid">
        <!-- Action Bar -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="<?= base_url() ?>assessments_manager" class="btn btn-outline-secondary">
                        <i class="fa fa-arrow-left mr-1"></i> Back to Assessment Manager
                    </a>
                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#create_new">
                        <i class="fa fa-plus-circle mr-1"></i> New Exercise
                    </button>
                </div>
            </div>
        </div>

        <!-- Assessment Plan Info Card -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card bg-gradient-light">
                    <div class="card-body py-4">
                        <div class="row align-items-center">
                            <div class="col-md-4 text-center text-md-right border-right">
                                <h2 class="text-primary font-weight-bold mb-0" style="font-family: 'Poppins', sans-serif; letter-spacing: -0.5px;">
                                    Assessment Plan
                                </h2>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-calendar-check mr-1"></i> <?= date('F j, Y') ?>
                                </p>
                            </div>
                            <div class="col-md-8 pl-md-4">
                                <div class="d-flex mb-2">
                                    <div class="mr-3">
                                        <span class="bg-primary p-2 rounded-circle">
                                            <i class="fas fa-clipboard-list text-white"></i>
                                        </span>
                                    </div>
                                    <div>
                                        <h6 class="text-uppercase text-muted mb-1" style="font-size: 0.8rem; letter-spacing: 1px;">PLAN DETAILS</h6>
                                        <h5 class="font-weight-bold mb-0" style="font-size: 1.1rem;"><?= $plan['code'] ?>. <?= $plan['title'] ?></h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exercises List -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-list-alt mr-1"></i> Exercises List</h3>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($data)) : ?>
                            <div class="text-center py-5">
                                <div class="icon-box bg-light d-inline-flex mb-3">
                                    <i class="fas fa-clipboard-list text-muted"></i>
                                </div>
                                <h5 class="text-muted mb-3">No exercises found for this assessment plan</h5>
                                <button type="button" class="btn btn-primary px-4" data-toggle="modal" data-target="#create_new">
                                    <i class="fa fa-plus-circle mr-1"></i> Create First Exercise
                                </button>
                            </div>
                        <?php else : ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="thead-light">
                                        <tr>
                                            <th style="width: 10%">Code</th>
                                            <th style="width: 35%">Title</th>
                                            <th style="width: 25%">Duration</th>
                                            <th style="width: 10%">Status</th>
                                            <th style="width: 20%">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($data as $row) : ?>
                                            <tr class="exercise-row hover-effect" style="cursor: pointer;" data-href="<?= base_url() ?>open_assess_exercise/<?= $row['ucode'] ?>">
                                                <td class="font-weight-bold"><?= $row['code'] ?></td>
                                                <td><?= $row['title'] ?></td>
                                                <td>
                                                    <i class="far fa-calendar-alt text-muted mr-1"></i> 
                                                    <?= dateforms($row['date_from']) ?> - <?= dateforms($row['date_to']) ?>
                                                </td>
                                                <td>
                                                    <?php if ($row['status'] == 'Active') : ?>
                                                        <span class="badge badge-success px-3 py-2">
                                                            <i class="fas fa-check-circle mr-1"></i> <?= $row['status'] ?>
                                                        </span>
                                                    <?php else : ?>
                                                        <span class="badge badge-secondary px-3 py-2">
                                                            <i class="fas fa-pause-circle mr-1"></i> <?= $row['status'] ?>
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a class="btn btn-info btn-sm" href="<?= base_url() ?>open_assess_exercise/<?= $row['ucode'] ?>" data-toggle="tooltip" title="Open Exercise">
                                                            <i class="fa fa-arrow-right"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-warning btn-sm modal-trigger" data-toggle="modal" data-target="#edit_plan<?= $row['id'] ?>" title="Edit Exercise">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-danger btn-sm modal-trigger" data-toggle="modal" data-target="#delete<?= $row['id'] ?>" title="Delete Exercise">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer bg-light">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle text-primary mr-2"></i>
                            <span>Click on any row to view exercise details</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal - Create New Exercise -->
    <div class="modal fade" id="create_new" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title text-white">
                        <i class="fa fa-plus-circle mr-1"></i> Create New Exercise
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <?= form_open_multipart('create_assessment_exercise') ?>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="title" class="font-weight-bold">Exercise Title</label>
                        <input type="text" name="title" id="title" class="form-control" placeholder="Enter exercise title" aria-describedby="titleHelp" required>
                        <small id="titleHelp" class="form-text text-muted">Provide a descriptive name for this exercise</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_from" class="font-weight-bold">Start Date</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="far fa-calendar-alt"></i></span>
                                    </div>
                                    <input type="date" name="date_from" id="date_from" class="form-control" aria-describedby="dateFromHelp" required>
                                </div>
                                <small id="dateFromHelp" class="form-text text-muted">Exercise beginning date</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_to" class="font-weight-bold">End Date</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="far fa-calendar-alt"></i></span>
                                    </div>
                                    <input type="date" name="date_to" id="date_to" class="form-control" aria-describedby="dateToHelp" required>
                                </div>
                                <small id="dateToHelp" class="form-text text-muted">Exercise completion date</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <input type="hidden" name="plan_id" value="<?= $plan['id'] ?>">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fa fa-times mr-1"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-paper-plane mr-1"></i> Create Exercise
                    </button>
                </div>
                <?= form_close() ?>
            </div>
        </div>
    </div>

    <!-- Modals for Edit and Delete -->
    <?php foreach ($data as $row) : ?>
        <!-- Modal - Delete Exercise -->
        <div class="modal fade" id="delete<?= $row['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header bg-danger">
                        <h5 class="modal-title text-white">
                            <i class="fa fa-exclamation-triangle mr-1"></i> Confirm Deletion
                        </h5>
                        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?= form_open('delete_assessment_exercise') ?>
                    <div class="modal-body text-center">
                        <div class="mb-4">
                            <div class="icon-box bg-danger d-inline-flex mb-3">
                                <i class="fas fa-trash-alt text-white"></i>
                            </div>
                            <h4 class="mb-2">Are you sure?</h4>
                            <p class="text-muted">This action cannot be undone</p>
                        </div>
                        <div class="alert alert-warning">
                            <strong>Exercise: </strong> <?= $row['code'] ?>: <?= $row['title'] ?>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="id" value="<?= $row['id'] ?>">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">
                            <i class="fa fa-times mr-1"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fa fa-trash mr-1"></i> Confirm Delete
                        </button>
                    </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <!-- Modal - Edit Exercise -->
        <div class="modal fade" id="edit_plan<?= $row['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header bg-warning">
                        <h5 class="modal-title">
                            <i class="fas fa-edit mr-1"></i> Edit Exercise
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?= form_open_multipart('update_assessment_exercise') ?>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="title<?= $row['id'] ?>" class="font-weight-bold">Exercise Title</label>
                            <input type="text" name="title" id="title<?= $row['id'] ?>" class="form-control" placeholder="Enter exercise title" value="<?= $row['title'] ?>" required>
                            <small class="form-text text-muted">Provide a descriptive name for this exercise</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="date_from<?= $row['id'] ?>" class="font-weight-bold">Start Date</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="far fa-calendar-alt"></i></span>
                                        </div>
                                        <input type="date" name="date_from" id="date_from<?= $row['id'] ?>" class="form-control" value="<?= $row['date_from'] ?>" required>
                                    </div>
                                    <small class="form-text text-muted">Exercise beginning date</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="date_to<?= $row['id'] ?>" class="font-weight-bold">End Date</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="far fa-calendar-alt"></i></span>
                                        </div>
                                        <input type="date" name="date_to" id="date_to<?= $row['id'] ?>" class="form-control" value="<?= $row['date_to'] ?>" required>
                                    </div>
                                    <small class="form-text text-muted">Exercise completion date</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="status<?= $row['id'] ?>" class="font-weight-bold">Status</label>
                            <select name="status" id="status<?= $row['id'] ?>" class="form-control">
                                <option value="Active" <?= ($row['status'] == 'Active') ? 'selected' : '' ?>>Active</option>
                                <option value="Inactive" <?= ($row['status'] == 'Inactive') ? 'selected' : '' ?>>Inactive</option>
                            </select>
                            <small class="form-text text-muted">Set the current status of this exercise</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="id" value="<?= $row['id'] ?>">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">
                            <i class="fa fa-times mr-1"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-warning">
                            <i class="fa fa-save mr-1"></i> Update Exercise
                        </button>
                    </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</section>

<script>
    // Simple script to make rows clickable
    $(document).ready(function() {
        $(".exercise-row").click(function(e) {
            // Don't trigger when clicking buttons or links
            if (!$(e.target).closest('button').length && !$(e.target).closest('a').length) {
                window.location = $(this).data("href");
            }
        });
    });
</script>

<?= $this->endSection(); ?>