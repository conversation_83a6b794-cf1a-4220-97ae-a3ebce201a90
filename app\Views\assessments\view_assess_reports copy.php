<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>

<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.1/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">

            <div class="col-sm-6">
                <h1> <i class="fas fa-line-chart"></i> Assessment Report Items</h1>
                <h5 class="m-0"></h5>

            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item ">...</li>
                    <li class="breadcrumb-item">Report Groups</li>
                    <li class="breadcrumb-item active">Report Items</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">
    <div class="row">
        <div class="col-md-12">
            <a href="<?= base_url() ?>view_assess_report_groupings/<?= $assex['ucode'] ?>" class="btn btn-outline-dark "> <i class="fa fa-reply" aria-hidden="true"></i> Back</a>

        </div>
    </div>
    <!-- ./col -->

    <div class="row pt-2">
        <div class="col-md-12 mb-2">
            <ul class="list-group">
                <!--tips: add .list-group-flush to the .list-group to remove some borders and rounded corners-->
                <li class="list-group-item "><b class=" float-left">Exercise: </b> <span class=" float-right"><?= $assex['code'] ?>. <?= $assex['title'] ?> </span> </li>

            </ul>
        </div>

    </div>
    <!-- ./row -->
    <!-- overall top level -->
    <div class="row">
        <div class="col-md-12">
            <h5>Overall Charts</h5>
        </div>
        <div class="col-md-7">
            <div class="card">
                <div class="card-body">
                    <canvas id="myradar_gap_chart"></canvas>

                    <!-- gap data collection -->
                    <?php
                    $item_targets_total = $item_achieved_total = 0;
                    $item_labels = $item_targets = $item_achieved = "";
                    foreach ($items as $item) {
                        $item_labels .= "'" . $item['code'] . "',";
                        $item_targets .= $assex['max_rate'] . ",";
                        $item_achieved .= $item['score'] . ",";
                        $item_targets_total += $assex['max_rate'];
                        $item_achieved_total += $item['score'];
                    }
                    ?>

                    <script>
                        var ctx = document.getElementById('myradar_gap_chart').getContext('2d');
                        var myRadarChart = new Chart(ctx, {
                            type: 'radar',
                            data: {
                                labels: [<?= $item_labels ?>],
                                datasets: [{
                                    label: 'Target',
                                    data: [<?= $item_targets ?>],
                                    fill: true,
                                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                    borderColor: 'rgb(255, 99, 132)',
                                    pointBackgroundColor: 'rgb(255, 99, 132)',
                                    pointBorderColor: '#fff',
                                    pointHoverBackgroundColor: '#fff',
                                    pointHoverBorderColor: 'rgb(255, 99, 132)'
                                }, {
                                    label: 'Achieved',
                                    data: [<?= $item_achieved ?>],
                                    fill: true,
                                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                    borderColor: 'rgb(54, 162, 235)',
                                    pointBackgroundColor: 'rgb(54, 162, 235)',
                                    pointBorderColor: '#fff',
                                    pointHoverBackgroundColor: '#fff',
                                    pointHoverBorderColor: 'rgb(54, 162, 235)'
                                }]
                            },
                            options: {
                                elements: {
                                    line: {
                                        borderWidth: 3
                                    }
                                }
                            },
                        });
                    </script>


                </div>
                <div class="card-footer">
                    <?php
                    //calcuate average totals
                    $targets_total_avg = $item_targets_total / count($items);
                    $achieved_total_avg = $item_achieved_total / count($items);

                    //calculate average gaps
                    $gap_avg = $targets_total_avg - $achieved_total_avg;
                    $gap_pc_avg = (($gap_avg / $targets_total_avg) * 100);
                    $achieved_pc_avg = (($achieved_total_avg / $targets_total_avg) * 100);
                    ?>
                    Chart showing the over Gap between Achieved and Proposed Target <br>
                    <div class="row d-flex justify-content-between ">
                        <b>Percentage Average Achievement: <?= round($achieved_pc_avg, 1)  ?>%</b>
                        <b>Percentage Average Gap: <?= round($gap_pc_avg, 1)  ?>%</b>
                    </div>

                </div>


            </div>
        </div>
        <!-- ./col -->
        <div class="col-md-5">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <div style="width:400px;">
                                <canvas id="myPieChart"></canvas>
                            </div>

                            <script>
                                var ctx = document.getElementById('myPieChart').getContext('2d');
                                var myPieChart = new Chart(ctx, {
                                    type: 'pie',
                                    data: {
                                        labels: ['Red', 'Blue'],
                                        datasets: [{
                                            label: 'Total Data',
                                            data: [<?= round($gap_pc_avg, 1) ?>, <?= round($achieved_pc_avg, 1) ?>],
                                            backgroundColor: [
                                                'rgba(255, 99, 132, 0.6)',
                                                'rgba(54, 162, 235, 0.6)',

                                            ],
                                            borderColor: [
                                                'rgba(255, 99, 132, 1)',
                                                'rgba(54, 162, 235, 1)',
                                                'rgba(255, 206, 86, 1)',
                                                'rgba(75, 192, 192, 1)',
                                                'rgba(153, 102, 255, 1)',
                                                'rgba(255, 159, 64, 1)'
                                            ],
                                            borderWidth: 1
                                        }]
                                    },
                                    options: {
                                        responsive: true,
                                        plugins: {
                                            legend: {
                                                position: 'top',
                                            },
                                            title: {
                                                display: true,
                                                text: 'Pie Chart Example'
                                            }
                                        }
                                    },
                                });
                            </script>
                        </div>
                        <div class="card-footer">

                        </div>
                    </div>
                    <!-- ./card -->
                </div>
                <!-- ./col -->

                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.1/chart.min.js"></script>
                            <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
                            <div style="width:400px;">
                                <canvas id="myPieChart2"></canvas>
                            </div>
                            <script>
                                var ctx = document.getElementById('myPieChart2').getContext('2d');
                                var myPieChart = new Chart(ctx, {
                                    type: 'pie',
                                    data: {
                                        labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],
                                        datasets: [{
                                            label: 'My First Dataset',
                                            data: [12, 19, 3, 5, 2, 3],
                                            backgroundColor: [
                                                'rgba(255, 99, 132, 0.6)',
                                                'rgba(54, 162, 235, 0.6)',
                                                'rgba(255, 206, 86, 0.6)',
                                                'rgba(75, 192, 192, 0.6)',
                                                'rgba(153, 102, 255, 0.6)',
                                                'rgba(255, 159, 64, 0.6)'
                                            ],
                                            borderColor: [
                                                'rgba(255, 99, 132, 1)',
                                                'rgba(54, 162, 235, 1)',
                                                'rgba(255, 206, 86, 1)',
                                                'rgba(75, 192, 192, 1)',
                                                'rgba(153, 102, 255, 1)',
                                                'rgba(255, 159, 64, 1)'
                                            ],
                                            borderWidth: 1
                                        }]
                                    },
                                    options: {
                                        responsive: true,
                                        plugins: {
                                            datalabels: {
                                                color: '#fff',
                                                formatter: (value, ctx) => {
                                                    return value;
                                                }
                                            }
                                        }
                                    }
                                });
                            </script>

                        </div>
                        <div class="card-footer">

                        </div>
                    </div>
                    <!-- ./card -->
                </div>
                <!-- ./col -->

            </div>

        </div>

    </div>

</section>

</body>


<?= $this->endSection() ?>