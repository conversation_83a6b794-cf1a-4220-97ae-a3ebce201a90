<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark"><i class="fas fa-table mr-2"></i>Assessment Summary Tables</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard"><i class="fas fa-home"></i></a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>view_assess_reports/<?= $assex['ucode'] ?>">Report Dashboard</a></li>
                    <li class="breadcrumb-item active">Summary Tables</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<!-- /.content-header -->

<section class="content">
    <div class="container-fluid">
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="<?= base_url() ?>view_assess_reports/<?= $assex['ucode'] ?>" class="btn btn-outline-secondary">
                        <i class="fa fa-arrow-left mr-1"></i> Back to Report Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Assessment Info Card -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card bg-gradient-light">
                    <div class="card-body py-4">
                        <div class="row align-items-center">
                            <div class="col-md-4 text-center text-md-right border-right">
                                <h2 class="text-primary font-weight-bold mb-0" style="font-family: 'Poppins', sans-serif; letter-spacing: -0.5px;">
                                    Summary Tables
                                </h2>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-calendar-check mr-1"></i> <?= date('F j, Y') ?>
                                </p>
                            </div>
                            <div class="col-md-4 pl-md-4">
                                <div class="d-flex mb-2">
                                    <div class="mr-3">
                                        <span class="bg-primary p-2 rounded-circle">
                                            <i class="fas fa-clipboard-list text-white"></i>
                                        </span>
                                    </div>
                                    <div>
                                        <h6 class="text-uppercase text-muted mb-1" style="font-size: 0.8rem; letter-spacing: 1px;">PLAN</h6>
                                        <h5 class="font-weight-bold mb-0" style="font-size: 1.1rem;"><?= $plan['code'] ?>. <?= $plan['title'] ?></h5>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 pl-md-4">
                                <div class="d-flex mb-2">
                                    <div class="mr-3">
                                        <span class="bg-info p-2 rounded-circle">
                                            <i class="fas fa-tasks text-white"></i>
                                        </span>
                                    </div>
                                    <div>
                                        <h6 class="text-uppercase text-muted mb-1" style="font-size: 0.8rem; letter-spacing: 1px;">EXERCISE</h6>
                                        <h5 class="font-weight-bold mb-0" style="font-size: 1.1rem;"><?= $assex['code'] ?>. <?= $assex['title'] ?></h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Items Groups Summary Table -->
        <div class="row mb-4" id="items_groups_section">
            <div class="col-md-12">
                <div class="card card-primary card-outline">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h3 class="card-title">
                            <i class="fas fa-layer-group mr-1"></i> Items Groups Summary
                        </h3>
                        <div class="card-tools">
                            <button class="btn btn-tool" id="btn_overall_charts">
                                <i class="fas fa-download"></i> Export Table
                            </button>
                            <?php copychart_toimage('items_groups_section', 'btn_overall_charts') ?>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th style="width: 10%">Code</th>
                                        <th style="width: 50%">Title</th>
                                        <th style="width: 20%">Achieved</th>
                                        <th style="width: 20%">Target</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $total_achieved = $total_target = 0;
                                    foreach ($items_groups as $igroup) :
                                        //calculate achieved and target
                                        $achieved = $target = 0;
                                        foreach ($items as $item) {
                                            if ($item['assess_items_groups_id'] == $igroup['id']) {
                                                $achieved += $item['score'];
                                                $target += $assex['max_rate'];
                                            }
                                        }
                                        
                                        // Calculate percentage for progress bar
                                        $percentage = ($target > 0) ? round(($achieved / $target) * 100) : 0;
                                        $progressColorClass = ($percentage >= 70) ? 'bg-success' : (($percentage >= 40) ? 'bg-warning' : 'bg-danger');
                                    ?>
                                        <tr>
                                            <td class="font-weight-bold"><?= $igroup['code'] ?></td>
                                            <td><?= $igroup['title'] ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="mr-2"><?= $achieved ?></span>
                                                    <div class="progress w-100" style="height: 6px;">
                                                        <div class="progress-bar <?= $progressColorClass ?>" role="progressbar" style="width: <?= $percentage ?>%" aria-valuenow="<?= $percentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?= $target ?></td>
                                            <?php
                                            $total_achieved += $achieved;
                                            $total_target += $target;
                                            ?>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="bg-light">
                                    <tr class="font-weight-bold">
                                        <td colspan="2" class="text-right">Total:</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="mr-2"><?= $total_achieved ?></span>
                                                <div class="progress w-100" style="height: 6px;">
                                                    <div class="progress-bar bg-primary" role="progressbar" style="width: <?= ($total_target > 0) ? round(($total_achieved / $total_target) * 100) : 0 ?>%" aria-valuenow="<?= ($total_target > 0) ? round(($total_achieved / $total_target) * 100) : 0 ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?= $total_target ?></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer bg-white">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="small-box bg-success py-2">
                                    <div class="inner">
                                        <h4><?= ($total_target > 0) ? round(($total_achieved / $total_target) * 100) : 0 ?>%</h4>
                                        <p class="mb-0">Overall Achievement</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="small-box bg-info py-2">
                                    <div class="inner">
                                        <h4><?= $total_achieved ?></h4>
                                        <p class="mb-0">Total Points Achieved</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="small-box bg-primary py-2">
                                    <div class="inner">
                                        <h4><?= $total_target ?></h4>
                                        <p class="mb-0">Total Target Points</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-bullseye"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports Groups Summary Table -->
        <div class="row mb-4" id="report_groups_section">
            <div class="col-md-12">
                <div class="card card-primary card-outline">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h3 class="card-title">
                            <i class="fas fa-file-alt mr-1"></i> Reports Groups Summary
                        </h3>
                        <div class="card-tools">
                            <button class="btn btn-tool" id="btn_item_groups_charts">
                                <i class="fas fa-download"></i> Export Table
                            </button>
                            <?php copychart_toimage('report_groups_section', 'btn_item_groups_charts') ?>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th style="width: 10%">Code</th>
                                        <th style="width: 50%">Title</th>
                                        <th style="width: 20%">Achieved</th>
                                        <th style="width: 20%">Target</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $grand_total_achieved = $grand_total_target = 0;
                                    foreach ($report_groups as $rgroup) :
                                        $count = $achieved_report = $target_report = 0;
                                    ?>
                                        <tr class="font-weight-bold" style="background-color: <?= $rgroup['bg_color'] ?>;">
                                            <td class="text-dark"><?= $rgroup['code'] ?></td>
                                            <td class="text-dark"><?= $rgroup['title'] ?></td>
                                            <td class="text-dark">
                                                <?php
                                                foreach ($report_items as $ritem) {
                                                    $achieved_items = $target_items = 0;
                                                    if ($ritem['assess_report_groups_id'] == $rgroup['id']) {
                                                        foreach ($items as $item) {
                                                            if ($item['assess_items_groups_id'] == $ritem['assess_items_groups_id']) {
                                                                $achieved_items += $item['score'];
                                                                $target_items += $assex['max_rate'];
                                                            }
                                                        }
                                                    }
                                                    $achieved_report += $achieved_items;
                                                    $target_report += $target_items;
                                                }
                                                // Add to grand totals
                                                $grand_total_achieved += $achieved_report;
                                                $grand_total_target += $target_report;
                                                ?>
                                                <?= $achieved_report ?>
                                            </td>
                                            <td class="text-dark"><?= $target_report ?></td>
                                        </tr>
                                        <?php
                                        foreach ($report_items as $ritem) {
                                            $achieved_items = $target_items = 0;
                                            if ($ritem['assess_report_groups_id'] == $rgroup['id']) {
                                                foreach ($items as $item) {
                                                    if ($item['assess_items_groups_id'] == $ritem['assess_items_groups_id']) {
                                                        $achieved_items += $item['score'];
                                                        $target_items += $assex['max_rate'];
                                                    }
                                                }
                                                
                                                // Calculate percentage for progress bar
                                                $percentage = ($target_items > 0) ? round(($achieved_items / $target_items) * 100) : 0;
                                                $progressColorClass = ($percentage >= 70) ? 'bg-success' : (($percentage >= 40) ? 'bg-warning' : 'bg-danger');
                                        ?>
                                                <tr>
                                                    <td class="pl-4"><?= $ritem['code'] ?></td>
                                                    <td><?= $ritem['title'] ?></td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <span class="mr-2"><?= $achieved_items ?></span>
                                                            <div class="progress w-100" style="height: 6px;">
                                                                <div class="progress-bar <?= $progressColorClass ?>" role="progressbar" style="width: <?= $percentage ?>%" aria-valuenow="<?= $percentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td><?= $target_items ?></td>
                                                </tr>
                                        <?php
                                            }
                                        }
                                        ?>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="bg-light">
                                    <tr class="font-weight-bold">
                                        <td colspan="2" class="text-right">Grand Total:</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="mr-2"><?= $grand_total_achieved ?></span>
                                                <div class="progress w-100" style="height: 6px;">
                                                    <div class="progress-bar bg-primary" role="progressbar" style="width: <?= ($grand_total_target > 0) ? round(($grand_total_achieved / $grand_total_target) * 100) : 0 ?>%" aria-valuenow="<?= ($grand_total_target > 0) ? round(($grand_total_achieved / $grand_total_target) * 100) : 0 ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?= $grand_total_target ?></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- AI Analysis Section -->
<section class="content">
    <div class="container-fluid mb-4">
        <div class="card card-primary card-outline">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-robot mr-1"></i> AI Summary Analysis</h3>
                <div class="card-tools">
                    <button id="aiAnalysisBtn" class="btn btn-info" onclick="runAiAnalysis()">
                        <i class="fas fa-robot mr-1"></i> Run AI Analysis
                        <span id="aiButtonSpinner" class="spinner-border spinner-border-sm ml-1" role="status" style="display: none;">
                            <span class="sr-only">Loading...</span>
                        </span>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <p>Click the button above to generate an AI-powered analysis of the summary tables data. This analysis will provide insights, recommendations, and improvement strategies based on the assessment summary results.</p>
            </div>
        </div>
    </div>
</section>

<!-- AI Analysis Results Container -->
<section class="content">
    <div class="container-fluid">
        <div id="aiAnalysisResults" class="mt-3" style="display: none;">
            <div class="card card-outline card-info">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-robot mr-1"></i> AI Analysis Results
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" onclick="document.getElementById('aiAnalysisResults').style.display = 'none';">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="aiLoadingIndicator" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-3 text-muted">Analyzing data with AI...</p>
                    </div>
                    <div id="aiContent" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- AI Analysis Implementation -->
<script>
// Global function for the AI Analysis button
function runAiAnalysis() {
    // Show results container and loading indicator
    document.getElementById('aiAnalysisResults').style.display = 'block';
    document.getElementById('aiLoadingIndicator').style.display = 'block';
    document.getElementById('aiContent').style.display = 'none';
    
    // Scroll to results
    document.getElementById('aiAnalysisResults').scrollIntoView({
        behavior: 'smooth'
    });
    
    // Show spinner on button
    document.getElementById('aiButtonSpinner').style.display = 'inline-block';
    
    // Disable button
    document.getElementById('aiAnalysisBtn').disabled = true;
    
    // Prepare the API request
    const apiKey = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
    const apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=' + apiKey;
    
    // Create a comprehensive prompt with more data - including summary tables information
    const promptText = `Please provide a professional analysis of this assessment based on summary tables data:

Plan: <?= isset($plan['code']) ? addslashes($plan['code']) : "" ?>. <?= isset($plan['title']) ? addslashes($plan['title']) : "" ?>
Exercise: <?= isset($assex['code']) ? addslashes($assex['code']) : "" ?>. <?= isset($assex['title']) ? addslashes($assex['title']) : "" ?>

Performance Summary:
- Items Groups Total: <?= $total_achieved ?> out of <?= $total_target ?> (<?= ($total_target > 0) ? round(($total_achieved / $total_target) * 100) : 0 ?>% achieved)
- Report Groups Total: <?= $grand_total_achieved ?> out of <?= $grand_total_target ?> (<?= ($grand_total_target > 0) ? round(($grand_total_achieved / $grand_total_target) * 100) : 0 ?>% achieved)

Item Groups Summary:
<?php foreach ($items_groups as $igroup): 
    $achieved = $target = 0;
    foreach ($items as $item) {
        if ($item['assess_items_groups_id'] == $igroup['id']) {
            $achieved += $item['score'];
            $target += $assex['max_rate'];
        }
    }
    $percentage = ($target > 0) ? round(($achieved / $target) * 100) : 0;
?>
- Group: <?= addslashes($igroup['code']) ?> - <?= addslashes($igroup['title']) ?>: <?= $achieved ?>/<?= $target ?> (<?= $percentage ?>% achieved)
<?php endforeach; ?>

Report Groups Summary:
<?php foreach ($report_groups as $rgroup): 
    $achieved_report = $target_report = 0;
    foreach ($report_items as $ritem) {
        if ($ritem['assess_report_groups_id'] == $rgroup['id']) {
            foreach ($items as $item) {
                if ($item['assess_items_groups_id'] == $ritem['assess_items_groups_id']) {
                    $achieved_report += $item['score'];
                    $target_report += $assex['max_rate'];
                }
            }
        }
    }
    $percentage = ($target_report > 0) ? round(($achieved_report / $target_report) * 100) : 0;
?>
- Report Group: <?= addslashes($rgroup['code']) ?> - <?= addslashes($rgroup['title']) ?>: <?= $achieved_report ?>/<?= $target_report ?> (<?= $percentage ?>% achieved)
<?php endforeach; ?>

Please provide:
1. A detailed analysis of strengths and weaknesses based on the summary tables
2. Comparison between different item groups and report groups performance
3. Specific recommendations for improvement in low-performing areas
4. Prioritized action items to address performance gaps
5. Expected outcomes if recommendations are followed`;
    
    // Create request data
    const requestData = {
        contents: [{
            parts: [{
                text: promptText
            }]
        }]
    };
    
    // Use XMLHttpRequest
    const xhr = new XMLHttpRequest();
    xhr.open('POST', apiUrl, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            // Reset button state
            document.getElementById('aiButtonSpinner').style.display = 'none';
            document.getElementById('aiAnalysisBtn').disabled = false;
            
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    
                    // Display results
                    if (response.candidates && response.candidates[0] && response.candidates[0].content) {
                        const text = response.candidates[0].content.parts[0].text;
                        displayFormattedResults(text);
                    } else {
                        displayError("Unexpected response format from the AI service.");
                    }
                } catch (error) {
                    displayError("Error parsing response: " + error.message);
                }
            } else {
                let errorMsg = "Request failed with status: " + xhr.status;
                
                if (xhr.responseText) {
                    try {
                        const errorJson = JSON.parse(xhr.responseText);
                        if (errorJson.error && errorJson.error.message) {
                            errorMsg = errorJson.error.message;
                        }
                    } catch (e) {
                        // Use the status text if we can't parse the error
                    }
                }
                
                displayError(errorMsg);
            }
        }
    };
    
    xhr.onerror = function() {
        document.getElementById('aiButtonSpinner').style.display = 'none';
        document.getElementById('aiAnalysisBtn').disabled = false;
        displayError("Network error occurred. Please check your internet connection.");
    };
    
    // Send request
    try {
        xhr.send(JSON.stringify(requestData));
    } catch (error) {
        document.getElementById('aiButtonSpinner').style.display = 'none';
        document.getElementById('aiAnalysisBtn').disabled = false;
        displayError("Error sending request: " + error.message);
    }
}

// Function to display formatted results
function displayFormattedResults(text) {
    // Hide loading indicator
    document.getElementById('aiLoadingIndicator').style.display = 'none';
    
    // Format markdown to HTML
    let formattedText = formatMarkdown(text);
    
    // Insert formatted text
    const aiContent = document.getElementById('aiContent');
    aiContent.innerHTML = formattedText;
    aiContent.style.display = 'block';
}

// Function to display error
function displayError(message) {
    // Hide loading indicator
    document.getElementById('aiLoadingIndicator').style.display = 'none';
    
    // Show error message
    const aiContent = document.getElementById('aiContent');
    aiContent.innerHTML = `
        <div class="alert alert-danger">
            <h5><i class="icon fas fa-exclamation-triangle"></i> Error</h5>
            <p>${message}</p>
        </div>
    `;
    aiContent.style.display = 'block';
}

// Function to format markdown to HTML
function formatMarkdown(text) {
    // Create sections for each main part
    const sections = [{
            title: 'Analysis',
            icon: 'chart-line',
            pattern: /strengths and weaknesses|analysis/i
        },
        {
            title: 'Comparison',
            icon: 'balance-scale',
            pattern: /comparison|compare/i
        },
        {
            title: 'Recommendations',
            icon: 'lightbulb',
            pattern: /recommendations/i
        },
        {
            title: 'Action Items',
            icon: 'tasks',
            pattern: /action items|prioritized/i
        },
        {
            title: 'Expected Outcomes',
            icon: 'bullseye',
            pattern: /expected outcomes/i
        }
    ];
    
    // Initial processing of markdown
    let html = '';
    let currentSection = null;
    
    // Process the text line by line
    const lines = text.split('\n');
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        // Check for section headers
        if (line.match(/^#+\s/)) {
            // Check if this is a section we recognize
            for (const section of sections) {
                if (line.match(section.pattern)) {
                    currentSection = section;
                    html += `
                        <div class="card card-outline card-primary mb-3">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-${section.icon} mr-2"></i> ${section.title}
                                </h3>
                            </div>
                            <div class="card-body">
                    `;
                    break;
                }
            }
            
            // If not a recognized section, just add as regular heading
            if (!currentSection || !line.match(currentSection.pattern)) {
                const level = (line.match(/^#+/) || [''])[0].length;
                const text = line.replace(/^#+\s+/, '');
                html += `<h${level+2} class="mt-3 mb-2">${text}</h${level+2}>`;
            }
        } 
        // Process list items with special styling
        else if (line.match(/^-\s+/)) {
            const listItem = line.replace(/^-\s+/, '');
            html += `<div class="d-flex mb-2">
                <div class="mr-2"><i class="fas fa-angle-right text-primary"></i></div>
                <div>${listItem}</div>
            </div>`;
        }
        // Process numbered list items
        else if (line.match(/^\d+\.\s+/)) {
            const listItem = line.replace(/^\d+\.\s+/, '');
            const number = (line.match(/^\d+/) || [''])[0];
            html += `<div class="d-flex mb-2">
                <div class="mr-2 bg-info text-white rounded-circle" style="width: 24px; height: 24px; text-align: center; line-height: 24px;">${number}</div>
                <div class="mt-1">${listItem}</div>
            </div>`;
        }
        // Check for closing a section
        else if (line === '' && currentSection) {
            // Check if the next non-empty line is a header
            let nextNonEmptyLine = '';
            for (let j = i + 1; j < lines.length; j++) {
                if (lines[j].trim() !== '') {
                    nextNonEmptyLine = lines[j];
                    break;
                }
            }
            
            // If next non-empty line is a heading, close the current section
            if (nextNonEmptyLine.match(/^#+\s/)) {
                html += `</div></div>`;
                currentSection = null;
            } else {
                html += `<p></p>`;
            }
        }
        // Regular paragraph
        else if (line !== '') {
            html += `<p>${line}</p>`;
        }
    }
    
    // Close any open section
    if (currentSection) {
        html += `</div></div>`;
    }
    
    // Bold and italic formatting
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    return `<div class="ai-analysis">${html}</div>`;
}
</script>

<?= $this->endSection(); ?>