{"url": "http://localhost/part/index.php/view_assess_report_groupings/ASSEX66108dae0cdd61712360878", "method": "GET", "isAJAX": false, "startTime": **********.723591, "totalTime": 174.9, "totalMemory": "7.438", "segmentDuration": 25, "segmentCount": 7, "CI_VERSION": "4.3.2", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.765239, "duration": 0.*****************}, {"name": "Routing", "component": "Timer", "start": **********.812123, "duration": 0.00013303756713867188}, {"name": "Before Filters", "component": "Timer", "start": **********.816625, "duration": 0.*****************}, {"name": "Controller", "component": "Timer", "start": **********.835887, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.83589, "duration": 0.*****************}, {"name": "After Filters", "component": "Timer", "start": **********.898556, "duration": 0.0007030963897705078}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(4 total Queries, 4 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `ucode` = &#039;ASSEX66108dae0cdd61712360878&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:268", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:656", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Assessments.php:558", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Assessments->view_assess_report_groupings()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Assessments.php:558", "qid": "00ab1bec3ea1d000bf7d100422dca952"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_plans`\n<strong>WHERE</strong> `id` = &#039;2&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:268", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:656", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Assessments.php:559", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Assessments->view_assess_report_groupings()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Assessments.php:559", "qid": "5c2be22ff07a66a80060472891c5cbeb"}, {"hover": "", "class": "", "duration": "0.45 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_items_groups`\n<strong>WHERE</strong> `assess_exercise_id` = &#039;15&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:203", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:557", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Assessments.php:560", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Assessments->view_assess_report_groupings()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Assessments.php:560", "qid": "6633e49a5f687f15bc2ccb5442beac6c"}, {"hover": "", "class": "", "duration": "0.5 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_report_groups`\n<strong>WHERE</strong> `assess_exercise_id` = &#039;15&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:203", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:557", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Assessments.php:561", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Assessments->view_assess_report_groupings()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Assessments.php:561", "qid": "5a47a24fae94df99a02133432bb50e99"}]}, "badgeValue": 4, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.885454, "duration": "0.002775"}, {"name": "Query", "component": "Database", "start": **********.889518, "duration": "0.000601", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `ucode` = &#039;ASSEX66108dae0cdd61712360878&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.893059, "duration": "0.000240", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_plans`\n<strong>WHERE</strong> `id` = &#039;2&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.89346, "duration": "0.000453", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_items_groups`\n<strong>WHERE</strong> `assess_exercise_id` = &#039;15&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;"}, {"name": "Query", "component": "Database", "start": **********.894344, "duration": "0.000498", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_report_groups`\n<strong>WHERE</strong> `assess_exercise_id` = &#039;15&#039;\n<strong>AND</strong> `orgcode` = &#039;2345&#039;"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "info", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: templates/adminlte/admindash.php", "component": "Views", "start": **********.896379, "duration": 0.0014009475708007812}, {"name": "View: assessments/view_assess_report_groupings.php", "component": "Views", "start": **********.895275, "duration": 0.0029649734497070312}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 169 )", "display": {"coreFiles": [{"name": "AutoRouterImproved.php", "path": "SYSTEMPATH\\Router\\AutoRouterImproved.php"}, {"name": "AutoRouterInterface.php", "path": "SYSTEMPATH\\Router\\AutoRouterInterface.php"}, {"name": "AutoloadConfig.php", "path": "SYSTEMPATH\\Config\\AutoloadConfig.php"}, {"name": "Autoloader.php", "path": "SYSTEMPATH\\Autoloader\\Autoloader.php"}, {"name": "BaseBuilder.php", "path": "SYSTEMPATH\\Database\\BaseBuilder.php"}, {"name": "BaseCollector.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php"}, {"name": "BaseConfig.php", "path": "SYSTEMPATH\\Config\\BaseConfig.php"}, {"name": "BaseConnection.php", "path": "SYSTEMPATH\\Database\\BaseConnection.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php"}, {"name": "BaseModel.php", "path": "SYSTEMPATH\\BaseModel.php"}, {"name": "BaseResult.php", "path": "SYSTEMPATH\\Database\\BaseResult.php"}, {"name": "BaseService.php", "path": "SYSTEMPATH\\Config\\BaseService.php"}, {"name": "Builder.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php"}, {"name": "CacheFactory.php", "path": "SYSTEMPATH\\Cache\\CacheFactory.php"}, {"name": "CacheInterface.php", "path": "SYSTEMPATH\\Cache\\CacheInterface.php"}, {"name": "CloneableCookieInterface.php", "path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php"}, {"name": "CodeIgniter.php", "path": "SYSTEMPATH\\CodeIgniter.php"}, {"name": "Common.php", "path": "SYSTEMPATH\\Common.php"}, {"name": "ConditionalTrait.php", "path": "SYSTEMPATH\\Traits\\ConditionalTrait.php"}, {"name": "Config.php", "path": "SYSTEMPATH\\Database\\Config.php"}, {"name": "Connection.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php"}, {"name": "ConnectionInterface.php", "path": "SYSTEMPATH\\Database\\ConnectionInterface.php"}, {"name": "ContentSecurityPolicy.php", "path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php"}, {"name": "Controller.php", "path": "SYSTEMPATH\\Controller.php"}, {"name": "Cookie.php", "path": "SYSTEMPATH\\Cookie\\Cookie.php"}, {"name": "CookieInterface.php", "path": "SYSTEMPATH\\Cookie\\CookieInterface.php"}, {"name": "CookieStore.php", "path": "SYSTEMPATH\\Cookie\\CookieStore.php"}, {"name": "Database.php", "path": "SYSTEMPATH\\Database\\Database.php"}, {"name": "Database.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php"}, {"name": "DebugToolbar.php", "path": "SYSTEMPATH\\Filters\\DebugToolbar.php"}, {"name": "DotEnv.php", "path": "SYSTEMPATH\\Config\\DotEnv.php"}, {"name": "Events.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php"}, {"name": "Events.php", "path": "SYSTEMPATH\\Events\\Events.php"}, {"name": "Exceptions.php", "path": "SYSTEMPATH\\Debug\\Exceptions.php"}, {"name": "Factories.php", "path": "SYSTEMPATH\\Config\\Factories.php"}, {"name": "Factory.php", "path": "SYSTEMPATH\\Config\\Factory.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php"}, {"name": "FileLocator.php", "path": "SYSTEMPATH\\Autoloader\\FileLocator.php"}, {"name": "Files.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php"}, {"name": "FilterInterface.php", "path": "SYSTEMPATH\\Filters\\FilterInterface.php"}, {"name": "Filters.php", "path": "SYSTEMPATH\\Filters\\Filters.php"}, {"name": "FormatRules.php", "path": "SYSTEMPATH\\Validation\\FormatRules.php"}, {"name": "HandlerInterface.php", "path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php"}, {"name": "Header.php", "path": "SYSTEMPATH\\HTTP\\Header.php"}, {"name": "IncomingRequest.php", "path": "SYSTEMPATH\\HTTP\\IncomingRequest.php"}, {"name": "Logger.php", "path": "SYSTEMPATH\\Log\\Logger.php"}, {"name": "Logs.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php"}, {"name": "Message.php", "path": "SYSTEMPATH\\HTTP\\Message.php"}, {"name": "MessageInterface.php", "path": "SYSTEMPATH\\HTTP\\MessageInterface.php"}, {"name": "MessageTrait.php", "path": "SYSTEMPATH\\HTTP\\MessageTrait.php"}, {"name": "Model.php", "path": "SYSTEMPATH\\Model.php"}, {"name": "Modules.php", "path": "SYSTEMPATH\\Modules\\Modules.php"}, {"name": "OutgoingRequest.php", "path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php"}, {"name": "OutgoingRequestInterface.php", "path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php"}, {"name": "Query.php", "path": "SYSTEMPATH\\Database\\Query.php"}, {"name": "QueryInterface.php", "path": "SYSTEMPATH\\Database\\QueryInterface.php"}, {"name": "RendererInterface.php", "path": "SYSTEMPATH\\View\\RendererInterface.php"}, {"name": "Request.php", "path": "SYSTEMPATH\\HTTP\\Request.php"}, {"name": "RequestInterface.php", "path": "SYSTEMPATH\\HTTP\\RequestInterface.php"}, {"name": "RequestTrait.php", "path": "SYSTEMPATH\\HTTP\\RequestTrait.php"}, {"name": "Response.php", "path": "SYSTEMPATH\\HTTP\\Response.php"}, {"name": "ResponseInterface.php", "path": "SYSTEMPATH\\HTTP\\ResponseInterface.php"}, {"name": "ResponseTrait.php", "path": "SYSTEMPATH\\API\\ResponseTrait.php"}, {"name": "ResponseTrait.php", "path": "SYSTEMPATH\\HTTP\\ResponseTrait.php"}, {"name": "Result.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Result.php"}, {"name": "ResultInterface.php", "path": "SYSTEMPATH\\Database\\ResultInterface.php"}, {"name": "RouteCollection.php", "path": "SYSTEMPATH\\Router\\RouteCollection.php"}, {"name": "RouteCollectionInterface.php", "path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php"}, {"name": "Router.php", "path": "SYSTEMPATH\\Router\\Router.php"}, {"name": "RouterInterface.php", "path": "SYSTEMPATH\\Router\\RouterInterface.php"}, {"name": "Routes.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php"}, {"name": "Services.php", "path": "SYSTEMPATH\\Config\\Services.php"}, {"name": "Session.php", "path": "SYSTEMPATH\\Session\\Session.php"}, {"name": "SessionInterface.php", "path": "SYSTEMPATH\\Session\\SessionInterface.php"}, {"name": "Time.php", "path": "SYSTEMPATH\\I18n\\Time.php"}, {"name": "TimeTrait.php", "path": "SYSTEMPATH\\I18n\\TimeTrait.php"}, {"name": "Timer.php", "path": "SYSTEMPATH\\Debug\\Timer.php"}, {"name": "Timers.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php"}, {"name": "Toolbar.php", "path": "SYSTEMPATH\\Debug\\Toolbar.php"}, {"name": "URI.php", "path": "SYSTEMPATH\\HTTP\\URI.php"}, {"name": "UserAgent.php", "path": "SYSTEMPATH\\HTTP\\UserAgent.php"}, {"name": "Validation.php", "path": "SYSTEMPATH\\Validation\\Validation.php"}, {"name": "ValidationInterface.php", "path": "SYSTEMPATH\\Validation\\ValidationInterface.php"}, {"name": "View.php", "path": "SYSTEMPATH\\Config\\View.php"}, {"name": "View.php", "path": "SYSTEMPATH\\View\\View.php"}, {"name": "ViewDecoratorTrait.php", "path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php"}, {"name": "Views.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php"}, {"name": "array_helper.php", "path": "SYSTEMPATH\\Helpers\\array_helper.php"}, {"name": "bootstrap.php", "path": "SYSTEMPATH\\bootstrap.php"}, {"name": "form_helper.php", "path": "SYSTEMPATH\\Helpers\\form_helper.php"}, {"name": "kint_helper.php", "path": "SYSTEMPATH\\Helpers\\kint_helper.php"}, {"name": "url_helper.php", "path": "SYSTEMPATH\\Helpers\\url_helper.php"}], "userFiles": [{"name": "AbstractRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\AbstractRenderer.php"}, {"name": "App.php", "path": "APPPATH\\Config\\App.php"}, {"name": "Assessments.php", "path": "APPPATH\\Controllers\\Assessments.php"}, {"name": "Auth.php", "path": "APPPATH\\Filters\\Auth.php"}, {"name": "Autoload.php", "path": "APPPATH\\Config\\Autoload.php"}, {"name": "BaseController.php", "path": "APPPATH\\Controllers\\BaseController.php"}, {"name": "Cache.php", "path": "APPPATH\\Config\\Cache.php"}, {"name": "ClassLoader.php", "path": "FCPATH\\vendor\\composer\\ClassLoader.php"}, {"name": "CliRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\CliRenderer.php"}, {"name": "Common.php", "path": "APPPATH\\Common.php"}, {"name": "Constants.php", "path": "APPPATH\\Config\\Constants.php"}, {"name": "ContentSecurityPolicy.php", "path": "APPPATH\\Config\\ContentSecurityPolicy.php"}, {"name": "Cookie.php", "path": "APPPATH\\Config\\Cookie.php"}, {"name": "Database.php", "path": "APPPATH\\Config\\Database.php"}, {"name": "Escaper.php", "path": "FCPATH\\vendor\\laminas\\laminas-escaper\\src\\Escaper.php"}, {"name": "Events.php", "path": "APPPATH\\Config\\Events.php"}, {"name": "Exceptions.php", "path": "APPPATH\\Config\\Exceptions.php"}, {"name": "FacadeInterface.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\FacadeInterface.php"}, {"name": "Feature.php", "path": "APPPATH\\Config\\Feature.php"}, {"name": "Filters.php", "path": "APPPATH\\Config\\Filters.php"}, {"name": "Functions.php", "path": "FCPATH\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php"}, {"name": "InstalledVersions.php", "path": "FCPATH\\vendor\\composer\\InstalledVersions.php"}, {"name": "Kint.php", "path": "APPPATH\\Config\\Kint.php"}, {"name": "Kint.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Kint.php"}, {"name": "LogLevel.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LogLevel.php"}, {"name": "Logger.php", "path": "APPPATH\\Config\\Logger.php"}, {"name": "LoggerAwareTrait.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LoggerAwareTrait.php"}, {"name": "LoggerInterface.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LoggerInterface.php"}, {"name": "Modules.php", "path": "APPPATH\\Config\\Modules.php"}, {"name": "Paths.php", "path": "APPPATH\\Config\\Paths.php"}, {"name": "RendererInterface.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\RendererInterface.php"}, {"name": "RichRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\RichRenderer.php"}, {"name": "Routes.php", "path": "APPPATH\\Config\\Routes.php"}, {"name": "Services.php", "path": "APPPATH\\Config\\Services.php"}, {"name": "Session.php", "path": "APPPATH\\Config\\Session.php"}, {"name": "TextRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\TextRenderer.php"}, {"name": "Toolbar.php", "path": "APPPATH\\Config\\Toolbar.php"}, {"name": "UserAgents.php", "path": "APPPATH\\Config\\UserAgents.php"}, {"name": "Utils.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Utils.php"}, {"name": "Validation.php", "path": "APPPATH\\Config\\Validation.php"}, {"name": "View.php", "path": "APPPATH\\Config\\View.php"}, {"name": "admindash.php", "path": "APPPATH\\Views\\templates\\adminlte\\admindash.php"}, {"name": "assess_ExercisesModel.php", "path": "APPPATH\\Models\\assess_ExercisesModel.php"}, {"name": "assess_PlansModel.php", "path": "APPPATH\\Models\\assess_PlansModel.php"}, {"name": "assess_itemsModel.php", "path": "APPPATH\\Models\\assess_itemsModel.php"}, {"name": "assess_items_GroupsModel.php", "path": "APPPATH\\Models\\assess_items_GroupsModel.php"}, {"name": "assess_report_GroupsModel.php", "path": "APPPATH\\Models\\assess_report_GroupsModel.php"}, {"name": "assess_report_itemsModel.php", "path": "APPPATH\\Models\\assess_report_itemsModel.php"}, {"name": "autoload.php", "path": "FCPATH\\vendor\\autoload.php"}, {"name": "autoload_real.php", "path": "FCPATH\\vendor\\composer\\autoload_real.php"}, {"name": "autoload_static.php", "path": "FCPATH\\vendor\\composer\\autoload_static.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-ctype\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-mbstring\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-php80\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-php81\\bootstrap.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-ctype\\bootstrap80.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap80.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-mbstring\\bootstrap80.php"}, {"name": "chart_helper.php", "path": "APPPATH\\Helpers\\chart_helper.php"}, {"name": "deep_copy.php", "path": "FCPATH\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php"}, {"name": "development.php", "path": "APPPATH\\Config\\Boot\\development.php"}, {"name": "function.php", "path": "FCPATH\\vendor\\symfony\\deprecation-contracts\\function.php"}, {"name": "functions.php", "path": "FCPATH\\vendor\\symfony\\string\\Resources\\functions.php"}, {"name": "index.php", "path": "FCPATH\\index.php"}, {"name": "info_helper.php", "path": "APPPATH\\Helpers\\info_helper.php"}, {"name": "init.php", "path": "FCPATH\\vendor\\kint-php\\kint\\init.php"}, {"name": "init_helpers.php", "path": "FCPATH\\vendor\\kint-php\\kint\\init_helpers.php"}, {"name": "installed.php", "path": "FCPATH\\vendor\\composer\\installed.php"}, {"name": "platform_check.php", "path": "FCPATH\\vendor\\composer\\platform_check.php"}, {"name": "usersModel.php", "path": "APPPATH\\Models\\usersModel.php"}, {"name": "view_assess_report_groupings.php", "path": "APPPATH\\Views\\assessments\\view_assess_report_groupings.php"}]}, "badgeValue": 169, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Assessments", "method": "view_assess_report_groupings", "paramCount": 1, "truePCount": 1, "params": [{"name": "$ucode = ", "value": "ASSEX66108dae0cdd61712360878"}]}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Home::login"}, {"method": "GET", "route": "logout", "handler": "\\App\\Controllers\\Home::logout"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Home::about"}, {"method": "GET", "route": "findme/(.*)", "handler": "\\App\\Controllers\\Home::findme/$1"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\Admindash::index"}, {"method": "GET", "route": "employees", "handler": "\\App\\Controllers\\Employees::index"}, {"method": "GET", "route": "edit_employees/(.*)", "handler": "\\App\\Controllers\\Employees::edit_employees/$1"}, {"method": "GET", "route": "profile_employees/(.*)", "handler": "\\App\\Controllers\\Employees::profile_employees/$1"}, {"method": "GET", "route": "emp_dashboard", "handler": "\\App\\Controllers\\Portal::index"}, {"method": "GET", "route": "emp_open_activity/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_activity/$1"}, {"method": "GET", "route": "emp_open_skillcomp/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skillcomp/$1"}, {"method": "GET", "route": "emp_open_feedbacks/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_feedbacks/$1"}, {"method": "GET", "route": "emp_open_feedback_groups/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_feedback_groups/$1"}, {"method": "GET", "route": "emp_open_skills_comp/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skills_comp/$1"}, {"method": "GET", "route": "emp_open_skills_comp_groups/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skills_comp_groups/$1"}, {"method": "GET", "route": "emp_open_skills_comp_employees/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skills_comp_employees/$1"}, {"method": "GET", "route": "plans_manager", "handler": "\\App\\Controllers\\Plans::index"}, {"method": "GET", "route": "view_plans/(.*)", "handler": "\\App\\Controllers\\Plans::view_plans/$1"}, {"method": "GET", "route": "align_plans/(.*)", "handler": "\\App\\Controllers\\Plans::align_plans/$1"}, {"method": "GET", "route": "view_groups/(.*)", "handler": "\\App\\Controllers\\Plans::view_groups/$1"}, {"method": "GET", "route": "view_strategies/(.*)", "handler": "\\App\\Controllers\\Plans::view_strategies/$1"}, {"method": "GET", "route": "view_programs/(.*)", "handler": "\\App\\Controllers\\Plans::view_programs/$1"}, {"method": "GET", "route": "assessments_manager", "handler": "\\App\\Controllers\\Assessments::index"}, {"method": "GET", "route": "view_assess_exercises/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_exercises/$1"}, {"method": "GET", "route": "open_assess_exercise/(.*)", "handler": "\\App\\Controllers\\Assessments::open_assess_exercise/$1"}, {"method": "GET", "route": "view_assess_items_groups/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_items_groups/$1"}, {"method": "GET", "route": "view_assess_items/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_items/$1"}, {"method": "GET", "route": "open_assess_score_group/(.*)", "handler": "\\App\\Controllers\\Assessments::open_assess_score_group/$1"}, {"method": "GET", "route": "open_assess_score_items/(.*)", "handler": "\\App\\Controllers\\Assessments::open_assess_score_items/$1"}, {"method": "GET", "route": "view_assess_report_groupings/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_report_groupings/$1"}, {"method": "GET", "route": "view_assess_report_items/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_report_items/$1"}, {"method": "GET", "route": "view_assess_reports/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_reports/$1"}, {"method": "GET", "route": "view_assess_reports_summary_tables/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_reports_summary_tables/$1"}, {"method": "GET", "route": "view_assess_reports_raw_scores/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_reports_raw_scores/$1"}, {"method": "GET", "route": "activities", "handler": "\\App\\Controllers\\Activities::index"}, {"method": "GET", "route": "new_activity", "handler": "\\App\\Controllers\\Activities::new_activity"}, {"method": "GET", "route": "open_activity/(.*)", "handler": "\\App\\Controllers\\Activities::open_activity/$1"}, {"method": "GET", "route": "open_qualitative_unpack/(.*)", "handler": "\\App\\Controllers\\Activities::open_qualitative_unpack/$1"}, {"method": "GET", "route": "open_qualitative_item/(.*)", "handler": "\\App\\Controllers\\Activities::open_qualitative_item/$1"}, {"method": "GET", "route": "reports_qualitative_dashboard/(.*)", "handler": "\\App\\Controllers\\Reports::reports_qualitative_dashboard/$1"}, {"method": "GET", "route": "da<PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::index"}, {"method": "GET", "route": "dlogout", "handler": "\\App\\Controllers\\Dakoii::logout"}, {"method": "GET", "route": "ddash", "handler": "\\App\\Controllers\\Dakoii::ddash"}, {"method": "GET", "route": "dopen_org/(.*)", "handler": "\\App\\Controllers\\Dakoii::open_org/$1"}, {"method": "GET", "route": "dlist_org", "handler": "\\App\\Controllers\\Dakoii::list_org"}, {"method": "GET", "route": "testa", "handler": "\\App\\Controllers\\Test::index"}, {"method": "GET", "route": "ajax", "handler": "\\App\\Controllers\\Test::ajax"}, {"method": "GET", "route": "ajaxform", "handler": "\\App\\Controllers\\Test::ajaxform"}, {"method": "GET", "route": "testmap", "handler": "\\App\\Controllers\\Test::testmap"}, {"method": "POST", "route": "login", "handler": "\\App\\Controllers\\Home::login"}, {"method": "POST", "route": "dologin", "handler": "\\App\\Controllers\\Home::dologin"}, {"method": "POST", "route": "gofindme", "handler": "\\App\\Controllers\\Home::gofindme"}, {"method": "POST", "route": "open_profile", "handler": "\\App\\Controllers\\Home::open_profile"}, {"method": "POST", "route": "create_employees", "handler": "\\App\\Controllers\\Employees::create_employees"}, {"method": "POST", "route": "update_employees", "handler": "\\App\\Controllers\\Employees::update_employees"}, {"method": "POST", "route": "create_plans", "handler": "\\App\\Controllers\\Plans::create_plans"}, {"method": "POST", "route": "update_plans", "handler": "\\App\\Controllers\\Plans::update_plans"}, {"method": "POST", "route": "delete_plans", "handler": "\\App\\Controllers\\Plans::delete_plans"}, {"method": "POST", "route": "create_plan_groups", "handler": "\\App\\Controllers\\Plans::create_plan_groups"}, {"method": "POST", "route": "update_plan_groups", "handler": "\\App\\Controllers\\Plans::update_plan_groups"}, {"method": "POST", "route": "delete_plan_groups", "handler": "\\App\\Controllers\\Plans::delete_plan_groups"}, {"method": "POST", "route": "create_strategies", "handler": "\\App\\Controllers\\Plans::create_strategies"}, {"method": "POST", "route": "update_strategies", "handler": "\\App\\Controllers\\Plans::update_strategies"}, {"method": "POST", "route": "delete_strategies", "handler": "\\App\\Controllers\\Plans::delete_strategies"}, {"method": "POST", "route": "create_programs", "handler": "\\App\\Controllers\\Plans::create_programs"}, {"method": "POST", "route": "update_programs", "handler": "\\App\\Controllers\\Plans::update_programs"}, {"method": "POST", "route": "delete_programs", "handler": "\\App\\Controllers\\Plans::delete_programs"}, {"method": "POST", "route": "create_pro_act", "handler": "\\App\\Controllers\\Plans::create_pro_act"}, {"method": "POST", "route": "update_pro_act", "handler": "\\App\\Controllers\\Plans::update_pro_act"}, {"method": "POST", "route": "create_assess_plan", "handler": "\\App\\Controllers\\Assessments::create_assess_plan"}, {"method": "POST", "route": "update_assess_plan", "handler": "\\App\\Controllers\\Assessments::update_assess_plan"}, {"method": "POST", "route": "delete_assess_plan", "handler": "\\App\\Controllers\\Assessments::delete_assess_plan"}, {"method": "POST", "route": "create_assessment_exercise", "handler": "\\App\\Controllers\\Assessments::create_assessment_exercise"}, {"method": "POST", "route": "update_assessment_exercise", "handler": "\\App\\Controllers\\Assessments::update_assessment_exercise"}, {"method": "POST", "route": "delete_assessment_exercise", "handler": "\\App\\Controllers\\Assessments::delete_assessment_exercise"}, {"method": "POST", "route": "create_assess_items_group", "handler": "\\App\\Controllers\\Assessments::create_assess_items_group"}, {"method": "POST", "route": "update_assess_items_group", "handler": "\\App\\Controllers\\Assessments::update_assess_items_group"}, {"method": "POST", "route": "delete_assess_items_group", "handler": "\\App\\Controllers\\Assessments::delete_assess_items_group"}, {"method": "POST", "route": "import_assess_items_groups", "handler": "\\App\\Controllers\\Assessments::import_assess_items_groups"}, {"method": "POST", "route": "create_assess_report_group", "handler": "\\App\\Controllers\\Assessments::create_assess_report_group"}, {"method": "POST", "route": "update_assess_report_group", "handler": "\\App\\Controllers\\Assessments::update_assess_report_group"}, {"method": "POST", "route": "delete_assess_report_group", "handler": "\\App\\Controllers\\Assessments::delete_assess_report_group"}, {"method": "POST", "route": "import_assess_items", "handler": "\\App\\Controllers\\Assessments::import_assess_items"}, {"method": "POST", "route": "create_assess_items", "handler": "\\App\\Controllers\\Assessments::create_assess_items"}, {"method": "POST", "route": "update_assess_items", "handler": "\\App\\Controllers\\Assessments::update_assess_items"}, {"method": "POST", "route": "delete_assess_items", "handler": "\\App\\Controllers\\Assessments::delete_assess_items"}, {"method": "POST", "route": "create_assess_report_items", "handler": "\\App\\Controllers\\Assessments::create_assess_report_items"}, {"method": "POST", "route": "delete_assess_report_items", "handler": "\\App\\Controllers\\Assessments::delete_assess_report_items"}, {"method": "POST", "route": "enter_score_items", "handler": "\\App\\Controllers\\Assessments::enter_score_items"}, {"method": "POST", "route": "update_target_rates", "handler": "\\App\\Controllers\\Assessments::update_target_rates"}, {"method": "POST", "route": "import_qualitative_data", "handler": "\\App\\Controllers\\Activities::import_qualitative_data"}, {"method": "POST", "route": "create_qualitative_item", "handler": "\\App\\Controllers\\Activities::create_qualitative_item"}, {"method": "POST", "route": "update_qualitative_item", "handler": "\\App\\Controllers\\Activities::update_qualitative_item"}, {"method": "POST", "route": "delete_qualitative_item", "handler": "\\App\\Controllers\\Activities::delete_qualitative_item"}, {"method": "POST", "route": "bg_qualitative_color", "handler": "\\App\\Controllers\\Activities::bg_qualitative_color"}, {"method": "POST", "route": "qualitative_for_score", "handler": "\\App\\Controllers\\Activities::qualitative_for_score"}, {"method": "POST", "route": "download_qualitative_data", "handler": "\\App\\Controllers\\Activities::download_qualitative_data"}, {"method": "POST", "route": "import_qualitative_score", "handler": "\\App\\Controllers\\Activities::import_qualitative_score"}, {"method": "POST", "route": "create_activity", "handler": "\\App\\Controllers\\Activities::create_activity"}, {"method": "POST", "route": "update_activity", "handler": "\\App\\Controllers\\Activities::update_activity"}, {"method": "POST", "route": "update_activity_status", "handler": "\\App\\Controllers\\Activities::update_activity_status"}, {"method": "POST", "route": "dlogin", "handler": "\\App\\Controllers\\Dakoii::login"}, {"method": "POST", "route": "dad<PERSON>g", "handler": "\\App\\Controllers\\Dakoii::addorg"}, {"method": "POST", "route": "deditorg", "handler": "\\App\\Controllers\\Dakoii::editorg"}, {"method": "POST", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::adduser"}, {"method": "POST", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::create_admin"}, {"method": "POST", "route": "ajax", "handler": "\\App\\Controllers\\Test::ajax"}, {"method": "POST", "route": "ajaxform", "handler": "\\App\\Controllers\\Test::ajaxform"}]}, "badgeValue": 50, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "8.87", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.13", "count": 4}}}, "badgeValue": 5, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.769873, "duration": 0.008867025375366211}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.890126, "duration": 3.409385681152344e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.893302, "duration": 3.0994415283203125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.893918, "duration": 3.3855438232421875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.894847, "duration": 3.218650817871094e-05}]}], "vars": {"varData": {"View Data": {"title": "Report Groupings", "menu": "dashboard", "assex": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (18)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSEX66108dae0cdd61712360878\"<div class=\"access-path\">$value['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"ASX23454\"<div class=\"access-path\">$value['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (22) \"ESPIPDP Review Scoring\"<div class=\"access-path\">$value['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_from</dfn> =&gt; <var>string</var> (10) \"2024-04-03\"<div class=\"access-path\">$value['date_from']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_to</dfn> =&gt; <var>string</var> (10) \"2024-04-11\"<div class=\"access-path\">$value['date_to']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>max_rate</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value['max_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>min_rate</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value['min_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:47:58\"<div class=\"access-path\">$value['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:47:58\"<div class=\"access-path\">$value['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:29:05\"<div class=\"access-path\">$value['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value['update_by']</div></dt></dl></dd></dl></div>", "plan": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (15)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASPLN6610835fbfa491712358239\"<div class=\"access-path\">$value['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (11) \"ESPIPDP2028\"<div class=\"access-path\">$value['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (59) \"East Sepik Province Integrated Development Plan 2018 - 2028\"<div class=\"access-path\">$value['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_from</dfn> =&gt; <var>string</var> (10) \"2018-12-12\"<div class=\"access-path\">$value['date_from']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_to</dfn> =&gt; <var>string</var> (10) \"2028-12-12\"<div class=\"access-path\">$value['date_to']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:03:59\"<div class=\"access-path\">$value['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:03:59\"<div class=\"access-path\">$value['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-04-15 12:36:41\"<div class=\"access-path\">$value['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value['update_by']</div></dt></dl></dd></dl></div>", "items_groups": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (44)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (44)</li><li>Contents (44)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>ucode</th><th>orgcode</th><th>plan_id</th><th>assess_exercise_id</th><th>code</th><th>title</th><th>bg_color</th><th>status</th><th>status_at</th><th>status_by</th><th>created_at</th><th>updated_at</th><th>create_by</th><th>update_by</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">20</td><td title=\"string (27)\">IGRP66129316e12db1712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.1.1</td><td title=\"string (27)\">Land Access and Development</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>1</th><td title=\"string (2)\">21</td><td title=\"string (27)\">IGRP66129316e3f9c1712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.1.2</td><td title=\"string (26)\"> Agriculture and Livestock</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>2</th><td title=\"string (2)\">22</td><td title=\"string (27)\">IGRP66129316e4aa41712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.1.3</td><td title=\"string (10)\"> Fisheries</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>3</th><td title=\"string (2)\">23</td><td title=\"string (27)\">IGRP66129316e6c051712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.1.4</td><td title=\"string (8)\"> Tourism</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>4</th><td title=\"string (2)\">24</td><td title=\"string (27)\">IGRP66129316e71931712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.1.5</td><td title=\"string (17)\"> Culture and Arts</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>5</th><td title=\"string (2)\">25</td><td title=\"string (27)\">IGRP66129316e78b41712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.1.6</td><td title=\"string (26)\"> Forestry and Biodiversity</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>6</th><td title=\"string (2)\">26</td><td title=\"string (27)\">IGRP66129316e7ff81712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.1.7</td><td title=\"string (7)\"> Mining</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>7</th><td title=\"string (2)\">27</td><td title=\"string (27)\">IGRP66129316e84df1712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.1.8</td><td title=\"string (29)\"> Small and Medium Enterprises</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>8</th><td title=\"string (2)\">28</td><td title=\"string (27)\">IGRP66129316e8ac21712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.1.9</td><td title=\"string (20)\"> Financial Inclusion</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>9</th><td title=\"string (2)\">29</td><td title=\"string (27)\">IGRP66129316e91421712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (6)\">5.1.10</td><td title=\"string (13)\">Business Arms</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>10</th><td title=\"string (2)\">30</td><td title=\"string (27)\">IGRP66129316e98b21712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.2.1</td><td title=\"string (14)\">Land Transport</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>11</th><td title=\"string (2)\">31</td><td title=\"string (27)\">IGRP66129316ea0751712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.2.2</td><td title=\"string (13)\">Air Transport</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>12</th><td title=\"string (2)\">32</td><td title=\"string (27)\">IGRP66129316ea8f21712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.2.3</td><td title=\"string (26)\">Marine and Water Transport</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>13</th><td title=\"string (2)\">33</td><td title=\"string (27)\">IGRP66129316eb2361712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.2.4</td><td title=\"string (6)\">Energy</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>14</th><td title=\"string (2)\">34</td><td title=\"string (27)\">IGRP66129316eb7a81712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.2.5</td><td title=\"string (29)\">Information and Communication</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>15</th><td title=\"string (2)\">35</td><td title=\"string (27)\">IGRP66129316ec1ca1712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.2.6</td><td title=\"string (12)\">Urbanisation</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>16</th><td title=\"string (2)\">36</td><td title=\"string (27)\">IGRP66129316ecb4f1712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.2.7</td><td title=\"string (28)\">Water Sanitation and Hygiene</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>17</th><td title=\"string (2)\">37</td><td title=\"string (27)\">IGRP66129316ed02b1712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.3.1</td><td title=\"string (20)\">Health and Nutrition</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>18</th><td title=\"string (2)\">38</td><td title=\"string (27)\">IGRP66129316ed53e1712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.3.2</td><td title=\"string (9)\">Education</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>19</th><td title=\"string (2)\">39</td><td title=\"string (27)\">IGRP66129316eda451712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.3.3</td><td title=\"string (50)\">Higher Education, Research, Science and Technology</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>20</th><td title=\"string (2)\">40</td><td title=\"string (27)\">IGRP66129316ee1a21712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.3.4</td><td title=\"string (10)\">Population</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>21</th><td title=\"string (2)\">41</td><td title=\"string (27)\">IGRP66129316ee8fe1712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.3.5</td><td title=\"string (15)\">Law and Justice</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>22</th><td title=\"string (2)\">42</td><td title=\"string (27)\">IGRP66129316eee1e1712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.3.6</td><td title=\"string (35)\"> Community Development and Churches</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>23</th><td title=\"string (2)\">43</td><td title=\"string (27)\">IGRP66129316ef2f31712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.3.7</td><td title=\"string (7)\"> Sports</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>24</th><td title=\"string (2)\">44</td><td title=\"string (27)\">IGRP66129316ef8341712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.3.8</td><td title=\"string (6)\"> Youth</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>25</th><td title=\"string (2)\">45</td><td title=\"string (27)\">IGRP66129316efec71712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.3.9</td><td title=\"string (39)\"> Vulnerable and Disadvantage Population</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>26</th><td title=\"string (2)\">46</td><td title=\"string (27)\">IGRP66129316f0b881712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.4.1</td><td title=\"string (25)\"> HIV Aids and Social Ills</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>27</th><td title=\"string (2)\">47</td><td title=\"string (27)\">IGRP66129316f12e91712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.4.2</td><td title=\"string (43)\"> Climate Change, Environment &amp; Carbon Trade</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>28</th><td title=\"string (2)\">48</td><td title=\"string (27)\">IGRP66129316f18f71712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.4.3</td><td title=\"string (25)\"> Disaster Risk Management</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>29</th><td title=\"string (2)\">49</td><td title=\"string (27)\">IGRP66129316f1ddf1712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.4.4</td><td title=\"string (7)\"> Gender</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>30</th><td title=\"string (2)\">50</td><td title=\"string (27)\">IGRP66129316f234c1712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.5.1</td><td title=\"string (10)\">Governance</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>31</th><td title=\"string (2)\">51</td><td title=\"string (27)\">IGRP66129316f28c71712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.5.2</td><td title=\"string (21)\">Public Administration</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>32</th><td title=\"string (2)\">52</td><td title=\"string (27)\">IGRP66129316f2db01712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.5.3</td><td title=\"string (27)\"> Human Resource Development</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>33</th><td title=\"string (2)\">53</td><td title=\"string (27)\">IGRP66129316f33671712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.5.4</td><td title=\"string (21)\"> Financial Management</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>34</th><td title=\"string (2)\">54</td><td title=\"string (27)\">IGRP66129316f39231712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.5.5</td><td title=\"string (21)\"> Audit and Compliance</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>35</th><td title=\"string (2)\">55</td><td title=\"string (27)\">IGRP66129316f3eb71712493334</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (5)\">5.5.6</td><td title=\"string (53)\"> Monitoring and Evaluation of programmes and projects</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (19)\">2024-04-07 22:35:34</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>36</th><td title=\"string (2)\">56</td><td title=\"string (27)\">IGRP6829c352690511747567442</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (8)\">Sector 1</td><td title=\"string (20)\">Sector: Business Arm</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>37</th><td title=\"string (2)\">57</td><td title=\"string (27)\">IGRP6829c3526c7b51747567442</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (8)\">Sector 2</td><td title=\"string (12)\">5.1.7 Mining</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>38</th><td title=\"string (2)\">58</td><td title=\"string (27)\">IGRP6829c3526de611747567442</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (8)\">Sector 3</td><td title=\"string (32)\">5.1.6 Forestry and Biodiversity </td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>39</th><td title=\"string (2)\">59</td><td title=\"string (27)\">IGRP6829c3526ede31747567442</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (8)\">Sector 4</td><td title=\"string (28)\">Small and Medium Enterprises</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>40</th><td title=\"string (2)\">60</td><td title=\"string (27)\">IGRP6829c3526fb0c1747567442</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (8)\">Sector 5</td><td title=\"string (10)\">Fisheries </td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>41</th><td title=\"string (2)\">61</td><td title=\"string (27)\">IGRP6829c3527093b1747567442</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (8)\">Sector 6</td><td title=\"string (25)\">Agriculture and lifestock</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>42</th><td title=\"string (2)\">62</td><td title=\"string (27)\">IGRP6829c3527170f1747567442</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (8)\">Sector 7</td><td title=\"string (7)\">Tourism</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>43</th><td title=\"string (2)\">63</td><td title=\"string (27)\">IGRP6829c352735701747567442</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (2)\">15</td><td title=\"string (8)\">Sector 8</td><td title=\"string (32)\">SECTOR:5.1.9 Financial Inclusion</td><td title=\"string (0)\"></td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (19)\">2025-05-18 21:24:02</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"20\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e12db1712493334\"<div class=\"access-path\">$value[0]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[0]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[0]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.1\"<div class=\"access-path\">$value[0]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (27) \"Land Access and Development\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[0]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e3f9c1712493334\"<div class=\"access-path\">$value[1]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[1]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[1]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.2\"<div class=\"access-path\">$value[1]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (26) \" Agriculture and Livestock\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[1]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e4aa41712493334\"<div class=\"access-path\">$value[2]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[2]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[2]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.3\"<div class=\"access-path\">$value[2]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (10) \" Fisheries\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[2]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e6c051712493334\"<div class=\"access-path\">$value[3]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[3]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[3]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[3]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.4\"<div class=\"access-path\">$value[3]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (8) \" Tourism\"<div class=\"access-path\">$value[3]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[3]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"24\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e71931712493334\"<div class=\"access-path\">$value[4]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[4]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[4]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[4]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.5\"<div class=\"access-path\">$value[4]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (17) \" Culture and Arts\"<div class=\"access-path\">$value[4]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[4]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[4]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"25\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e78b41712493334\"<div class=\"access-path\">$value[5]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[5]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[5]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[5]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.6\"<div class=\"access-path\">$value[5]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (26) \" Forestry and Biodiversity\"<div class=\"access-path\">$value[5]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[5]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[5]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[5]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[5]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"26\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e7ff81712493334\"<div class=\"access-path\">$value[6]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[6]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[6]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[6]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.7\"<div class=\"access-path\">$value[6]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (7) \" Mining\"<div class=\"access-path\">$value[6]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[6]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[6]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[6]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[6]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[6]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"27\"<div class=\"access-path\">$value[7]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e84df1712493334\"<div class=\"access-path\">$value[7]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[7]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[7]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[7]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.8\"<div class=\"access-path\">$value[7]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (29) \" Small and Medium Enterprises\"<div class=\"access-path\">$value[7]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[7]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[7]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[7]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[7]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[7]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[7]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[8]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e8ac21712493334\"<div class=\"access-path\">$value[8]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[8]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[8]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[8]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.1.9\"<div class=\"access-path\">$value[8]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (20) \" Financial Inclusion\"<div class=\"access-path\">$value[8]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[8]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[8]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[8]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[8]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[8]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[8]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[9]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e91421712493334\"<div class=\"access-path\">$value[9]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[9]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[9]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[9]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (6) \"5.1.10\"<div class=\"access-path\">$value[9]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (13) \"Business Arms\"<div class=\"access-path\">$value[9]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[9]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[9]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[9]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[9]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[9]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[9]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>10</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[10]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[10]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316e98b21712493334\"<div class=\"access-path\">$value[10]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[10]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[10]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[10]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.2.1\"<div class=\"access-path\">$value[10]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (14) \"Land Transport\"<div class=\"access-path\">$value[10]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[10]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[10]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[10]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[10]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[10]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[10]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[10]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>11</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[11]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"31\"<div class=\"access-path\">$value[11]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316ea0751712493334\"<div class=\"access-path\">$value[11]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[11]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[11]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[11]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.2.2\"<div class=\"access-path\">$value[11]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (13) \"Air Transport\"<div class=\"access-path\">$value[11]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[11]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[11]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[11]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[11]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[11]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[11]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[11]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>12</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[12]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"32\"<div class=\"access-path\">$value[12]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316ea8f21712493334\"<div class=\"access-path\">$value[12]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[12]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[12]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[12]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.2.3\"<div class=\"access-path\">$value[12]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (26) \"Marine and Water Transport\"<div class=\"access-path\">$value[12]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[12]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[12]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[12]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[12]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[12]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[12]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[12]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>13</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[13]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"33\"<div class=\"access-path\">$value[13]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316eb2361712493334\"<div class=\"access-path\">$value[13]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[13]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[13]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[13]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.2.4\"<div class=\"access-path\">$value[13]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (6) \"Energy\"<div class=\"access-path\">$value[13]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[13]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[13]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[13]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[13]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[13]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[13]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[13]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[13]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>14</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[14]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"34\"<div class=\"access-path\">$value[14]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316eb7a81712493334\"<div class=\"access-path\">$value[14]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[14]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[14]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[14]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.2.5\"<div class=\"access-path\">$value[14]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (29) \"Information and Communication\"<div class=\"access-path\">$value[14]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[14]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[14]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[14]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[14]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[14]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[14]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[14]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[14]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>15</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[15]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"35\"<div class=\"access-path\">$value[15]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316ec1ca1712493334\"<div class=\"access-path\">$value[15]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[15]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[15]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[15]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.2.6\"<div class=\"access-path\">$value[15]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (12) \"Urbanisation\"<div class=\"access-path\">$value[15]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[15]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[15]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[15]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[15]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[15]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[15]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[15]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[15]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>16</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[16]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"36\"<div class=\"access-path\">$value[16]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316ecb4f1712493334\"<div class=\"access-path\">$value[16]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[16]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[16]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[16]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.2.7\"<div class=\"access-path\">$value[16]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (28) \"Water Sanitation and Hygiene\"<div class=\"access-path\">$value[16]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[16]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[16]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[16]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[16]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[16]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[16]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[16]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[16]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>17</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[17]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"37\"<div class=\"access-path\">$value[17]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316ed02b1712493334\"<div class=\"access-path\">$value[17]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[17]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[17]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[17]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.3.1\"<div class=\"access-path\">$value[17]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (20) \"Health and Nutrition\"<div class=\"access-path\">$value[17]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[17]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[17]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[17]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[17]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[17]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[17]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[17]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[17]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>18</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[18]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"38\"<div class=\"access-path\">$value[18]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316ed53e1712493334\"<div class=\"access-path\">$value[18]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[18]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[18]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[18]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.3.2\"<div class=\"access-path\">$value[18]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (9) \"Education\"<div class=\"access-path\">$value[18]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[18]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[18]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[18]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[18]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[18]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[18]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[18]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[18]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>19</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[19]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"39\"<div class=\"access-path\">$value[19]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316eda451712493334\"<div class=\"access-path\">$value[19]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[19]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[19]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[19]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.3.3\"<div class=\"access-path\">$value[19]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (50) \"Higher Education, Research, Science and Technology\"<div class=\"access-path\">$value[19]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[19]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[19]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[19]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[19]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[19]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[19]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[19]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[19]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>20</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[20]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"40\"<div class=\"access-path\">$value[20]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316ee1a21712493334\"<div class=\"access-path\">$value[20]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[20]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[20]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[20]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.3.4\"<div class=\"access-path\">$value[20]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (10) \"Population\"<div class=\"access-path\">$value[20]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[20]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[20]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[20]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[20]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[20]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[20]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[20]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[20]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>21</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[21]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"41\"<div class=\"access-path\">$value[21]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316ee8fe1712493334\"<div class=\"access-path\">$value[21]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[21]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[21]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[21]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.3.5\"<div class=\"access-path\">$value[21]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (15) \"Law and Justice\"<div class=\"access-path\">$value[21]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[21]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[21]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[21]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[21]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[21]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[21]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[21]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[21]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>22</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[22]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"42\"<div class=\"access-path\">$value[22]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316eee1e1712493334\"<div class=\"access-path\">$value[22]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[22]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[22]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[22]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.3.6\"<div class=\"access-path\">$value[22]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (35) \" Community Development and Churches\"<div class=\"access-path\">$value[22]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[22]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[22]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[22]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[22]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[22]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[22]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[22]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[22]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>23</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[23]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"43\"<div class=\"access-path\">$value[23]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316ef2f31712493334\"<div class=\"access-path\">$value[23]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[23]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[23]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[23]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.3.7\"<div class=\"access-path\">$value[23]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (7) \" Sports\"<div class=\"access-path\">$value[23]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[23]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[23]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[23]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[23]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[23]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[23]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[23]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[23]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>24</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[24]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"44\"<div class=\"access-path\">$value[24]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316ef8341712493334\"<div class=\"access-path\">$value[24]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[24]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[24]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[24]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.3.8\"<div class=\"access-path\">$value[24]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (6) \" Youth\"<div class=\"access-path\">$value[24]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[24]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[24]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[24]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[24]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[24]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[24]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[24]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[24]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>25</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[25]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"45\"<div class=\"access-path\">$value[25]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316efec71712493334\"<div class=\"access-path\">$value[25]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[25]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[25]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[25]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.3.9\"<div class=\"access-path\">$value[25]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (39) \" Vulnerable and Disadvantage Population\"<div class=\"access-path\">$value[25]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[25]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[25]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[25]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[25]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[25]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[25]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[25]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[25]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>26</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[26]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"46\"<div class=\"access-path\">$value[26]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316f0b881712493334\"<div class=\"access-path\">$value[26]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[26]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[26]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[26]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.4.1\"<div class=\"access-path\">$value[26]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (25) \" HIV Aids and Social Ills\"<div class=\"access-path\">$value[26]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[26]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[26]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[26]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[26]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[26]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[26]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[26]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[26]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>27</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[27]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"47\"<div class=\"access-path\">$value[27]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316f12e91712493334\"<div class=\"access-path\">$value[27]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[27]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[27]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[27]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.4.2\"<div class=\"access-path\">$value[27]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (43) \" Climate Change, Environment &amp; Carbon Trade\"<div class=\"access-path\">$value[27]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[27]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[27]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[27]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[27]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[27]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[27]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[27]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[27]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>28</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[28]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"48\"<div class=\"access-path\">$value[28]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316f18f71712493334\"<div class=\"access-path\">$value[28]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[28]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[28]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[28]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.4.3\"<div class=\"access-path\">$value[28]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (25) \" Disaster Risk Management\"<div class=\"access-path\">$value[28]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[28]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[28]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[28]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[28]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[28]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[28]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[28]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[28]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>29</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[29]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"49\"<div class=\"access-path\">$value[29]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316f1ddf1712493334\"<div class=\"access-path\">$value[29]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[29]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[29]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[29]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.4.4\"<div class=\"access-path\">$value[29]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (7) \" Gender\"<div class=\"access-path\">$value[29]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[29]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[29]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[29]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[29]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[29]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[29]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[29]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[29]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>30</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[30]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"50\"<div class=\"access-path\">$value[30]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316f234c1712493334\"<div class=\"access-path\">$value[30]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[30]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[30]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[30]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.5.1\"<div class=\"access-path\">$value[30]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (10) \"Governance\"<div class=\"access-path\">$value[30]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[30]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[30]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[30]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[30]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[30]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[30]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[30]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[30]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>31</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[31]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"51\"<div class=\"access-path\">$value[31]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316f28c71712493334\"<div class=\"access-path\">$value[31]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[31]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[31]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[31]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.5.2\"<div class=\"access-path\">$value[31]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (21) \"Public Administration\"<div class=\"access-path\">$value[31]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[31]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[31]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[31]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[31]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[31]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[31]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[31]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[31]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>32</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[32]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"52\"<div class=\"access-path\">$value[32]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316f2db01712493334\"<div class=\"access-path\">$value[32]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[32]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[32]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[32]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.5.3\"<div class=\"access-path\">$value[32]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (27) \" Human Resource Development\"<div class=\"access-path\">$value[32]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[32]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[32]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[32]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[32]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[32]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[32]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[32]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[32]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>33</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[33]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"53\"<div class=\"access-path\">$value[33]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316f33671712493334\"<div class=\"access-path\">$value[33]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[33]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[33]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[33]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.5.4\"<div class=\"access-path\">$value[33]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (21) \" Financial Management\"<div class=\"access-path\">$value[33]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[33]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[33]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[33]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[33]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[33]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[33]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[33]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[33]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>34</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[34]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"54\"<div class=\"access-path\">$value[34]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316f39231712493334\"<div class=\"access-path\">$value[34]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[34]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[34]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[34]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.5.5\"<div class=\"access-path\">$value[34]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (21) \" Audit and Compliance\"<div class=\"access-path\">$value[34]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[34]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[34]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[34]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[34]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[34]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[34]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[34]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[34]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>35</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[35]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"55\"<div class=\"access-path\">$value[35]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP66129316f3eb71712493334\"<div class=\"access-path\">$value[35]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[35]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[35]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[35]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (5) \"5.5.6\"<div class=\"access-path\">$value[35]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (53) \" Monitoring and Evaluation of programmes and projects\"<div class=\"access-path\">$value[35]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[35]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[35]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[35]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[35]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[35]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:35:34\"<div class=\"access-path\">$value[35]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[35]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[35]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>36</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[36]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"56\"<div class=\"access-path\">$value[36]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP6829c352690511747567442\"<div class=\"access-path\">$value[36]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[36]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[36]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[36]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"Sector 1\"<div class=\"access-path\">$value[36]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (20) \"Sector: Business Arm\"<div class=\"access-path\">$value[36]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[36]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[36]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[36]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[36]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[36]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[36]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[36]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[36]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>37</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[37]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"57\"<div class=\"access-path\">$value[37]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP6829c3526c7b51747567442\"<div class=\"access-path\">$value[37]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[37]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[37]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[37]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"Sector 2\"<div class=\"access-path\">$value[37]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (12) \"5.1.7 Mining\"<div class=\"access-path\">$value[37]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[37]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[37]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[37]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[37]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[37]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[37]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[37]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[37]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>38</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[38]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"58\"<div class=\"access-path\">$value[38]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP6829c3526de611747567442\"<div class=\"access-path\">$value[38]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[38]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[38]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[38]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"Sector 3\"<div class=\"access-path\">$value[38]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (32) \"5.1.6 Forestry and Biodiversity \"<div class=\"access-path\">$value[38]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[38]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[38]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[38]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[38]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[38]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[38]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[38]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[38]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>39</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[39]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"59\"<div class=\"access-path\">$value[39]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP6829c3526ede31747567442\"<div class=\"access-path\">$value[39]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[39]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[39]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[39]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"Sector 4\"<div class=\"access-path\">$value[39]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (28) \"Small and Medium Enterprises\"<div class=\"access-path\">$value[39]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[39]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[39]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[39]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[39]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[39]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[39]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[39]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[39]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>40</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[40]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"60\"<div class=\"access-path\">$value[40]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP6829c3526fb0c1747567442\"<div class=\"access-path\">$value[40]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[40]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[40]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[40]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"Sector 5\"<div class=\"access-path\">$value[40]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (10) \"Fisheries \"<div class=\"access-path\">$value[40]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[40]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[40]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[40]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[40]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[40]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[40]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[40]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[40]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>41</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[41]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[41]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP6829c3527093b1747567442\"<div class=\"access-path\">$value[41]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[41]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[41]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[41]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"Sector 6\"<div class=\"access-path\">$value[41]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (25) \"Agriculture and lifestock\"<div class=\"access-path\">$value[41]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[41]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[41]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[41]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[41]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[41]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[41]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[41]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[41]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>42</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[42]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"62\"<div class=\"access-path\">$value[42]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP6829c3527170f1747567442\"<div class=\"access-path\">$value[42]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[42]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[42]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[42]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"Sector 7\"<div class=\"access-path\">$value[42]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (7) \"Tourism\"<div class=\"access-path\">$value[42]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[42]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[42]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[42]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[42]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[42]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[42]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[42]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[42]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>43</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[43]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"63\"<div class=\"access-path\">$value[43]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"IGRP6829c352735701747567442\"<div class=\"access-path\">$value[43]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[43]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[43]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[43]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"Sector 8\"<div class=\"access-path\">$value[43]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (32) \"SECTOR:5.1.9 Financial Inclusion\"<div class=\"access-path\">$value[43]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[43]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[43]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[43]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[43]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[43]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:24:02\"<div class=\"access-path\">$value[43]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[43]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[43]['update_by']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "data": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>ucode</dfn> =&gt; <var>string</var> (27) \"RGRP6829c62d236c91747568173\"<div class=\"access-path\">$value[0]['ucode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[0]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>assess_exercise_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[0]['assess_exercise_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (4) \"DIP2\"<div class=\"access-path\">$value[0]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (13) \"Cooking Smite\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>bg_color</dfn> =&gt; <var>string</var> (7) \"#000000\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 0, 0, 1)\"></div></div><div class=\"access-path\">$value[0]['bg_color']</div></dt><dd><pre><dfn>black</dfn>\n<dfn>#000</dfn>\n<dfn>#000000</dfn>\n<dfn>rgb(0, 0, 0)</dfn>\n<dfn>hsl(0, 0%, 0%)</dfn>\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>chart_type</dfn> =&gt; <var>string</var> (8) \"barChart\"<div class=\"access-path\">$value[0]['chart_type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>chart_operation</dfn> =&gt; <var>string</var> (8) \"negative\"<div class=\"access-path\">$value[0]['chart_operation']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:36:13\"<div class=\"access-path\">$value[0]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:36:13\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:36:13\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['update_by']</div></dt></dl></dd></dl></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>**********</pre>", "_ci_previous_url": "http://localhost/part/index.php/view_assess_reports/ASSEX66108dae0cdd61712360878", "username": "minad", "name": "<PERSON><PERSON>", "role": "admin", "status": "1", "orgname": "Morobe Provincial Administration", "orglogo": "http://localhost/ocna/public/uploads/org_logo/2345_1681386431.gif", "orgcode": "2345", "orgcountry": "PG", "orgprov": "12"}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost/part/index.php/view_assess_report_items/RGRP6829c62d236c91747568173", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9", "Cookie": "ci_session=80jp0rp2fbg424jl6b28hbmdr586ooor"}, "cookies": {"ci_session": "80jp0rp2fbg424jl6b28hbmdr586ooor"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.3.2", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/part", "timezone": "UTC", "locale": "en", "cspEnabled": false}}