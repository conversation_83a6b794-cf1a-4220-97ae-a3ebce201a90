<?php

namespace Config;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

/*
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();
$routes->setAutoRoute(true);
//$autoRoutesImproved(true);
// The Auto Routing (Legacy) is very dangerous. It is easy to create vulnerable apps
// where controller filters or CSRF protection are bypassed.
// If you don't want to define all routes, please use the Auto Routing (Improved).
// Set `$autoRoutesImproved` to true in `app/Config/Feature.php` and set the following to true.
// $routes->setAutoRoute(false);

/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// We get a performance increase by specifying the default
// route since we don't have to scan directories.
$routes->get('/', 'Home::index');

$routes->get('login', 'Home::login');
$routes->post('login', 'Home::login');
$routes->post('dologin', 'Home::dologin');
$routes->get('logout', 'Home::logout');
$routes->get('about', 'Home::about');
$routes->get('findme/(:any)', 'Home::findme/$1');
$routes->post('gofindme', 'Home::gofindme');
$routes->post('open_profile', 'Home::open_profile');


//=========== Admindash routes ==================================================
$routes->get('dashboard', 'Admindash::index');

//========== employees routes ===================================================
$routes->get('employees', 'Employees::index');
$routes->get('edit_employees/(:any)', 'Employees::edit_employees/$1');
$routes->get('profile_employees/(:any)', 'Employees::profile_employees/$1');

//========== portal routes =======================================================
$routes->get('emp_dashboard', 'Portal::index');
$routes->get('emp_open_activity/(:any)', 'Portal::emp_open_activity/$1');
$routes->get('emp_open_skillcomp/(:any)', 'Portal::emp_open_skillcomp/$1');
$routes->get('emp_open_feedbacks/(:any)', 'Portal::emp_open_feedbacks/$1');
$routes->get('emp_open_feedback_groups/(:any)', 'Portal::emp_open_feedback_groups/$1');

$routes->get('emp_open_skills_comp/(:any)', 'Portal::emp_open_skills_comp/$1');
$routes->get('emp_open_skills_comp_groups/(:any)', 'Portal::emp_open_skills_comp_groups/$1');
$routes->get('emp_open_skills_comp_employees/(:any)', 'Portal::emp_open_skills_comp_employees/$1');




$routes->post('create_employees', 'Employees::create_employees');
$routes->post('update_employees', 'Employees::update_employees');

//========== plans routes =====================================================
$routes->get('plans_manager', 'Plans::index');
$routes->get('view_plans/(:any)', 'Plans::view_plans/$1');
$routes->get('align_plans/(:any)', 'Plans::align_plans/$1');
$routes->get('view_groups/(:any)', 'Plans::view_groups/$1');
$routes->get('view_strategies/(:any)', 'Plans::view_strategies/$1');
$routes->get('view_programs/(:any)', 'Plans::view_programs/$1');

$routes->post('create_plans', 'Plans::create_plans');
$routes->post('update_plans', 'Plans::update_plans');
$routes->post('delete_plans', 'Plans::delete_plans');

$routes->post('create_plan_groups', 'Plans::create_plan_groups');
$routes->post('update_plan_groups', 'Plans::update_plan_groups');
$routes->post('delete_plan_groups', 'Plans::delete_plan_groups');

$routes->post('create_strategies', 'Plans::create_strategies');
$routes->post('update_strategies', 'Plans::update_strategies');
$routes->post('delete_strategies', 'Plans::delete_strategies');

$routes->post('create_programs', 'Plans::create_programs');
$routes->post('update_programs', 'Plans::update_programs');
$routes->post('delete_programs', 'Plans::delete_programs');

$routes->post('create_pro_act', 'Plans::create_pro_act');
$routes->post('update_pro_act', 'Plans::update_pro_act');


//========== Assessments routes =====================================================

$routes->get('assessments_manager', 'Assessments::index');
$routes->get('view_assess_exercises/(:any)', 'Assessments::view_assess_exercises/$1');
$routes->get('open_assess_exercise/(:any)', 'Assessments::open_assess_exercise/$1');
$routes->get('view_assess_items_groups/(:any)', 'Assessments::view_assess_items_groups/$1');
$routes->get('view_assess_items/(:any)', 'Assessments::view_assess_items/$1');
$routes->get('open_assess_score_group/(:any)', 'Assessments::open_assess_score_group/$1');
$routes->get('open_assess_score_items/(:any)', 'Assessments::open_assess_score_items/$1');

$routes->get('view_assess_report_groupings/(:any)', 'Assessments::view_assess_report_groupings/$1');
$routes->get('view_assess_report_items/(:any)', 'Assessments::view_assess_report_items/$1');

//reports
$routes->get('view_assess_reports/(:any)', 'Assessments::view_assess_reports/$1');
$routes->get('view_assess_reports_summary_tables/(:any)', 'Assessments::view_assess_reports_summary_tables/$1');
$routes->get('view_assess_reports_raw_scores/(:any)', 'Assessments::view_assess_reports_raw_scores/$1');

$routes->post('create_assess_plan', 'Assessments::create_assess_plan');
$routes->post('update_assess_plan', 'Assessments::update_assess_plan');
$routes->post('delete_assess_plan', 'Assessments::delete_assess_plan');

$routes->post('create_assessment_exercise', 'Assessments::create_assessment_exercise');
$routes->post('update_assessment_exercise', 'Assessments::update_assessment_exercise');
$routes->post('delete_assessment_exercise', 'Assessments::delete_assessment_exercise');

$routes->post('create_assess_items_group', 'Assessments::create_assess_items_group');
$routes->post('update_assess_items_group', 'Assessments::update_assess_items_group');
$routes->post('delete_assess_items_group', 'Assessments::delete_assess_items_group');

$routes->post('import_assess_items_groups', 'Assessments::import_assess_items_groups');
$routes->post('create_assess_report_group', 'Assessments::create_assess_report_group');
$routes->post('update_assess_report_group', 'Assessments::update_assess_report_group');
$routes->post('delete_assess_report_group', 'Assessments::delete_assess_report_group');

$routes->post('import_assess_items', 'Assessments::import_assess_items');
$routes->post('create_assess_items', 'Assessments::create_assess_items');
$routes->post('update_assess_items', 'Assessments::update_assess_items');
$routes->post('delete_assess_items', 'Assessments::delete_assess_items');

$routes->post('create_assess_report_items', 'Assessments::create_assess_report_items');
$routes->post('delete_assess_report_items', 'Assessments::delete_assess_report_items');

$routes->post('enter_score_items', 'Assessments::enter_score_items');
$routes->post('update_target_rates', 'Assessments::update_target_rates');

//========== activity routes =====================================================
$routes->get('activities', 'Activities::index');
$routes->get('new_activity', 'Activities::new_activity');
$routes->get('open_activity/(:any)', 'Activities::open_activity/$1');
$routes->get('open_qualitative_unpack/(:any)', 'Activities::open_qualitative_unpack/$1');
$routes->get('open_qualitative_item/(:any)', 'Activities::open_qualitative_item/$1');

$routes->post('import_qualitative_data', 'Activities::import_qualitative_data');
$routes->post('create_qualitative_item', 'Activities::create_qualitative_item');
$routes->post('update_qualitative_item', 'Activities::update_qualitative_item');
$routes->post('delete_qualitative_item', 'Activities::delete_qualitative_item');

$routes->post('bg_qualitative_color', 'Activities::bg_qualitative_color');
$routes->post('qualitative_for_score', 'Activities::qualitative_for_score');
$routes->post('download_qualitative_data', 'Activities::download_qualitative_data');
$routes->post('import_qualitative_score', 'Activities::import_qualitative_score');

$routes->post('create_activity', 'Activities::create_activity');
$routes->post('update_activity', 'Activities::update_activity');
$routes->post('update_activity_status', 'Activities::update_activity_status');

//=========================== Reports routes =========================================================  

$routes->get('reports_qualitative_dashboard/(:any)', 'Reports::reports_qualitative_dashboard/$1');














// Dakoii Routes
$routes->get('dakoii', 'Dakoii::index');
$routes->post('dlogin', 'Dakoii::login');
$routes->get('dlogout', 'Dakoii::logout');
$routes->get('ddash', 'Dakoii::ddash');
$routes->get('dopen_org/(:any)', 'Dakoii::open_org/$1');
$routes->get('dlist_org', 'Dakoii::list_org');
$routes->post('daddorg', 'Dakoii::addorg');
$routes->post('deditorg', 'Dakoii::editorg');
$routes->post('dadduser', 'Dakoii::adduser');
$routes->post('daddadmin', 'Dakoii::create_admin');

//testing
$routes->get('testa', 'Test::index');
$routes->get('ajax', 'Test::ajax');
$routes->post('ajax', 'Test::ajax');
$routes->post('ajaxform', 'Test::ajaxform');
$routes->get('ajaxform', 'Test::ajaxform');
$routes->get('testmap', 'Test::testmap');



/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
if (is_file(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php')) {
    require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}
