<?php

namespace App\Models;

use CodeIgniter\Model;

class assess_itemsModel extends Model
{
    protected $table      = 'assess_items';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'ucode', 'orgcode','plan_id','assess_exercise_id','assess_items_groups_id', 'code', 'title','score', 'bg_color','status','status_by','status_at',
        'create_by', 'update_by'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;
}
