<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark"><i class="fas fa-clipboard-check mr-2"></i>Assessments Manager</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard"><i class="fas fa-home"></i></a></li>
                    <li class="breadcrumb-item active">Assessments Manager</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<!-- /.content-header -->

<section class="content">
    <div class="container-fluid">
        <!-- Action Bar -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="<?= base_url() ?>dashboard" class="btn btn-outline-secondary">
                        <i class="fa fa-arrow-left mr-1"></i> Back to Dashboard
                    </a>
                    
                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#create_new">
                        <i class="fa fa-plus-circle mr-1"></i> New Assessment Plan
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Assessment Info Card -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h4 class="text-primary mb-2">Assessment Plans</h4>
                                <p class="text-muted mb-0">
                                    Manage your assessment plans for evaluating student performance and progress.
                                </p>
                            </div>
                            <div class="col-md-4 text-right">
                                <div class="d-inline-block mr-3">
                                    <h5 class="mb-0 badge badge-primary p-2"><?= count($data) ?></h5>
                                    <span class="text-muted">Total Plans</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Table Card -->
        <div class="row">
            <div class="col-md-12">
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fa fa-list-alt mr-1"></i> Assessment Plans</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead class="bg-light">
                                    <tr>
                                        <th>Code</th>
                                        <th>Title</th>
                                        <th>Duration</th>
                                        <th class="text-center" width="120">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data as $row) : ?>
                                        <tr class="plan-row" data-href="<?= base_url() ?>view_assess_exercises/<?= $row['ucode'] ?>">
                                            <td class="font-weight-bold"><?= $row['code'] ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge badge-primary mr-2">
                                                        <i class="fas fa-clipboard-check"></i>
                                                    </span>
                                                    <strong><?= $row['title'] ?></strong>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="text-muted">
                                                    <i class="far fa-calendar-alt mr-1"></i>
                                                    <?= dateforms($row['date_from']) ?> - <?= dateforms($row['date_to']) ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group">
                                                    <a href="<?= base_url() ?>view_assess_exercises/<?= $row['ucode'] ?>" 
                                                       class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-warning" 
                                                            data-toggle="modal" 
                                                            data-target="#edit_plan<?= $row['id'] ?>">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-danger" 
                                                            data-toggle="modal" 
                                                            data-target="#delete<?= $row['id'] ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>

                                                <!-- Delete Modal -->
                                                <div class="modal fade" id="delete<?= $row['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel<?= $row['id'] ?>" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header bg-danger text-white">
                                                                <h5 class="modal-title" id="deleteModalLabel<?= $row['id'] ?>">
                                                                    <i class="fa fa-exclamation-triangle" aria-hidden="true"></i> Confirm Deletion
                                                                </h5>
                                                                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <?= form_open('delete_assess_plan') ?>
                                                            <div class="modal-body">
                                                                <div class="text-center mb-4">
                                                                    <i class="fas fa-trash-alt text-danger fa-3x mb-3"></i>
                                                                    <h5>Are you sure you want to delete this assessment plan?</h5>
                                                                    <div class="mt-3 p-2 bg-light rounded">
                                                                        <p class="mb-0"><strong><?= $row['code'] ?>:</strong> <?= $row['title'] ?></p>
                                                                    </div>
                                                                </div>
                                                                <p class="text-muted small text-center">This action cannot be undone.</p>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <input type="hidden" name="id" value="<?= $row['id'] ?>">
                                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                                <button type="submit" class="btn btn-danger">
                                                                    <i class="fa fa-trash mr-1" aria-hidden="true"></i> Delete
                                                                </button>
                                                            </div>
                                                            <?= form_close() ?>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Edit Modal -->
                                                <div class="modal fade" id="edit_plan<?= $row['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="editModalLabel<?= $row['id'] ?>" aria-hidden="true">
                                                    <div class="modal-dialog modal-lg" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header bg-warning">
                                                                <h5 class="modal-title" id="editModalLabel<?= $row['id'] ?>">
                                                                    <i class="fas fa-edit"></i> Edit Assessment Plan
                                                                </h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <?= form_open_multipart('update_assess_plan') ?>
                                                            <div class="modal-body">
                                                                <div class="form-group">
                                                                    <label for="code<?= $row['id'] ?>">Code</label>
                                                                    <input type="text" 
                                                                           class="form-control" 
                                                                           id="code<?= $row['id'] ?>" 
                                                                           name="code" 
                                                                           placeholder="Plan Code" 
                                                                           value="<?= $row['code'] ?>" 
                                                                           required>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label for="title<?= $row['id'] ?>">Title</label>
                                                                    <input type="text" 
                                                                           class="form-control" 
                                                                           id="title<?= $row['id'] ?>" 
                                                                           name="title" 
                                                                           placeholder="Plan Title" 
                                                                           value="<?= $row['title'] ?>" 
                                                                           required>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="date_from<?= $row['id'] ?>">Start Date</label>
                                                                            <input type="date" 
                                                                                   class="form-control" 
                                                                                   id="date_from<?= $row['id'] ?>" 
                                                                                   name="date_from" 
                                                                                   value="<?= $row['date_from'] ?>" 
                                                                                   required>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="date_to<?= $row['id'] ?>">End Date</label>
                                                                            <input type="date" 
                                                                                   class="form-control" 
                                                                                   id="date_to<?= $row['id'] ?>" 
                                                                                   name="date_to" 
                                                                                   value="<?= $row['date_to'] ?>" 
                                                                                   required>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <input type="hidden" name="id" value="<?= $row['id'] ?>">
                                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                                <button type="submit" class="btn btn-warning">
                                                                    <i class="fa fa-save mr-1" aria-hidden="true"></i> Save Changes
                                                                </button>
                                                            </div>
                                                            <?= form_close() ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                    
                                    <?php if(empty($data)): ?>
                                        <tr>
                                            <td colspan="4" class="text-center py-4">
                                                <div class="py-3">
                                                    <i class="fas fa-info-circle text-muted fa-2x mb-3"></i>
                                                    <h5 class="text-muted">No Assessment Plans Found</h5>
                                                    <p class="text-muted small">Create your first assessment plan to get started</p>
                                                    <button class="btn btn-primary mt-2" data-toggle="modal" data-target="#create_new">
                                                        <i class="fa fa-plus-circle mr-1"></i> Create New Plan
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-light">
                        <small class="text-muted">
                            <i class="fas fa-info-circle mr-1"></i> 
                            Click on a row to view the assessment exercises.
                        </small>
                        <?php if(!empty($data)): ?>
                        <span class="badge badge-primary float-right"><?= count($data) ?> plans</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Create New Modal -->
    <div class="modal fade" id="create_new" tabindex="-1" role="dialog" aria-labelledby="createModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="createModalLabel">
                        <i class="fa fa-plus-circle" aria-hidden="true"></i> Create New Assessment Plan
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <?= form_open_multipart('create_assess_plan') ?>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="code">Plan Code</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="code" 
                                       name="code" 
                                       placeholder="Enter code" 
                                       required>
                                <small class="form-text text-muted">A unique identifier for this plan</small>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="title">Plan Title</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="title" 
                                       name="title" 
                                       placeholder="Enter descriptive title" 
                                       required>
                                <small class="form-text text-muted">A descriptive name for this assessment plan</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_from">Start Date</label>
                                <input type="date" 
                                       class="form-control" 
                                       id="date_from" 
                                       name="date_from" 
                                       required>
                                <small class="form-text text-muted">When the assessment begins</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_to">End Date</label>
                                <input type="date" 
                                       class="form-control" 
                                       id="date_to" 
                                       name="date_to" 
                                       required>
                                <small class="form-text text-muted">When the assessment ends</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-save mr-1" aria-hidden="true"></i> Create Plan
                    </button>
                </div>
                <?= form_close() ?>
            </div>
        </div>
    </div>
</section>

<script>
    // Initialize Bootstrap components
    $(document).ready(function() {
        // This ensures modals work correctly
        $('.modal').modal({
            show: false
        });
        
        // Simple script to make rows clickable
        $(".plan-row").click(function(e) {
            // Don't trigger when clicking buttons or links
            if (!$(e.target).closest('button').length && !$(e.target).closest('a').length) {
                window.location = $(this).data("href");
            }
        });
    });
</script>

<?= $this->endSection() ?>