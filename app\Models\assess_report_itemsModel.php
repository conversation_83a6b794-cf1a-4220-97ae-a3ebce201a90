<?php

namespace App\Models;

use CodeIgniter\Model;

class assess_report_itemsModel extends Model
{
    protected $table      = 'assess_report_items';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'ucode', 'orgcode', 'plan_id', 'assess_exercise_id', 'assess_items_groups_id', 'assess_report_groups_id', 'code', 'title',
        'create_by', 'update_by'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;
}
