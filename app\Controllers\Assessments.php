<?php

namespace App\Controllers;

use App\Models\activitiesModel;
use App\Models\assess_ExercisesModel;
use App\Models\assess_items_GroupsModel;
use App\Models\assess_itemsModel;
use App\Models\assess_PlansModel;
use App\Models\assess_report_GroupsModel;
use App\Models\assess_report_itemsModel;
use App\Models\assessareaModel;
use App\Models\emp_feedbacksModel;
use App\Models\employeesModel;
use App\Models\eventsModel;
use App\Models\plansModel;
use App\Models\questionsModel;
use App\Models\usersModel;

class Assessments extends BaseController
{
    public $session;
    public $usersModel;
    public $asssess_exercisesModel;
    public $assess_items_groupsModel;
    public $assess_report_groupsModel;
    public $assess_itemsModel;
    public $assess_report_itemsModel;
    public $assess_plansModel;



    public function __construct()
    {
        helper(['form', 'url', 'info', 'chart']);
        $this->session = session();

        $this->usersModel = new usersModel();
        $this->asssess_exercisesModel = new assess_ExercisesModel();
        $this->assess_items_groupsModel = new assess_items_GroupsModel();
        $this->assess_report_groupsModel = new assess_report_GroupsModel();
        $this->assess_itemsModel = new assess_itemsModel();
        $this->assess_report_itemsModel = new assess_report_itemsModel();
        $this->assess_plansModel = new assess_PlansModel();
    }

    public function index()
    {
        $data['title'] = "Assessment Manager";
        $data['menu'] = "assessments_manager";

        $data['data'] = $this->assess_plansModel->where('orgcode', session('orgcode'))->find();

        echo view('assessments/assessments_manager', $data);
    }

    public function create_assess_plan()
    {

        $title = $this->request->getVar('title');
        $code = $this->request->getVar('code');
        $datefrom = $this->request->getVar('date_from');
        $dateto = $this->request->getVar('date_to');

        $data = [
            'ucode' => "ASPLN" . uniqid() . time(),
            'orgcode' => session('orgcode'),
            'code' => $code,
            'title' => $title,
            'date_from' => $datefrom,
            'date_to' => $dateto,
            'status' => 'active',
            'status_by' => session('name'),
            'status_at' => date('Y-m-d H:i:s'),
            'create_by' => session('name'),
        ];

        $this->assess_plansModel->insert($data);

        return redirect()->back()->with('success', 'Plan Registered successfully');
    }

    public function update_assess_plan()
    {

        $title = $this->request->getVar('title');
        $code = $this->request->getVar('code');
        $datefrom = $this->request->getVar('date_from');
        $dateto = $this->request->getVar('date_to');
        $id = $this->request->getVar('id');

        $data = [
            'code' => $code,
            'title' => $title,
            'date_from' => $datefrom,
            'date_to' => $dateto,
            'updated_by' => session('name'),
        ];

        $this->assess_plansModel->update($id, $data);

        return redirect()->back()->with('success', 'Plan Saved');
    }

    public function delete_assess_plan()
    {
        $id = $this->request->getVar('id');
        $data = $this->asssess_exercisesModel->where('plan_id', $id)->first();
        if (!empty($data)) {
            return redirect()->back()->with('error', 'Cannot delete plan, plan has assessment exercises');
        } else {
            $this->assess_plansModel->where('id', $id)->delete();

            return redirect()->back()->with('success', 'Plan Deleted');
        }
    }

    public function view_assess_exercises($ucode)
    {
        $data['title'] = "Assessment Manager";
        $data['menu'] = "dashboard";

        $data['plan'] = $this->assess_plansModel->where('ucode', $ucode)->where('orgcode', session('orgcode'))->first();
        $data['data'] = $this->asssess_exercisesModel->where('plan_id', $data['plan']['id'])->where('orgcode', session('orgcode'))->find();

        echo view('assessments/view_assess_exercises', $data);
    }


    public function create_assessment_exercise()
    {

        $title = $this->request->getVar('title');
        $description = $this->request->getVar('description');
        $datefrom = $this->request->getVar('date_from');
        $dateto = $this->request->getVar('date_to');
        $plan_id = $this->request->getVar('plan_id');

        $get_code = $this->asssess_exercisesModel->where('orgcode', session('orgcode'))->orderBy('id', 'desc')->first();
        $code = "";
        if (empty($get_code)) {
            $code = "ASX" . session('orgcode') . 1;
        } else {

            $originalString = $get_code['code'];
            $substringToRemove = "ASX" . session('orgcode');
            $trimmedString = str_replace($substringToRemove, "", $originalString);

            $convertedInt = intval($trimmedString);

            $convertedInt = $convertedInt + 1;

            $code = "ASX" . session('orgcode') . ($convertedInt);
        }

        $data = [
            'ucode' => "ASSEX" . uniqid() . time(),
            'orgcode' => session('orgcode'),
            'plan_id' => $plan_id,
            'code' => $code,
            'title' => $title,
            'description' => $description,
            'date_from' => $datefrom,
            'date_to' => $dateto,
            'status' => 'active',
            'status_by' => session('name'),
            'status_at' => date('Y-m-d H:i:s'),
            'create_by' => session('name'),
        ];

        $this->asssess_exercisesModel->insert($data);

        return redirect()->back()->with('success', 'Assessment exercise created successfully');
    }

    public function update_assessment_exercise()
    {

        $title = $this->request->getVar('title');
        $description = $this->request->getVar('description');
        $datefrom = $this->request->getVar('date_from');
        $dateto = $this->request->getVar('date_to');
        $id = $this->request->getVar('id');

        $data = [
            'title' => $title,
            'description' => $description,
            'date_from' => $datefrom,
            'date_to' => $dateto,
            'update_by' => session('name'),
        ];

        $this->asssess_exercisesModel->update($id, $data);

        return redirect()->back()->with('success', 'Assessment exercise updated successfully');
    }


    public function delete_assessment_exercise()
    {

        $id = $this->request->getVar('id');

        $data = $this->assess_items_groupsModel->where('assess_exercise_id', $id)->first();

        if (!empty($data)) {
            return redirect()->back()->with('error', 'Cannot delete exercise, exercise has assessment groups');
        } else {
            $this->asssess_exercisesModel->where('id', $id)->delete();
        }

        return redirect()->back()->with('success', 'Assessment exercise updated successfully');
    }

    public function open_assess_exercise($ucode)
    {
        $data['title'] = "Assessment Exercise";
        $data['menu'] = "dashboard";


        $data['assex'] = $this->asssess_exercisesModel->where('ucode', $ucode)->where('orgcode', session('orgcode'))->first();
        $data['plan'] = $this->assess_plansModel->where('id', $data['assex']['plan_id'])->where('orgcode', session('orgcode'))->first();

        echo view('assessments/open_assess_exercise', $data);
    }


    public function view_assess_items_groups($ucode)
    {
        $data['title'] = "Assess Groups";
        $data['menu'] = "dashboard";

        $data['assex'] = $this->asssess_exercisesModel->where('ucode', $ucode)->where('orgcode', session('orgcode'))->first();
        $data['plan'] = $this->assess_plansModel->where('id', $data['assex']['plan_id'])->where('orgcode', session('orgcode'))->first();
        $data['data'] = $this->assess_items_groupsModel->where('assess_exercise_id', $data['assex']['id'])->where('orgcode', session('orgcode'))->find();

        $item_groups = array();
        foreach ($data['data'] as $row) {
            $item_groups[] = $row['id'];
        }

        if (!empty($item_groups)) {
            $data['items'] = $this->assess_itemsModel->whereIn('assess_items_groups_id', $item_groups)->find();
        }

        echo view('assessments/view_assess_items_groups', $data);
    }


    public function import_assess_items_groups()
    {
        $file = $this->request->getFile('csv_data');
        $assex_id = $this->request->getVar('assex_id');
        $plan_id = $this->request->getVar('plan_id');

        if ($file->isValid() && $file->getExtension() === 'csv') {
            $csvData = array_map('str_getcsv', file($file->getPathname()));


            // Skip header row
            unset($csvData[0]);
            $x = 0;
            
            
            $newArray = [];
            $previousElement = [];

            foreach ($csvData as $element) {
                if (count($element) === 1) {
                    $previousElement[1] .= $element[0];
                } elseif (count($element) > 2) {
                    $previousElement[1] .= $element[0];
                    $previousElement[1] .= implode(" ", array_slice($element, 1));
                } else {
                    if (!empty($previousElement)) {
                        $newArray[] = $previousElement;
                    }
                    $previousElement = $element;
                }
            }

            $newArray[] = $previousElement;

            // Remove arrays with empty items
            $newArray = array_filter($newArray, function ($arr) {
                return !in_array("", $arr, true);
            });
            
            foreach ($newArray as $row) {

                $get_data = $this->assess_items_groupsModel->where('assess_exercise_id', $assex_id)->find();

                foreach ($get_data as $item) {
                    if ($item['code'] == $row[0]) {
                        return redirect()->back()->with('error', 'Item code ' . $row[0] . ' already exists');
                    }
                }

                // Remove double quote at the end of $row[1] if it exists
                $title = rtrim($row[1], '"');

                $data = [
                    'ucode' => "IGRP" . uniqid() . time(),
                    'orgcode' => session('orgcode'),
                    'plan_id' => $plan_id,
                    'assess_exercise_id' => $assex_id,
                    'code' => $row[0],
                    'title' => $title,
                    'status' => 'active',
                    'status_by' => session('name'),
                    'status_at' => date('Y-m-d H:i:s'),
                    'create_by' => session('name'),
                ];

                $this->assess_items_groupsModel->insert($data);
                // }
                $x++;
            }
            // Redirect or show success message
            return redirect()->back()->with('success', 'CSV data imported successfully.');
        } else {
            // Handle invalid file type or other errors
            return redirect()->back()->with('error', 'Invalid file format or file not uploaded.');
        }
        //Status - 
        return redirect()->back()->with('error', 'Import Failed!');
    }



    public function create_assess_items_group()
    {
        $code = $this->request->getVar('code');
        $title = $this->request->getVar('title');
        $assex_id = $this->request->getVar('assex_id');
        $plan_id = $this->request->getVar('plan_id');

        $data = [
            'ucode' => "IGRP" . uniqid() . time(),
            'orgcode' => session('orgcode'),
            'plan_id' => $plan_id,
            'assess_exercise_id' => $assex_id,
            'code' => $code,
            'title' => $title,
            'status' => 'active',
            'status_by' => session('name'),
            'status_at' => date('Y-m-d H:i:s'),
            'create_by' => session('name'),
        ];

        $this->assess_items_groupsModel->insert($data);

        return redirect()->back()->with('success', 'Items Group created successfully');
    }

    public function update_assess_items_group()
    {
        $code = $this->request->getVar('code');
        $title = $this->request->getVar('title');
        $id = $this->request->getVar('id');

        $data = [

            'code' => $code,
            'title' => $title,
            'update_by' => session('name'),
        ];

        $this->assess_items_groupsModel->update($id, $data);

        return redirect()->back()->with('success', 'Items Group Updated successfully');
    }

    public function delete_assess_items_group()
    {
        $id = $this->request->getVar('id');

        $get_data = $this->assess_itemsModel->where('assess_items_groups_id', $id)->first();

        if (!empty($get_data)) {
            return redirect()->back()->with('error', 'This group contains items and cannot be deleted. Please delete items first');
        } else {

            $get_id = $this->assess_report_itemsModel->where('assess_items_groups_id', $id)->first();
            $this->assess_report_itemsModel->where('id', $get_id['id'])->delete();
            $this->assess_items_groupsModel->where('id', $id)->delete();


            return redirect()->back()->with('success', 'Group Deleted successfully');
        }
    }


    public function view_assess_items($ucode)
    {
        $data['title'] = "Assess Items";
        $data['menu'] = "dashboard";

        $data['group'] = $this->assess_items_groupsModel->where('ucode', $ucode)->where('orgcode', session('orgcode'))->first();
        $data['assex'] = $this->asssess_exercisesModel->where('id', $data['group']['assess_exercise_id'])->where('orgcode', session('orgcode'))->first();
        $data['plan'] = $this->assess_plansModel->where('id', $data['assex']['plan_id'])->where('orgcode', session('orgcode'))->first();
        $data['data'] = $this->assess_itemsModel->where('assess_items_groups_id', $data['group']['id'])->where('orgcode', session('orgcode'))->find();

        echo view('assessments/view_assess_items', $data);
    }


    public function import_assess_items()
    {
        $file = $this->request->getFile('csv_data');
        $assex_id = $this->request->getVar('assex_id');
        $group_id = $this->request->getVar('group_id');
        $plan_id = $this->request->getVar('plan_id');


        if ($file->isValid() && $file->getExtension() === 'csv') {
            $csvData = array_map('str_getcsv', file($file->getPathname()));


            // Skip header row
            unset($csvData[0]);
            $x = 0;

            $newArray = [];
            $previousElement = [];

            foreach ($csvData as $element) {
                if (count($element) === 1) {
                    $previousElement[1] .= $element[0];
                } elseif (count($element) > 2) {
                    $previousElement[1] .= $element[0];
                    $previousElement[1] .= implode(" ", array_slice($element, 1));
                } else {
                    if (!empty($previousElement)) {
                        $newArray[] = $previousElement;
                    }
                    $previousElement = $element;
                }
            }

            $newArray[] = $previousElement;

            // Remove arrays with empty items
            $newArray = array_filter($newArray, function ($arr) {
                return !in_array("", $arr, true);
            });

            /*  echo "<pre>";
            print_r($newArray); */

            /*  echo "<pre>";
            print_r($csvData); */

            foreach ($newArray as $row) {

                $get_data = $this->assess_itemsModel->where('assess_items_groups_id', $group_id)->find();

                foreach ($get_data as $item) {
                    if ($item['code'] == $row[0]) {
                        return redirect()->back()->with('error', 'Item code ' . $row[0] . ' already exists');
                    }
                }

                // Remove double quote at the end of $row[1] if it exists
                $title = rtrim($row[1], '"');

                $data = [
                    'ucode' => "ASSIT" . uniqid() . time(),
                    'orgcode' => session('orgcode'),
                    'plan_id' => $plan_id,
                    'assess_exercise_id' => $assex_id,
                    'assess_items_groups_id' => $group_id,
                    'code' => $row[0],
                    'title' => $title,
                    'status' => 'active',
                    'status_by' => session('name'),
                    'status_at' => date('Y-m-d H:i:s'),
                    'create_by' => session('name'),
                ];

                $this->assess_itemsModel->insert($data);

                $x++;
            }
            // Redirect or show success message
            return redirect()->back()->with('success', 'CSV data imported successfully.');
        } else {
            // Handle invalid file type or other errors
            return redirect()->back()->with('error', 'Invalid file format or file not uploaded.');
        }
        //Status - 
        return redirect()->back()->with('error', 'Import Failed!');
    }


    public function create_assess_items()
    {

        $code = $this->request->getVar('code');
        $title = $this->request->getVar('title');
        $assex_id = $this->request->getVar('assex_id');
        $group_id = $this->request->getVar('group_id');
        $plan_id = $this->request->getVar('plan_id');

        $data = [
            'ucode' => "ASSIT" . uniqid() . time(),
            'orgcode' => session('orgcode'),
            'plan_id' => $plan_id,
            'assess_exercise_id' => $assex_id,
            'assess_items_groups_id' => $group_id,
            'code' => $code,
            'title' => $title,
            'status' => 'active',
            'status_by' => session('name'),
            'status_at' => date('Y-m-d H:i:s'),
            'create_by' => session('name'),
        ];

        $this->assess_itemsModel->insert($data);

        return redirect()->back()->with('success', 'Assessment Items created successfully!');
    }


    public function update_assess_items()
    {

        $code = $this->request->getVar('code');
        $title = $this->request->getVar('title');
        $id = $this->request->getVar('id');

        $data = [
            'code' => $code,
            'title' => $title,
            'update_by' => session('name'),
        ];

        $this->assess_itemsModel->update($id, $data);

        return redirect()->back()->with('success', 'Assessment Items Updated successfully!');
    }

    public function delete_assess_items()
    {

        $id = $this->request->getVar('id');
        $this->assess_itemsModel->where('id', $id)->delete();

        return redirect()->back()->with('success', 'Item Deleted successfully!');
    }

    public function view_assess_report_groupings($ucode)
    {
        $data['title'] = "Report Groupings";
        $data['menu'] = "dashboard";


        $data['assex'] = $this->asssess_exercisesModel->where('ucode', $ucode)->where('orgcode', session('orgcode'))->first();
        $data['plan'] = $this->assess_plansModel->where('id', $data['assex']['plan_id'])->where('orgcode', session('orgcode'))->first();
        $data['items_groups'] = $this->assess_items_groupsModel->where('assess_exercise_id', $data['assex']['id'])->where('orgcode', session('orgcode'))->find();
        $data['data'] = $this->assess_report_groupsModel->where('assess_exercise_id', $data['assex']['id'])->where('orgcode', session('orgcode'))->find();

        echo view('assessments/view_assess_report_groupings', $data);
    }

    public function create_assess_report_group()
    {
        $code = $this->request->getVar('code');
        $title = $this->request->getVar('title');
        $assex_id = $this->request->getVar('assex_id');
        $plan_id = $this->request->getVar('plan_id');
        $bg_color = $this->request->getVar('bg_color');
        $chart_types = $this->request->getVar('chart_types');
        $chart_operation = $this->request->getVar('chart_operation');

        $data = [
            'ucode' => "RGRP" . uniqid() . time(),
            'orgcode' => session('orgcode'),
            'plan_id' => $plan_id,
            'assess_exercise_id' => $assex_id,
            'code' => $code,
            'title' => $title,
            'status' => 'active',
            'bg_color' => $bg_color,
            'chart_type' => $chart_types,
            'chart_operation' => $chart_operation,
            'status_by' => session('name'),
            'status_at' => date('Y-m-d H:i:s'),
            'create_by' => session('name'),
        ];

        $this->assess_report_groupsModel->insert($data);

        return redirect()->back()->with('success', 'Report Group created successfully');
    }

    public function update_assess_report_group()
    {
        $code = $this->request->getVar('code');
        $title = $this->request->getVar('title');
        $id = $this->request->getVar('id');
        $bg_color = $this->request->getVar('bg_color');
        $chart_types = $this->request->getVar('chart_types');
        $chart_operation = $this->request->getVar('chart_operation');
        if (($chart_types != "barChart")) {
            $chart_operation = 'positive';
        }

        $data = [
            'code' => $code,
            'title' => $title,
            'bg_color' => $bg_color,
            'chart_type' => $chart_types,
            'chart_operation' => $chart_operation,
            'update_by' => session('name'),
        ];

        $this->assess_report_groupsModel->update($id, $data);

        return redirect()->back()->with('success', 'Report Group created successfully');
    }

    public function update_target_rates()
    {
        $id = $this->request->getVar('id');
        $max_rate = $this->request->getVar('max_rate');
        $min_rate = $this->request->getVar('min_rate');

        $data = [
            'max_rate' => $max_rate,
            'min_rate' => $min_rate,
            'update_by' => session('name'),
        ];

        $this->asssess_exercisesModel->update($id, $data);

        return redirect()->back()->with('success', 'Target rates set');
    }

    public function delete_assess_report_group()
    {
        $id = $this->request->getVar('id');

        $get_data = $this->assess_report_itemsModel->where('assess_report_groups_id', $id)->first();

        if (!empty($get_data)) {
            return redirect()->back()->with('error', 'This group has imported items. Please remove the items before delete!');
        } else {
            $this->assess_report_groupsModel->where('id', $id)->delete();

            return redirect()->back()->with('success', 'Report Group Deleted successfully');
        }
    }

    public function view_assess_report_items($ucode)
    {
        $data['title'] = "Report Items";
        $data['menu'] = "dashboard";

        $data['report_groups'] = $this->assess_report_groupsModel->where('ucode', $ucode)->where('orgcode', session('orgcode'))->first();
        $data['assex'] = $this->asssess_exercisesModel->where('id', $data['report_groups']['assess_exercise_id'])->where('orgcode', session('orgcode'))->first();
        $data['plan'] = $this->assess_plansModel->where('id', $data['assex']['plan_id'])->where('orgcode', session('orgcode'))->first();

        $data['data'] = $this->assess_report_itemsModel->where('assess_report_groups_id', $data['report_groups']['id'])->where('orgcode', session('orgcode'))->find();
        $data['items_groups'] = array();
        $report_items = array();
        foreach ($data['data'] as $row) {
            $report_items[] = $row['assess_items_groups_id'];
        }

        //print_r($report_items);
        //get items groups
        if (!empty($report_items)) {
            $data['items_groups'] = $this->assess_items_groupsModel->where('assess_exercise_id', $data['report_groups']['assess_exercise_id'])->where('orgcode', session('orgcode'))->whereNotIn('id', $report_items)->find();
        } else {
            $data['items_groups'] = $this->assess_items_groupsModel->where('assess_exercise_id', $data['report_groups']['assess_exercise_id'])->where('orgcode', session('orgcode'))->find();
        }

        echo view('assessments/view_assess_report_items', $data);
    }

    public function create_assess_report_items()
    {
        $report_groups_id = $this->request->getVar('report_groups_id');
        $items_groups_id = $this->request->getVar('items_groups_id');
        $assex_id = $this->request->getVar('assex_id');
        $code = $this->request->getVar('code');
        $title = $this->request->getVar('title');
        $plan_id = $this->request->getVar('plan_id');

        $data = [
            'ucode' => "ARI" . uniqid() . time(),
            'orgcode' => session('orgcode'),
            'plan_id' => $plan_id,
            'assess_exercise_id' => $assex_id,
            'assess_report_groups_id' => $report_groups_id,
            'assess_items_groups_id' => $items_groups_id,
            'code' => $code,
            'title' => $title,
            'status' => 'active',
            'status_by' => session('name'),
            'status_at' => date('Y-m-d H:i:s'),
            'create_by' => session('name'),
        ];

        $this->assess_report_itemsModel->insert($data);

        return redirect()->back()->with('success', 'Report Item Imported successfully');
    }

    public function delete_assess_report_items()
    {
        $id = $this->request->getVar('id');

        $this->assess_report_itemsModel->where('id', $id)->delete();

        return redirect()->back()->with('success', 'Report Item Removed successfully!');
    }


    public function open_assess_score_group($ucode)
    {
        $data['title'] = "Scoreboard";
        $data['menu'] = "dashboard";

        $data['assex'] = $this->asssess_exercisesModel->where('ucode', $ucode)->where('orgcode', session('orgcode'))->first();
        $data['plan'] = $this->assess_plansModel->where('id', $data['assex']['plan_id'])->where('orgcode', session('orgcode'))->first();
        $data['item_groups'] = $this->assess_items_groupsModel->where('assess_exercise_id', $data['assex']['id'])->where('orgcode', session('orgcode'))->find();

        $get_groups = array();
        foreach ($data['item_groups'] as $row) {
            $get_groups[] = $row['id'];
        }

        $data['items'] = $this->assess_itemsModel->whereIn('assess_items_groups_id', $get_groups)->where('orgcode', session('orgcode'))->find();

        if (($data['assex']['max_rate'] == "" || ($data['assex']['min_rate'] == ""))) {
            return redirect()->back()->with('error', 'Please set Max and Min Rate in Report Setting first!');
        }

        echo view('assessments/open_assess_score_group', $data);
    }


    public function open_assess_score_items($ucode)
    {
        $data['title'] = "Score Items";
        $data['menu'] = "dashboard";


        $data['item_groups'] = $this->assess_items_groupsModel->where('ucode', $ucode)->where('orgcode', session('orgcode'))->first();
        $data['assex'] = $this->asssess_exercisesModel->where('id', $data['item_groups']['assess_exercise_id'])->where('orgcode', session('orgcode'))->first();
        $data['plan'] = $this->assess_plansModel->where('id', $data['assex']['plan_id'])->where('orgcode', session('orgcode'))->first();
        $data['assess_items'] = $this->assess_itemsModel->where('assess_items_groups_id', $data['item_groups']['id'])->where('orgcode', session('orgcode'))->find();

        echo view('assessments/open_assess_score_items', $data);
    }

    public function enter_score_items()
    {
        $id = $this->request->getVar('id');
        $score = $this->request->getVar('score');

        $data = [
            'score' => $score,
            'update_by' => session('name'),
        ];

        $this->assess_itemsModel->update($id, $data);

        // Not a POST request
        $response = ['status' => 'success', 'message' => "$score GPS Info Saved."];
        return $this->response->setJSON($response);

        //return redirect()->back()->with('success', 'Report Item Imported successfully');
    }


    public function view_assess_reports($ucode)
    {
        $data['title'] = "Report Dashboard";
        $data['menu'] = "dashboard";

        $data['assex'] = $this->asssess_exercisesModel->where('ucode', $ucode)->where('orgcode', session('orgcode'))->first();
        $data['plan'] = $this->assess_plansModel->where('id', $data['assex']['plan_id'])->where('orgcode', session('orgcode'))->first();
        $assex_id = $data['assex']['id'];

        $data['report_items'] = $this->assess_report_itemsModel->where('assess_exercise_id', $assex_id)->where('orgcode', session('orgcode'))->find();
        $report_groups = array();
        foreach ($data['report_items'] as $row) {
            $report_groups[] = $row['assess_report_groups_id'];
        }
        $data['report_groups'] = array();
        if (!empty($report_groups)) {
            $data['report_groups'] = $this->assess_report_groupsModel->whereIn('id', $report_groups)->where('assess_exercise_id', $assex_id)->where('orgcode', session('orgcode'))->find();
        }

        $data['items'] = $this->assess_itemsModel->where('assess_exercise_id', $assex_id)->where('orgcode', session('orgcode'))->find();

        $items_groups = array();
        $items_data = 0;
        foreach ($data['items'] as $row) {
            $items_groups[] = $row['assess_items_groups_id'];
            $items_data += $row['score'];
        }
        $data['items_groups'] = array();
        if (!empty($items_groups)) {
            $data['items_groups'] = $this->assess_items_groupsModel->whereIn('id', $items_groups)->where('assess_exercise_id', $assex_id)->where('orgcode', session('orgcode'))->find();
        }

        if ($items_data <= 0) {
            return redirect()->back()->with('error', 'No items scored yet');
        }

        if (($data['assex']['max_rate'] == "" || ($data['assex']['min_rate'] == ""))) {
            return redirect()->back()->with('error', 'Please set Max and Min Rating Targets in Report Setting first!');
        }

        echo view('assessments/view_assess_reports', $data);
    }
    
    
    public function view_assess_reports_summary_tables($ucode)
    {
        $data['title'] = "Report Dashboard";
        $data['menu'] = "dashboard";

        $data['assex'] = $this->asssess_exercisesModel->where('ucode', $ucode)->where('orgcode', session('orgcode'))->first();
        $data['plan'] = $this->assess_plansModel->where('id', $data['assex']['plan_id'])->where('orgcode', session('orgcode'))->first();
        $assex_id = $data['assex']['id'];

        $data['report_items'] = $this->assess_report_itemsModel->where('assess_exercise_id', $assex_id)->where('orgcode', session('orgcode'))->find();
        $report_groups = array();
        foreach ($data['report_items'] as $row) {
            $report_groups[] = $row['assess_report_groups_id'];
        }
        $data['report_groups'] = array();
        if (!empty($report_groups)) {
            $data['report_groups'] = $this->assess_report_groupsModel->whereIn('id', $report_groups)->where('assess_exercise_id', $assex_id)->where('orgcode', session('orgcode'))->find();
        }

        $data['items'] = $this->assess_itemsModel->where('assess_exercise_id', $assex_id)->where('orgcode', session('orgcode'))->find();

        $items_groups = array();
        $items_data = 0;
        foreach ($data['items'] as $row) {
            $items_groups[] = $row['assess_items_groups_id'];
            $items_data += $row['score'];
        }
        $data['items_groups'] = array();
        if (!empty($items_groups)) {
            $data['items_groups'] = $this->assess_items_groupsModel->whereIn('id', $items_groups)->where('assess_exercise_id', $assex_id)->where('orgcode', session('orgcode'))->find();
        }

        if ($items_data <= 0) {
            return redirect()->back()->with('error', 'No items scored yet');
        }

        if (($data['assex']['max_rate'] == "" || ($data['assex']['min_rate'] == ""))) {
            return redirect()->back()->with('error', 'Please set Max and Min Rating Targets in Report Setting first!');
        }

        echo view('assessments/view_assess_reports_summary_tables', $data);
    }




    public function view_assess_reports_raw_scores($ucode)
    {
        $data['title'] = "Report Raw Scores";
        $data['menu'] = "dashboard";

        $data['assex'] = $this->asssess_exercisesModel->where('ucode', $ucode)->where('orgcode', session('orgcode'))->first();
        $data['plan'] = $this->assess_plansModel->where('id', $data['assex']['plan_id'])->where('orgcode', session('orgcode'))->first();
        $assex_id = $data['assex']['id'];

        $data['report_items'] = $this->assess_report_itemsModel->where('assess_exercise_id', $assex_id)->where('orgcode', session('orgcode'))->find();
        $report_groups = array();
        foreach ($data['report_items'] as $row) {
            $report_groups[] = $row['assess_report_groups_id'];
        }
        $data['report_groups'] = array();
        if (!empty($report_groups)) {
            $data['report_groups'] = $this->assess_report_groupsModel->whereIn('id', $report_groups)->where('assess_exercise_id', $assex_id)->where('orgcode', session('orgcode'))->find();
        }

        $data['items'] = $this->assess_itemsModel->where('assess_exercise_id', $assex_id)->where('orgcode', session('orgcode'))->find();

        $items_groups = array();
        $items_data = 0;
        foreach ($data['items'] as $row) {
            $items_groups[] = $row['assess_items_groups_id'];
            $items_data += $row['score'];
        }
        $data['items_groups'] = array();
        if (!empty($items_groups)) {
            $data['items_groups'] = $this->assess_items_groupsModel->whereIn('id', $items_groups)->where('assess_exercise_id', $assex_id)->where('orgcode', session('orgcode'))->find();
        }

        if ($items_data <= 0) {
            return redirect()->back()->with('error', 'No items scored yet');
        }

        if (($data['assex']['max_rate'] == "" || ($data['assex']['min_rate'] == ""))) {
            return redirect()->back()->with('error', 'Please set Max and Min Rating Targets in Report Setting first!');
        }

        echo view('assessments/view_assess_reports_raw_scores', $data);
    }
}
