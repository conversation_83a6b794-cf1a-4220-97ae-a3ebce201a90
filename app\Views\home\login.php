<?= $this->extend("templates/nolstemp"); ?>
<?= $this->section('content'); ?>

<div class="row d-flex justify-content-center align-items-center">
    <div class="col-md-6 col-lg-5 col-xl-4">
        <div class="card border-0 shadow-lg rounded-3">
            <!-- Card Header -->
            <div class="card-header bg-primary text-white p-4 text-center border-0">
                <h3 class="fw-light mb-0">
                    <i class="fa-solid fa-right-to-bracket me-2"></i>Sign In
                </h3>
            </div>
            
            <!-- Card Body -->
            <div class="card-body p-4 p-md-5">
                <!-- Error Message -->
                <?php if (session()->has('error')) : ?>
                    <div class="alert alert-danger d-flex align-items-center" role="alert">
                        <i class="fa-solid fa-circle-exclamation me-2"></i>
                        <div><?= session('error') ?></div>
                    </div>
                <?php endif; ?>
                
                <!-- Login Form -->
                <?= form_open('dologin', ['class' => 'needs-validation']) ?>
                    <div class="mb-4">
                        <label for="username" class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fa-solid fa-user text-primary"></i>
                            </span>
                            <input type="text" class="form-control" id="username" name="username" placeholder="Enter your username" required>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fa-solid fa-lock text-primary"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 mt-5">
                        <button type="submit" class="btn btn-primary py-3 fw-bold">
                            Sign In
                        </button>
                    </div>
                <?= form_close() ?>
            </div>
            
            <!-- Card Footer -->
            <div class="card-footer bg-light p-4 text-center border-0">
                <p class="mb-0 text-muted">Secure access to PART analytics and planning tools</p>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>