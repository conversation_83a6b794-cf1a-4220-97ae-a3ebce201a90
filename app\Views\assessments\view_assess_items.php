<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>



<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">

            <div class="col-sm-6">
                <h1> <i class="fas fa-clipboard-check"></i> Assessment Items</h1>
                <h5 class="m-0"></h5>

            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item">Dashboard</li>
                    <li class="breadcrumb-item ">Assessments Manager</li>
                    <li class="breadcrumb-item active">Items Groups</li>
                    <li class="breadcrumb-item active">Items</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">
    <div class="row">
        <div class="col-md-12">
            <a href="<?= base_url() ?>view_assess_items_groups/<?= $assex['ucode'] ?>" class="btn btn-outline-dark "> <i class="fa fa-reply" aria-hidden="true"></i> Back</a>

            <div class=" float-right">
                <a href="<?= base_url() ?>public/uploads/template_import_items_data.csv" class="btn btn-outline-dark "> <i class="fa fa-download" aria-hidden="true"></i> Data Template File </a>

                <!-- Button trigger modal -->
                <button type="button" class="btn btn-outline-primary " data-toggle="modal" data-target="#import_modal">
                    <i class="fas fa-upload"></i> Import Data
                </button>

                <!-- Button trigger modal -->
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#create_new">
                    <i class="fa fa-plus-circle" aria-hidden="true"></i> New Item
                </button>
            </div>


            <!-- Modal -->
            <div class="modal fade" id="import_modal" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title"> <i class="fa fa-file-import" aria-hidden="true"></i> Import Data</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <?= form_open_multipart('import_assess_items') ?>
                        <div class="modal-body">

                            <div class="form-group col-md-12">
                                <div class="input-group">
                                    <div class="custom-file">
                                        <input type="file" name="csv_data" class="custom-file-input" id="exampleInputFile" accept=".csv" required>
                                        <label class="custom-file-label" for="exampleInputFile">Choose (.csv) file</label>
                                    </div>
                                </div>
                                <small class="text-muted"></small>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <input type="hidden" name="assex_id" value="<?= $assex['id'] ?>">
                            <input type="hidden" name="group_id" value="<?= $group['id'] ?>">
                            <input type="hidden" name="plan_id" value="<?= $plan['id'] ?>">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-outline-primary"> <i class="fa fa-upload" aria-hidden="true"></i> Import Data</button>
                        </div>
                        <?= form_close() ?>
                    </div>
                </div>
            </div>



            <!-- Modal -->
            <div class="modal fade" id="create_new" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-primary">
                            <h5 class="modal-title"><i class="fa fa-plus-circle" aria-hidden="true"></i> Create Items </h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <?= form_open_multipart('create_assess_items') ?>
                        <div class="modal-body">
                            <div class="row">
                                <div class="form-group col-md-12">
                                    <input type="text" name="code" id="" class="form-control" placeholder=" Code" aria-describedby="helpId" required>
                                    <small id="helpId" class="text-muted">Code</small>
                                </div>
                                <div class="form-group col-md-12">
                                    <input type="text" name="title" id="" class="form-control" placeholder=" Title" aria-describedby="helpId" required>
                                    <small id="helpId" class="text-muted">Title</small>
                                </div>

                            </div>

                        </div>
                        <div class="modal-footer">
                            <input type="hidden" name="assex_id" value="<?= $assex['id'] ?>">
                            <input type="hidden" name="group_id" value="<?= $group['id'] ?>">
                            <input type="hidden" name="plan_id" value="<?= $plan['id'] ?>">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary"> <i class="fa fa-paper-plane" aria-hidden="true"></i> Create</button>
                        </div>
                        <?= form_close() ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- ./col -->

    <div class="row pt-2">
        <div class="col-md-12 mb-2">
            <ul class="list-group">
                <!--tips: add .list-group-flush to the .list-group to remove some borders and rounded corners-->
                <li class="list-group-item "><b class=" float-left">Plan: </b> <span class=" float-right"> <?= $plan['code'] ?>. <?= $plan['title'] ?> </span> </li>
                <li class="list-group-item "><b class=" ">Exercise: </b> <span class=" float-right"><?= $assex['code'] ?>. <?= $assex['title'] ?> </span> </li>
                <li class="list-group-item "><b class=" ">Group: </b> <span class=" float-right"><?= $group['code'] ?>. <?= $group['title'] ?> </span> </li>

            </ul>

        </div>

        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary">
                    <i class="fa fa-info-circle" aria-hidden="true"></i> Items List
                </div>
                <div class="card-body p-0 table-responsive">
                    <table class="table text-nowrap">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Title</th>
                                <th width="10%">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data as $row) : ?>
                                <tr>
                                    <td scope="row">

                                        <?= $row['code'] ?>
                                    </td>
                                    <td scope="row">

                                        <?= $row['title'] ?>
                                    </td>


                                    <td class=" d-flex justify-content-between">

                                        <!-- Button trigger modal -->
                                        <button type="button" class="btn btn-warning btn-sm " data-toggle="modal" data-target="#edit_plan<?= $row['id'] ?>">
                                            <i class="fas fa-edit    "></i>
                                        </button>

                                        <!-- Button trigger modal -->
                                        <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#delete<?= $row['id'] ?>">
                                            <i class="fas fa-trash-alt    "></i>
                                        </button>

                                        <!-- Modal -->
                                        <div class="modal fade" id="delete<?= $row['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                            <div class="modal-dialog modal-lg" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-danger">
                                                        <h5 class="modal-title"> <i class="fa fa-exclamation-triangle" aria-hidden="true"></i> Your about to Delete!</h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <?= form_open('delete_assess_items') ?>
                                                    <div class="modal-body">
                                                        <div class="row">
                                                            <div class="col-md-12 text-wrap">
                                                                <p><b><?= $row['code'] ?></b>: <?= $row['title'] ?> </p>
                                                            </div>
                                                        </div>

                                                    </div>
                                                    <div class="modal-footer">
                                                        <input type="hidden" name="id" value="<?= $row['id'] ?>">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                        <button type="submit" class="btn btn-danger"> <i class="fa fa-times-circle" aria-hidden="true"></i> Confirm Delete</button>
                                                    </div>
                                                    <?= form_close() ?>
                                                </div>
                                            </div>
                                        </div>


                                        <!-- Modal -->
                                        <div class="modal fade" id="edit_plan<?= $row['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                            <div class="modal-dialog modal-lg" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-warning">
                                                        <h5 class="modal-title"> <i class="fas fa-edit    "></i> Edit</h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <?= form_open_multipart('update_assess_items') ?>
                                                    <div class="modal-body">
                                                        <div class="row">
                                                            <div class="form-group col-md-12">
                                                                <input type="text" name="code" id="" class="form-control" placeholder="Code" value="<?= $row['code'] ?>" required>
                                                                <small id="helpId" class="text-muted">Code</small>
                                                            </div>
                                                            <div class="form-group col-md-12">
                                                                <input type="text" name="title" id="" class="form-control" placeholder="Title" value="<?= $row['title'] ?>" required>
                                                                <small id="helpId" class="text-muted">Title</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <input type="hidden" name="id" value="<?= $row['id'] ?>">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                        <button type="submit" class="btn btn-warning"> <i class="fa fa-upload" aria-hidden="true"></i> Save Changes</button>
                                                    </div>
                                                    <?= form_close() ?>
                                                </div>
                                            </div>
                                        </div>

                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                </div>

            </div>
        </div>
        <!-- ./col -->
    </div>
    <!-- ./row -->

</section>

</body>


<?= $this->endSection() ?>