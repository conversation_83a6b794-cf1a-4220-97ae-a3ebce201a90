<?php

namespace App\Models;

use CodeIgniter\Model;

class assess_PlansModel extends Model
{
    protected $table      = 'assess_plans';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'ucode', 'orgcode', 'code', 'title', 'date_from','date_to','bg_color','status','status_by','status_at',
        'create_by', 'update_by'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;
}
