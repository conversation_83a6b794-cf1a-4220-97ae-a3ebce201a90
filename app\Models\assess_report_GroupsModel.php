<?php

namespace App\Models;

use CodeIgniter\Model;

class assess_report_GroupsModel extends Model
{
    protected $table      = 'assess_report_groups';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'ucode', 'orgcode','plan_id','assess_exercise_id', 'code', 'title', 'bg_color','chart_type','chart_operation','status','status_by','status_at',
        'create_by', 'update_by'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;
}
