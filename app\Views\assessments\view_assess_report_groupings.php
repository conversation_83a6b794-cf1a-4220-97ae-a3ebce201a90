<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>



<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">

            <div class="col-sm-6">
                <h1> <i class="fas fa-line-chart"></i> Assessment Report Groups</h1>
                <h5 class="m-0"></h5>

            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item">Dashboard</li>
                    <li class="breadcrumb-item ">Assess Manager</li>
                    <li class="breadcrumb-item active">Report Groups</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">
    <div class="row">
        <div class="col-md-12">
            <a href="<?= base_url() ?>open_assess_exercise/<?= $assex['ucode'] ?>" class="btn btn-outline-dark "> <i class="fa fa-reply" aria-hidden="true"></i> Back</a>

        </div>
    </div>
    <!-- ./col -->

    <div class="row pt-2">
        <div class="col-md-12 mb-2">

            <ul class="list-group">
                <!--tips: add .list-group-flush to the .list-group to remove some borders and rounded corners-->
                <li class="list-group-item "><b class=" float-left">Plan: </b> <span class=" float-right"> <?= $plan['code'] ?>. <?= $plan['title'] ?> </span> </li>
                <li class="list-group-item "><b class=" float-left">Exercise: </b> <span class=" float-right"><?= $assex['code'] ?>. <?= $assex['title'] ?> </span> </li>
            </ul>

        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-info">
                    Max & Min Rating Targets
                </div>
                <?= form_open('update_target_rates') ?>
                <div class="card-body">
                    <div class="form-group mb-3">
                        <input type="number" class="form-control" name="max_rate" placeholder="Max Target" value="<?= $assex['max_rate'] ?>">
                        <small>Maximum Target</small>
                    </div>
                    <div class="form-group mb-3">
                        <input type="number" class="form-control" name="min_rate" placeholder="Min Target" value="<?= $assex['min_rate'] ?>">
                        <small>Minimum Target</small>
                    </div>
                </div>
                <div class="card-footer">
                    <input type="hidden" name="id" value="<?= $assex['id'] ?>">
                    <button type="submit" class="btn btn-info float-right"> <i class="fas fa-save    "></i> Save Changes</button>
                </div>
                <?= form_close() ?>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-info">

                    <span class=" float-left"><i class="fa fa-info-circle" aria-hidden="true"></i> Report Groupings</span>

                    <!-- Button trigger modal -->
                    <button type="button" class="btn btn-default float-right" data-toggle="modal" data-target="#create_new">
                        <i class="fa fa-plus-circle" aria-hidden="true"></i> New Report Groupings
                    </button>

                    <!-- Modal -->
                    <div class="modal fade" id="create_new" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                        <div class="modal-dialog modal-lg" role="document">
                            <div class="modal-content">
                                <div class="modal-header bg-info">
                                    <h5 class="modal-title"><i class="fa fa-plus-circle" aria-hidden="true"></i> Create Report Group </h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <?= form_open_multipart('create_assess_report_group') ?>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="form-group col-md-4">
                                            <input type="text" name="code" id="" class="form-control" placeholder=" Code" aria-describedby="helpId" required>
                                            <small id="helpId" class="text-muted">Code</small>
                                        </div>
                                        <div class="form-group col-md-8">
                                            <input type="text" name="title" id="" class="form-control" placeholder=" Title" aria-describedby="helpId" required>
                                            <small id="helpId" class="text-muted">Title</small>
                                        </div>

                                        <div class="form-group col-md-2">
                                            <input type="color" name="bg_color" id="" class="form-control" placeholder=" Title" aria-describedby="helpId">
                                            <small id="helpId" class="text-muted">Select Color</small>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <select name="chart_types" id="" class=" form-control">
                                                <option value="">-- Select Chart Types --</option>
                                                <option value="radarChart">Radar Chart</option>
                                                <option value="pieChart">Pie Chart</option>
                                                <option value="barChart">Bar Chart</option>
                                            </select>
                                            <small id="helpId" class="text-muted">Chart Type</small>
                                        </div>

                                        <div class="form-group col-md-4">
                                            <select name="chart_operation" id="data_operations" class=" form-control">
                                                <option value="">-- Yet to Achieve Data --</option>
                                                <option value="positive">Positive</option>
                                                <option value="negative">Negative</option>
                                            </select>
                                            <small id="helpId" class="text-muted">Set Data to Positive/Negative</small>
                                        </div>

                                        <script>
                                            document.addEventListener('DOMContentLoaded', function() {
                                                var chartTypeSelect = document.querySelector('select[name="chart_types"]');
                                                var dataOperationsSelect = document.querySelector('select[name="chart_operation"]').parentNode; // Assuming you want to hide the entire div

                                                // Function to toggle visibility
                                                function toggleDataOperationsDisplay() {
                                                    if (chartTypeSelect.value === 'barChart') {
                                                        dataOperationsSelect.style.display = 'block'; // Show
                                                    } else {
                                                        dataOperationsSelect.style.display = 'none'; // Hide
                                                    }
                                                }

                                                // Initial check in case the page is reloaded with a selection
                                                toggleDataOperationsDisplay();

                                                // Event listener for change on chart type select
                                                chartTypeSelect.addEventListener('change', toggleDataOperationsDisplay);
                                            });
                                        </script>

                                    </div>

                                </div>
                                <div class="modal-footer">
                                    <input type="hidden" name="assex_id" value="<?= $assex['id'] ?>">
                                    <input type="hidden" name="plan_id" value="<?= $plan['id'] ?>">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                    <button type="submit" class="btn btn-info"> <i class="fa fa-paper-plane" aria-hidden="true"></i> Create</button>
                                </div>
                                <?= form_close() ?>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="card-body p-0 table-responsive">
                    <table class="table text-nowrap">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Title</th>
                                <th>Chart</th>
                                <th width="20%">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data as $row) : ?>
                                <tr style="background-color: <?= $row['bg_color'] ?>;">
                                    <td scope="row">

                                        <?= $row['code'] ?>
                                    </td>
                                    <td scope="row">

                                        <?= $row['title'] ?>
                                    </td>
                                    <td>
                                        <?= $row['chart_type'] ?>
                                        (<?= $row['chart_operation'] ?>)

                                    </td>

                                    <td class=" d-flex justify-content-between">

                                        <a class="btn btn-primary btn-sm btn-block" href="<?= base_url() ?>view_assess_report_items/<?= $row['ucode'] ?>">
                                            <i class="fa fa-arrow-right" aria-hidden="true"></i>
                                        </a>
                                        <!-- Button trigger modal -->
                                        <button type="button" class="btn btn-warning btn-sm " data-toggle="modal" data-target="#edit_plan<?= $row['id'] ?>">
                                            <i class="fas fa-edit    "></i>
                                        </button>

                                        <!-- Button trigger modal -->
                                        <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#delete<?= $row['id'] ?>">
                                            <i class="fas fa-trash-alt    "></i>
                                        </button>

                                        <!-- Modal -->
                                        <div class="modal fade" id="delete<?= $row['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                            <div class="modal-dialog modal-lg" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-danger">
                                                        <h5 class="modal-title"> <i class="fa fa-exclamation-triangle" aria-hidden="true"></i> Your about to Delete!</h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <?= form_open('delete_assess_report_group') ?>
                                                    <div class="modal-body text-center">
                                                        <p><b><?= $row['code'] ?></b>: <?= $row['title'] ?> </p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <input type="hidden" name="id" value="<?= $row['id'] ?>">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                        <button type="submit" class="btn btn-danger"> <i class="fa fa-times-circle" aria-hidden="true"></i> Confirm Delete</button>
                                                    </div>
                                                    <?= form_close() ?>
                                                </div>
                                            </div>
                                        </div>


                                        <!-- Modal -->
                                        <div class="modal fade" id="edit_plan<?= $row['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                            <div class="modal-dialog modal-lg" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-warning">
                                                        <h5 class="modal-title"> <i class="fas fa-edit    "></i> Edit</h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <?= form_open_multipart('update_assess_report_group') ?>
                                                    <div class="modal-body">
                                                        <div class="row">
                                                            <div class="form-group col-md-4">
                                                                <input type="text" name="code" id="" class="form-control" placeholder="Code" value="<?= $row['code'] ?>" required>
                                                                <small id="helpId" class="text-muted">Code</small>
                                                            </div>

                                                            <div class="form-group col-md-8">
                                                                <input type="text" name="title" id="" class="form-control" placeholder="Title" value="<?= $row['title'] ?>" required>
                                                                <small id="helpId" class="text-muted">Title</small>
                                                            </div>

                                                            <div class="form-group col-md-2">
                                                                <input type="color" name="bg_color" id="" class="form-control" value="<?= $row['bg_color'] ?>">
                                                                <small id="helpId" class="text-muted">Select Color</small>
                                                            </div>

                                                            <div class="form-group col-md-4">
                                                                <select name="chart_types" id="chart_types<?= $row['id'] ?>" class=" form-control">
                                                                    <option value="<?= $row['chart_type'] ?>"><?= ucfirst($row['chart_type']) ?></option>
                                                                    <option value="radarChart">Radar Chart</option>
                                                                    <option value="pieChart">Pie Chart</option>
                                                                    <option value="barChart">Bar Chart</option>
                                                                </select>
                                                                <small id="helpId" class="text-muted">Chart Type</small>
                                                            </div>

                                                            <div class="form-group col-md-4">
                                                                <select name="chart_operation" id="chart_operation<?= $row['id'] ?>" class=" form-control">
                                                                    <option value="<?= $row['chart_operation'] ?>"><?= ucfirst($row['chart_operation']) ?></option>
                                                                    <option value="positive">Positive</option>
                                                                    <option value="negative">Negative</option>
                                                                </select>
                                                                <small id="helpId" class="text-muted">Set Data to Positive/Negative</small>
                                                            </div>

                                                            <script>
                                                                document.addEventListener('DOMContentLoaded', function() {
                                                                    var chartTypeSelect = document.querySelector('select[id="chart_types<?= $row['id'] ?>"]');
                                                                    var dataOperationsSelect = document.querySelector('select[id="chart_operation<?= $row['id'] ?>"]').parentNode; // Assuming you want to hide the entire div

                                                                    // Function to toggle visibility
                                                                    function toggleDataOperationsDisplay() {
                                                                        if (chartTypeSelect.value === 'barChart') {
                                                                            dataOperationsSelect.style.display = 'block'; // Show
                                                                        } else {
                                                                            dataOperationsSelect.value = 'positive';
                                                                            dataOperationsSelect.style.display = 'none'; // Hide
                                                                        }
                                                                    }

                                                                    // Initial check in case the page is reloaded with a selection
                                                                    toggleDataOperationsDisplay();

                                                                    // Event listener for change on chart type select
                                                                    chartTypeSelect.addEventListener('change', toggleDataOperationsDisplay);
                                                                });
                                                            </script>


                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <input type="hidden" name="id" value="<?= $row['id'] ?>">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                        <button type="submit" class="btn btn-warning"> <i class="fa fa-upload" aria-hidden="true"></i> Save Changes</button>
                                                    </div>
                                                    <?= form_close() ?>
                                                </div>
                                            </div>
                                        </div>

                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                </div>

            </div>
        </div>
        <!-- ./col -->
    </div>
    <!-- ./row -->


</section>

</body>


<?= $this->endSection() ?>