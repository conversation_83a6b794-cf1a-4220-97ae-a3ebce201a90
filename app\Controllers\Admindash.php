<?php

namespace App\Controllers;

use App\Models\activitiesModel;
use App\Models\assess_PlansModel;
use App\Models\assess_ExercisesModel;
use App\Models\assess_itemsModel;
use App\Models\assess_items_GroupsModel;
use App\Models\assess_report_GroupsModel;
use App\Models\assess_report_itemsModel;
use App\Models\assessareaModel;
use App\Models\emp_feedbacksModel;
use App\Models\employeesModel;
use App\Models\eventsModel;
use App\Models\plansModel;
use App\Models\questionsModel;
use App\Models\usersModel;

class Admindash extends BaseController
{
    public $session;
    public $usersModel;
    public $assess_plansModel;
    public $exercisesModel;
    public $itemsModel;
    public $itemsGroupsModel;
    public $reportGroupsModel;
    public $reportItemsModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        // Load all required models
        $this->usersModel = new usersModel();
        $this->assess_plansModel = new assess_PlansModel();
        $this->exercisesModel = new assess_ExercisesModel();
        $this->itemsModel = new assess_itemsModel();
        $this->itemsGroupsModel = new assess_items_GroupsModel();
        $this->reportGroupsModel = new assess_report_GroupsModel();
        $this->reportItemsModel = new assess_report_itemsModel();
    }

    public function index()
    {
        $data['title'] = "Dashboard";
        $data['menu'] = "dashboard";
        
        // Get organization code from session
        $orgcode = $this->session->get('orgcode');

        // Get counts and statistics
        $data['activePlans'] = $this->assess_plansModel->where('orgcode', $orgcode)
            ->where('status', 'active')
            ->countAllResults();
            
        $data['completedAssessments'] = $this->exercisesModel->where('orgcode', $orgcode)
            ->where('status', 'completed')
            ->countAllResults();
            
        $data['pendingReviews'] = $this->exercisesModel->where('orgcode', $orgcode)
            ->where('status', 'pending')
            ->countAllResults();
            
        $data['totalItems'] = $this->itemsModel->where('orgcode', $orgcode)
            ->countAllResults();

        // Get assessment items with critical scores
        $data['criticalItems'] = $this->itemsModel->where('orgcode', $orgcode)
            ->where('score <', 3) // Assuming scores below 3 are critical
            ->countAllResults();

        // Get active exercises for the chart data
        $data['activeExercises'] = $this->exercisesModel->where('orgcode', $orgcode)
            ->where('status', 'active')
            ->findAll();

        // Get assessment distribution by group type
        $assessmentGroups = $this->itemsGroupsModel->select('title, COUNT(*) as count')
            ->where('orgcode', $orgcode)
            ->groupBy('title')
            ->findAll();

        // Prepare chart data
        $data['groupLabels'] = [];
        $data['groupCounts'] = [];
        $data['groupColors'] = [
            'rgba(60, 141, 188, 0.8)',
            'rgba(210, 214, 222, 0.8)',
            'rgba(0, 192, 239, 0.8)',
            'rgba(76, 175, 80, 0.8)',
            'rgba(255, 152, 0, 0.8)',
            'rgba(233, 30, 99, 0.8)'
        ];

        // Process assessment groups for chart
        $data['totalGroups'] = 0;
        foreach ($assessmentGroups as $index => $group) {
            $data['groupLabels'][] = $group['title'];
            $data['groupCounts'][] = $group['count'];
            $data['totalGroups'] += $group['count'];
        }
        
        $data['assessmentGroups'] = $assessmentGroups;

        // Get recent activity
        $recentPlans = $this->assess_plansModel->where('orgcode', $orgcode)
            ->orderBy('created_at', 'DESC')
            ->limit(5)
            ->findAll();

        $recentExercises = $this->exercisesModel->where('orgcode', $orgcode)
            ->orderBy('created_at', 'DESC')
            ->limit(5)
            ->findAll();

        // Merge and sort recent activity
        $recentActivity = array_merge($recentPlans, $recentExercises);
        usort($recentActivity, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        $data['recentActivity'] = array_slice($recentActivity, 0, 5);

        // Calculate completion rate over time (last 6 months)
        $data['months'] = [];
        $data['completionRates'] = [];
        $data['targetRates'] = [];

        for ($i = 5; $i >= 0; $i--) {
            $month = date('M', strtotime("-$i months"));
            $data['months'][] = $month;
            
            // Calculate completion rates
            $monthStart = date('Y-m-01 00:00:00', strtotime("-$i months"));
            $monthEnd = date('Y-m-t 23:59:59', strtotime("-$i months"));
            
            $totalMonthExercises = $this->exercisesModel->where('orgcode', $orgcode)
                ->where('created_at >=', $monthStart)
                ->where('created_at <=', $monthEnd)
                ->countAllResults();
            
            $completedMonthExercises = $this->exercisesModel->where('orgcode', $orgcode)
                ->where('status', 'completed')
                ->where('created_at >=', $monthStart)
                ->where('created_at <=', $monthEnd)
                ->countAllResults();
            
            $completionRate = $totalMonthExercises > 0 ? round(($completedMonthExercises / $totalMonthExercises) * 100) : 0;
            $data['completionRates'][] = $completionRate;
            
            // Target rates (increasing by 5% each month, starting at 65%)
            $data['targetRates'][] = min(65 + ($i * 5), 100);
        }

        // Calculate average scores
        $avgScoreResult = $this->itemsModel->selectAvg('score')->where('orgcode', $orgcode)->first();
        $data['averageScore'] = round($avgScoreResult['score'] ?? 0);

        // Get upcoming exercises with deadlines
        $data['upcomingExercises'] = $this->exercisesModel->where('orgcode', $orgcode)
            ->where('date_to >=', date('Y-m-d'))
            ->where('status !=', 'completed')
            ->orderBy('date_to', 'ASC')
            ->limit(3)
            ->findAll();

        echo view('admindash/dashboard', $data);
    }
}
