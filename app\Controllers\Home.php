<?php

namespace App\Controllers;

use App\Models\employeesModel;
use App\Models\orgModel;
use App\Models\projectsModel;
use App\Models\usersModel;

class Home extends BaseController
{
    public $session;
    public $usersModel;
    public $orgModel;



    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        $this->usersModel = new usersModel();
        $this->orgModel = new orgModel();
    }

    public function index()
    {
        $data['title'] = "Home";
        $data['menu'] = "home";



        echo view('home/home', $data);
    }

    public function about()
    {
        $data['title'] = "About Org.Calendar";
        $data['menu'] = "about";
        echo view('home/about', $data);
    }



    public function login()
    {


        // Display login form
        //  return view('home/login');

        $data['title'] = "Login";
        $data['menu'] = "login";

        echo view('home/login', $data);
    }

    public function dologin()
    {
        // Check if form has been submitted
        // if ($this->request->getMethod() == 'post') {


        // Validate form data
        $rules = [
            'username' => 'required',
            'password' => 'required'
        ];
        if (!$this->validate($rules)) {
            // Display login form with validation errors
            // return view('home/login', ['validation' => $this->validator]);

            return redirect()->back()->with('error', 'Enter Correct Username and Password!');
        }

        // Retrieve form data
        $username = $this->request->getVar('username');
        $password = $this->request->getVar('password');

        // Check if user exists

        $user = $this->usersModel->where('username', $username)->first();
        if (!$user) {
            // Display login form with error message
            return redirect()->back()->with('error', 'Incorrect Username!');

            //return view('home/login', ['error' => 'Invalid username or password']);
        }

        if (empty($user['orgcode'])) {
            // Display login form with error message
            /* $this->session->setTempdata('error', 'Username is not associated with any organization', 2);
                return  redirect()->to(current_url()); */
            return redirect()->back()->with('error', 'Username is not associated with any organization!');
        }

        // Check if password is correct
        if (!password_verify($password, $user['password'])) {
            // Display login form with error message

            return redirect()->back()->with('error', 'Incorrect Password!');
        }

        $org = $this->orgModel->where('orgcode', $user['orgcode'])->first();

        if ($org['is_active'] != 1) {
            // Display login form with error message

            return redirect()->back()->with('error', 'Your Organization has been Deactivated!');
        }

        // Store user data in session
        $this->session->set('username', $user['username']);
        $this->session->set('name', $user['name']);
        $this->session->set('role', $user['role']);
        $this->session->set('status', $user['is_active']);
        $this->session->set('orgname', $org['name']);
        $this->session->set('orglogo', $org['orglogo']);
        $this->session->set('orgcode', $org['orgcode']);
        $this->session->set('orgcountry', $org['addlockcountry']);
        $this->session->set('orgprov', $org['addlockprov']);

        if ($user['is_active'] != 1) {
            //return redirect()->back()->with($this->session->setTempdata('error', 'Your account is not active', 2));
            return redirect()->back()->with('error', 'Your account is not active!');
        }

        // Redirect to dashboard
        return redirect()->to('dashboard');
    }


    public function logout()
    {
        // Destroy the user's session
        $session = session();
        $session->destroy();

        // Redirect to the login page
        return redirect()->to(base_url());
    }
}
