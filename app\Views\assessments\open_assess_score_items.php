<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>



<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">

            <div class="col-sm-6">
                <h1> <i class="fas fa-clipboard-check"></i> Score Items</h1>
                <h5 class="m-0"></h5>

            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item ">...</li>
                    <li class="breadcrumb-item ">Score Group</li>
                    <li class="breadcrumb-item active">Score Item</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">
    <div class="row">
        <div class="col-md-12">
            <a href="<?= base_url() ?>open_assess_score_group/<?= $assex['ucode'] ?>" class="btn btn-outline-dark "> <i class="fa fa-reply" aria-hidden="true"></i> Back</a>

        </div>
    </div>
    <!-- ./col -->

    <div class="row pt-2">

        <div class="col-md-12 mb-2">
            <ul class="list-group">
                <!--tips: add .list-group-flush to the .list-group to remove some borders and rounded corners-->
                <li class="list-group-item "><b class=" float-left">Plan: </b> <span class=" float-right"> <?= $plan['code'] ?>. <?= $plan['title'] ?> </span> </li>
                <li class="list-group-item "><b>Exercise:</b> <span class=" float-right" ><?= $assex['code'] ?>. <?= $assex['title'] ?></span> </li>
                <li class="list-group-item "><b>Group:</b> <span class=" float-right" ><?= $item_groups['code'] ?>. <?= $item_groups['title'] ?></span> </li>
            </ul>
        </div>

        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-warning">
                    <i class="fa fa-info-circle" aria-hidden="true"></i> Item Groups
                </div>
                <div class="card-body p-0 table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Title</th>
                                <th width="20%" >Achieved</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($assess_items as $row) : ?>
                                <tr>
                                    <td scope="row"><?= $row['code'] ?></td>
                                    <td><?= $row['title'] ?></td>
                                    <td>
                                        <input type="hidden" name="id" id="id" value="<?= $row['id'] ?>">
                                        <input type="number" min="0" max="10" name="score[]" class=" form-control score-input<?= $row['id'] ?>" placeholder="Enter score" value="<?= $row['score'] ?>">
                                        <small id="error<?= $row['id'] ?>" class=" text-danger"></small>
                                        <small id="success<?= $row['id'] ?>" class=" text-success"></small>
                                    </td>
                                </tr>

                                <script>
                                    $(document).ready(function() {
                                        $('.score-input<?= $row['id'] ?>').on('input', function() {
                                            var score = $(this).val();
                                            var id = <?= $row['id'] ?>; // Ensure PHP variables are correctly embedded

                                            // Ensure that score is not empty, is a number, and is between the min_rate and max_rate
                                            if (score !== '' && !isNaN(score) && score >= <?= $assex['min_rate'] ?> && score <= <?= $assex['max_rate'] ?>) {
                                                $.ajax({
                                                    url: "<?= base_url() ?>enter_score_items", // Ensure the PHP variable is correctly echoed
                                                    type: 'POST',
                                                    data: {
                                                        id: id,
                                                        score: score
                                                    },
                                                    success: function(response) {
                                                        console.log(response);
                                                        // Optionally, alert the user to success
                                                        var successMessage = "<i class='fas fa-check'></i>"; // Corrected to a success message
                                                        console.error(successMessage); // It might be more appropriate to use console.log here
                                                        $('#success<?= $row['id'] ?>').html(successMessage); // Display the success message

                                                        // Clear the success message after 3 seconds
                                                        setTimeout(function() {
                                                            $('#success<?= $row['id'] ?>').html(''); // This will clear the message
                                                        }, 3000); // 3000 milliseconds = 3 seconds

                                                        $('#error<?= $row['id'] ?>').html(''); // Clear any previous error messages
                                                    },
                                                    error: function(xhr, status, error) {
                                                        console.error("An error occurred: " + error);
                                                        // Optionally, alert the user to the error
                                                        $('#error<?= $row['id'] ?>').html("Error: " + error); // Display the error message
                                                    }
                                                });
                                            } else {
                                                // Generate an error and ask the user to input values within the specified range
                                                var errorMessage = "Please input values between <?= $assex['min_rate'] ?> and <?= $assex['max_rate'] ?>";
                                                console.error(errorMessage);
                                                $('#error<?= $row['id'] ?>').html(errorMessage); // Display the error message
                                                $('#success<?= $row['id'] ?>').html(""); // Display the success message
                                            }
                                        });
                                    });
                                </script>

                            <?php endforeach; ?>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>
    <!-- ./row -->



</section>

</body>


<?= $this->endSection() ?>