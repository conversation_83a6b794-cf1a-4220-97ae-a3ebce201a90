{"url": "http://localhost/part/index.php/dashboard", "method": "GET", "isAJAX": false, "startTime": **********.576634, "totalTime": 306.**************, "totalMemory": "7.672", "segmentDuration": 45, "segmentCount": 7, "CI_VERSION": "4.3.2", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.618712, "duration": 0.*****************}, {"name": "Routing", "component": "Timer", "start": **********.672082, "duration": 7.510185241699219e-05}, {"name": "Before Filters", "component": "Timer", "start": **********.676882, "duration": 0.029957056045532227}, {"name": "Controller", "component": "Timer", "start": **********.706849, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.706853, "duration": 0.****************}, {"name": "After Filters", "component": "Timer", "start": **********.88272, "duration": 0.0008409023284912109}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(23 total Queries, 21 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "14.04 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_plans`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:57", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:57", "qid": "11b15538afd5cc6265c94ecfe0dbc58b"}, {"hover": "", "class": "", "duration": "6.44 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:61", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:61", "qid": "a7f90762687c228c07b662bae903f207"}, {"hover": "", "class": "", "duration": "0.71 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;pending&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:65", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:65", "qid": "5915581df8874b87acfffeb49cf7fcc0"}, {"hover": "", "class": "", "duration": "9.03 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_items`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:68", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:68", "qid": "bfb5e9a1d8449ca2078a003b3f82e677"}, {"hover": "", "class": "", "duration": "2.07 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_items`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `score` &lt; 3", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:73", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:73", "qid": "e5c8185360b395998e422636150529be"}, {"hover": "", "class": "", "duration": "0.83 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:241", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:618", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Admindash.php:78", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:78", "qid": "095dd90cee8669a8a4c901199a684649"}, {"hover": "", "class": "", "duration": "23.74 ms", "sql": "<strong>SELECT</strong> `title`, <strong>COUNT</strong>(*) as count\n<strong>FROM</strong> `assess_items_groups`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>GROUP</strong> <strong>BY</strong> `title`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:241", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:618", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Admindash.php:84", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:84", "qid": "715dadb856ace670e3ef5d9d2ce5b1c9"}, {"hover": "", "class": "", "duration": "0.8 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_plans`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>ORDER</strong> <strong>BY</strong> `created_at` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:241", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:618", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Admindash.php:112", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:112", "qid": "7fa700f065bf13d19980f77b55216caf"}, {"hover": "", "class": "", "duration": "0.75 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>ORDER</strong> <strong>BY</strong> `created_at` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:241", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:618", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Admindash.php:117", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:117", "qid": "63a4e13799b454062898060fc4cd4d8a"}, {"hover": "", "class": "", "duration": "1.63 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2024-12-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2024-12-31 23:59:59&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:142", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:142", "qid": "dfaa2c53e40e9428c6b968bd70c105a7"}, {"hover": "", "class": "", "duration": "0.57 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2024-12-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2024-12-31 23:59:59&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:148", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:148", "qid": "c7b819e09641e9def0e8e00989f180c5"}, {"hover": "", "class": "", "duration": "0.64 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-01-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-01-31 23:59:59&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:142", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:142", "qid": "45f52a3acb074e7cfc6de7369d606556"}, {"hover": "", "class": "", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-01-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-01-31 23:59:59&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:148", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:148", "qid": "f4c669c4e37c5f7da598de57407debc8"}, {"hover": "", "class": "", "duration": "0.54 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-03-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-03-31 23:59:59&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:142", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:142", "qid": "8b4431b0dd053a5cf8e0497ef57fb7cc"}, {"hover": "", "class": "", "duration": "0.72 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-03-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-03-31 23:59:59&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:148", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:148", "qid": "855f73a6f42415615accd57e9ba2dbe4"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.71 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-03-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-03-31 23:59:59&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:142", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:142", "qid": "a903b5445af72263a78cd4c0dc9897f6"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.91 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-03-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-03-31 23:59:59&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:148", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:148", "qid": "e760ef9763d84c9a28a4282fbbf03f13"}, {"hover": "", "class": "", "duration": "0.81 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-04-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-04-30 23:59:59&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:142", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:142", "qid": "8fb9eca6814d23df5558924bcb8bae5d"}, {"hover": "", "class": "", "duration": "0.61 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-04-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-04-30 23:59:59&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:148", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:148", "qid": "47933d766ea9650da46bdfc5e3ab389c"}, {"hover": "", "class": "", "duration": "0.49 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-05-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-05-31 23:59:59&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:142", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:142", "qid": "a301aa36cfabc3a57fd91085768139cc"}, {"hover": "", "class": "", "duration": "0.64 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-05-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-05-31 23:59:59&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:606", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Admindash.php:148", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:148", "qid": "685c95fd31a740f6ee88ff400aa969db"}, {"hover": "", "class": "", "duration": "1.22 ms", "sql": "<strong>SELECT</strong> <strong>AVG</strong>(`score`) <strong>AS</strong> `score`\n<strong>FROM</strong> `assess_items`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:268", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:656", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Admindash.php:158", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:158", "qid": "de6d839942e907c6146e1b7d02d7aff6"}, {"hover": "", "class": "", "duration": "0.91 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `date_to` &gt;= &#039;2025-05-30&#039;\n<strong>AND</strong> `status` != &#039;completed&#039;\n<strong>ORDER</strong> <strong>BY</strong> `date_to` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:241", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:618", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Admindash.php:167", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:934", "function": "        App\\Controllers\\Admindash->index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:499", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:368", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "FCPATH\\index.php:67", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}], "trace-file": "APPPATH\\Controllers\\Admindash.php:167", "qid": "997001ad532ff874d71e5d5185884dd3"}]}, "badgeValue": 23, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.771113, "duration": "0.014137"}, {"name": "Query", "component": "Database", "start": **********.78812, "duration": "0.014039", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_plans`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.805758, "duration": "0.006443", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;"}, {"name": "Query", "component": "Database", "start": **********.812379, "duration": "0.000711", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;pending&#039;"}, {"name": "Query", "component": "Database", "start": **********.813458, "duration": "0.009027", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_items`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;"}, {"name": "Query", "component": "Database", "start": **********.823049, "duration": "0.002066", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_items`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `score` &lt; 3"}, {"name": "Query", "component": "Database", "start": **********.825362, "duration": "0.000831", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.82729, "duration": "0.023738", "query": "<strong>SELECT</strong> `title`, <strong>COUNT</strong>(*) as count\n<strong>FROM</strong> `assess_items_groups`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>GROUP</strong> <strong>BY</strong> `title`"}, {"name": "Query", "component": "Database", "start": **********.851492, "duration": "0.000798", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_plans`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>ORDER</strong> <strong>BY</strong> `created_at` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.852533, "duration": "0.000754", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>ORDER</strong> <strong>BY</strong> `created_at` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.855339, "duration": "0.001628", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2024-12-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2024-12-31 23:59:59&#039;"}, {"name": "Query", "component": "Database", "start": **********.857127, "duration": "0.000571", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2024-12-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2024-12-31 23:59:59&#039;"}, {"name": "Query", "component": "Database", "start": **********.858044, "duration": "0.000642", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-01-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-01-31 23:59:59&#039;"}, {"name": "Query", "component": "Database", "start": **********.859024, "duration": "0.000597", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-01-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-01-31 23:59:59&#039;"}, {"name": "Query", "component": "Database", "start": **********.859881, "duration": "0.000539", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-03-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-03-31 23:59:59&#039;"}, {"name": "Query", "component": "Database", "start": **********.86079, "duration": "0.000723", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-03-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-03-31 23:59:59&#039;"}, {"name": "Query", "component": "Database", "start": **********.861805, "duration": "0.000705", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-03-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-03-31 23:59:59&#039;"}, {"name": "Query", "component": "Database", "start": **********.862738, "duration": "0.000907", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-03-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-03-31 23:59:59&#039;"}, {"name": "Query", "component": "Database", "start": **********.864266, "duration": "0.000814", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-04-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-04-30 23:59:59&#039;"}, {"name": "Query", "component": "Database", "start": **********.865412, "duration": "0.000610", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-04-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-04-30 23:59:59&#039;"}, {"name": "Query", "component": "Database", "start": **********.866491, "duration": "0.000491", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-05-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-05-31 23:59:59&#039;"}, {"name": "Query", "component": "Database", "start": **********.867495, "duration": "0.000638", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `status` = &#039;completed&#039;\n<strong>AND</strong> `created_at` &gt;= &#039;2025-05-01 00:00:00&#039;\n<strong>AND</strong> `created_at` &lt;= &#039;2025-05-31 23:59:59&#039;"}, {"name": "Query", "component": "Database", "start": **********.868373, "duration": "0.001224", "query": "<strong>SELECT</strong> <strong>AVG</strong>(`score`) <strong>AS</strong> `score`\n<strong>FROM</strong> `assess_items`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.869964, "duration": "0.000913", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `assess_exercise`\n<strong>WHERE</strong> `orgcode` = &#039;2345&#039;\n<strong>AND</strong> `date_to` &gt;= &#039;2025-05-30&#039;\n<strong>AND</strong> `status` != &#039;completed&#039;\n<strong>ORDER</strong> <strong>BY</strong> `date_to` <strong>ASC</strong>"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "info", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: templates/adminlte/admindash.php", "component": "Views", "start": **********.875961, "duration": 0.004993915557861328}, {"name": "View: admindash/dashboard.php", "component": "Views", "start": **********.871129, "duration": 0.011015892028808594}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 168 )", "display": {"coreFiles": [{"name": "AutoRouterImproved.php", "path": "SYSTEMPATH\\Router\\AutoRouterImproved.php"}, {"name": "AutoRouterInterface.php", "path": "SYSTEMPATH\\Router\\AutoRouterInterface.php"}, {"name": "AutoloadConfig.php", "path": "SYSTEMPATH\\Config\\AutoloadConfig.php"}, {"name": "Autoloader.php", "path": "SYSTEMPATH\\Autoloader\\Autoloader.php"}, {"name": "BaseBuilder.php", "path": "SYSTEMPATH\\Database\\BaseBuilder.php"}, {"name": "BaseCollector.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php"}, {"name": "BaseConfig.php", "path": "SYSTEMPATH\\Config\\BaseConfig.php"}, {"name": "BaseConnection.php", "path": "SYSTEMPATH\\Database\\BaseConnection.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php"}, {"name": "BaseHandler.php", "path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php"}, {"name": "BaseModel.php", "path": "SYSTEMPATH\\BaseModel.php"}, {"name": "BaseResult.php", "path": "SYSTEMPATH\\Database\\BaseResult.php"}, {"name": "BaseService.php", "path": "SYSTEMPATH\\Config\\BaseService.php"}, {"name": "Builder.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php"}, {"name": "CacheFactory.php", "path": "SYSTEMPATH\\Cache\\CacheFactory.php"}, {"name": "CacheInterface.php", "path": "SYSTEMPATH\\Cache\\CacheInterface.php"}, {"name": "CloneableCookieInterface.php", "path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php"}, {"name": "CodeIgniter.php", "path": "SYSTEMPATH\\CodeIgniter.php"}, {"name": "Common.php", "path": "SYSTEMPATH\\Common.php"}, {"name": "ConditionalTrait.php", "path": "SYSTEMPATH\\Traits\\ConditionalTrait.php"}, {"name": "Config.php", "path": "SYSTEMPATH\\Database\\Config.php"}, {"name": "Connection.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php"}, {"name": "ConnectionInterface.php", "path": "SYSTEMPATH\\Database\\ConnectionInterface.php"}, {"name": "ContentSecurityPolicy.php", "path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php"}, {"name": "Controller.php", "path": "SYSTEMPATH\\Controller.php"}, {"name": "Cookie.php", "path": "SYSTEMPATH\\Cookie\\Cookie.php"}, {"name": "CookieInterface.php", "path": "SYSTEMPATH\\Cookie\\CookieInterface.php"}, {"name": "CookieStore.php", "path": "SYSTEMPATH\\Cookie\\CookieStore.php"}, {"name": "Database.php", "path": "SYSTEMPATH\\Database\\Database.php"}, {"name": "Database.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php"}, {"name": "DebugToolbar.php", "path": "SYSTEMPATH\\Filters\\DebugToolbar.php"}, {"name": "DotEnv.php", "path": "SYSTEMPATH\\Config\\DotEnv.php"}, {"name": "Events.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php"}, {"name": "Events.php", "path": "SYSTEMPATH\\Events\\Events.php"}, {"name": "Exceptions.php", "path": "SYSTEMPATH\\Debug\\Exceptions.php"}, {"name": "Factories.php", "path": "SYSTEMPATH\\Config\\Factories.php"}, {"name": "Factory.php", "path": "SYSTEMPATH\\Config\\Factory.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php"}, {"name": "FileHandler.php", "path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php"}, {"name": "FileLocator.php", "path": "SYSTEMPATH\\Autoloader\\FileLocator.php"}, {"name": "Files.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php"}, {"name": "FilterInterface.php", "path": "SYSTEMPATH\\Filters\\FilterInterface.php"}, {"name": "Filters.php", "path": "SYSTEMPATH\\Filters\\Filters.php"}, {"name": "FormatRules.php", "path": "SYSTEMPATH\\Validation\\FormatRules.php"}, {"name": "HandlerInterface.php", "path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php"}, {"name": "Header.php", "path": "SYSTEMPATH\\HTTP\\Header.php"}, {"name": "IncomingRequest.php", "path": "SYSTEMPATH\\HTTP\\IncomingRequest.php"}, {"name": "Logger.php", "path": "SYSTEMPATH\\Log\\Logger.php"}, {"name": "Logs.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php"}, {"name": "Message.php", "path": "SYSTEMPATH\\HTTP\\Message.php"}, {"name": "MessageInterface.php", "path": "SYSTEMPATH\\HTTP\\MessageInterface.php"}, {"name": "MessageTrait.php", "path": "SYSTEMPATH\\HTTP\\MessageTrait.php"}, {"name": "Model.php", "path": "SYSTEMPATH\\Model.php"}, {"name": "Modules.php", "path": "SYSTEMPATH\\Modules\\Modules.php"}, {"name": "OutgoingRequest.php", "path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php"}, {"name": "OutgoingRequestInterface.php", "path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php"}, {"name": "Query.php", "path": "SYSTEMPATH\\Database\\Query.php"}, {"name": "QueryInterface.php", "path": "SYSTEMPATH\\Database\\QueryInterface.php"}, {"name": "RendererInterface.php", "path": "SYSTEMPATH\\View\\RendererInterface.php"}, {"name": "Request.php", "path": "SYSTEMPATH\\HTTP\\Request.php"}, {"name": "RequestInterface.php", "path": "SYSTEMPATH\\HTTP\\RequestInterface.php"}, {"name": "RequestTrait.php", "path": "SYSTEMPATH\\HTTP\\RequestTrait.php"}, {"name": "Response.php", "path": "SYSTEMPATH\\HTTP\\Response.php"}, {"name": "ResponseInterface.php", "path": "SYSTEMPATH\\HTTP\\ResponseInterface.php"}, {"name": "ResponseTrait.php", "path": "SYSTEMPATH\\API\\ResponseTrait.php"}, {"name": "ResponseTrait.php", "path": "SYSTEMPATH\\HTTP\\ResponseTrait.php"}, {"name": "Result.php", "path": "SYSTEMPATH\\Database\\MySQLi\\Result.php"}, {"name": "ResultInterface.php", "path": "SYSTEMPATH\\Database\\ResultInterface.php"}, {"name": "RouteCollection.php", "path": "SYSTEMPATH\\Router\\RouteCollection.php"}, {"name": "RouteCollectionInterface.php", "path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php"}, {"name": "Router.php", "path": "SYSTEMPATH\\Router\\Router.php"}, {"name": "RouterInterface.php", "path": "SYSTEMPATH\\Router\\RouterInterface.php"}, {"name": "Routes.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php"}, {"name": "Services.php", "path": "SYSTEMPATH\\Config\\Services.php"}, {"name": "Session.php", "path": "SYSTEMPATH\\Session\\Session.php"}, {"name": "SessionInterface.php", "path": "SYSTEMPATH\\Session\\SessionInterface.php"}, {"name": "Time.php", "path": "SYSTEMPATH\\I18n\\Time.php"}, {"name": "TimeTrait.php", "path": "SYSTEMPATH\\I18n\\TimeTrait.php"}, {"name": "Timer.php", "path": "SYSTEMPATH\\Debug\\Timer.php"}, {"name": "Timers.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php"}, {"name": "Toolbar.php", "path": "SYSTEMPATH\\Debug\\Toolbar.php"}, {"name": "URI.php", "path": "SYSTEMPATH\\HTTP\\URI.php"}, {"name": "UserAgent.php", "path": "SYSTEMPATH\\HTTP\\UserAgent.php"}, {"name": "Validation.php", "path": "SYSTEMPATH\\Validation\\Validation.php"}, {"name": "ValidationInterface.php", "path": "SYSTEMPATH\\Validation\\ValidationInterface.php"}, {"name": "View.php", "path": "SYSTEMPATH\\Config\\View.php"}, {"name": "View.php", "path": "SYSTEMPATH\\View\\View.php"}, {"name": "ViewDecoratorTrait.php", "path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php"}, {"name": "Views.php", "path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php"}, {"name": "array_helper.php", "path": "SYSTEMPATH\\Helpers\\array_helper.php"}, {"name": "bootstrap.php", "path": "SYSTEMPATH\\bootstrap.php"}, {"name": "form_helper.php", "path": "SYSTEMPATH\\Helpers\\form_helper.php"}, {"name": "kint_helper.php", "path": "SYSTEMPATH\\Helpers\\kint_helper.php"}, {"name": "url_helper.php", "path": "SYSTEMPATH\\Helpers\\url_helper.php"}], "userFiles": [{"name": "AbstractRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\AbstractRenderer.php"}, {"name": "Admindash.php", "path": "APPPATH\\Controllers\\Admindash.php"}, {"name": "App.php", "path": "APPPATH\\Config\\App.php"}, {"name": "Auth.php", "path": "APPPATH\\Filters\\Auth.php"}, {"name": "Autoload.php", "path": "APPPATH\\Config\\Autoload.php"}, {"name": "BaseController.php", "path": "APPPATH\\Controllers\\BaseController.php"}, {"name": "Cache.php", "path": "APPPATH\\Config\\Cache.php"}, {"name": "ClassLoader.php", "path": "FCPATH\\vendor\\composer\\ClassLoader.php"}, {"name": "CliRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\CliRenderer.php"}, {"name": "Common.php", "path": "APPPATH\\Common.php"}, {"name": "Constants.php", "path": "APPPATH\\Config\\Constants.php"}, {"name": "ContentSecurityPolicy.php", "path": "APPPATH\\Config\\ContentSecurityPolicy.php"}, {"name": "Cookie.php", "path": "APPPATH\\Config\\Cookie.php"}, {"name": "Database.php", "path": "APPPATH\\Config\\Database.php"}, {"name": "Escaper.php", "path": "FCPATH\\vendor\\laminas\\laminas-escaper\\src\\Escaper.php"}, {"name": "Events.php", "path": "APPPATH\\Config\\Events.php"}, {"name": "Exceptions.php", "path": "APPPATH\\Config\\Exceptions.php"}, {"name": "FacadeInterface.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\FacadeInterface.php"}, {"name": "Feature.php", "path": "APPPATH\\Config\\Feature.php"}, {"name": "Filters.php", "path": "APPPATH\\Config\\Filters.php"}, {"name": "Functions.php", "path": "FCPATH\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php"}, {"name": "InstalledVersions.php", "path": "FCPATH\\vendor\\composer\\InstalledVersions.php"}, {"name": "Kint.php", "path": "APPPATH\\Config\\Kint.php"}, {"name": "Kint.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Kint.php"}, {"name": "LogLevel.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LogLevel.php"}, {"name": "Logger.php", "path": "APPPATH\\Config\\Logger.php"}, {"name": "LoggerAwareTrait.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LoggerAwareTrait.php"}, {"name": "LoggerInterface.php", "path": "FCPATH\\vendor\\psr\\log\\Psr\\Log\\LoggerInterface.php"}, {"name": "Modules.php", "path": "APPPATH\\Config\\Modules.php"}, {"name": "Paths.php", "path": "APPPATH\\Config\\Paths.php"}, {"name": "RendererInterface.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\RendererInterface.php"}, {"name": "RichRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\RichRenderer.php"}, {"name": "Routes.php", "path": "APPPATH\\Config\\Routes.php"}, {"name": "Services.php", "path": "APPPATH\\Config\\Services.php"}, {"name": "Session.php", "path": "APPPATH\\Config\\Session.php"}, {"name": "TextRenderer.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Renderer\\TextRenderer.php"}, {"name": "Toolbar.php", "path": "APPPATH\\Config\\Toolbar.php"}, {"name": "UserAgents.php", "path": "APPPATH\\Config\\UserAgents.php"}, {"name": "Utils.php", "path": "FCPATH\\vendor\\kint-php\\kint\\src\\Utils.php"}, {"name": "Validation.php", "path": "APPPATH\\Config\\Validation.php"}, {"name": "View.php", "path": "APPPATH\\Config\\View.php"}, {"name": "admindash.php", "path": "APPPATH\\Views\\templates\\adminlte\\admindash.php"}, {"name": "assess_ExercisesModel.php", "path": "APPPATH\\Models\\assess_ExercisesModel.php"}, {"name": "assess_PlansModel.php", "path": "APPPATH\\Models\\assess_PlansModel.php"}, {"name": "assess_itemsModel.php", "path": "APPPATH\\Models\\assess_itemsModel.php"}, {"name": "assess_items_GroupsModel.php", "path": "APPPATH\\Models\\assess_items_GroupsModel.php"}, {"name": "assess_report_GroupsModel.php", "path": "APPPATH\\Models\\assess_report_GroupsModel.php"}, {"name": "assess_report_itemsModel.php", "path": "APPPATH\\Models\\assess_report_itemsModel.php"}, {"name": "autoload.php", "path": "FCPATH\\vendor\\autoload.php"}, {"name": "autoload_real.php", "path": "FCPATH\\vendor\\composer\\autoload_real.php"}, {"name": "autoload_static.php", "path": "FCPATH\\vendor\\composer\\autoload_static.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-ctype\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-mbstring\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-php80\\bootstrap.php"}, {"name": "bootstrap.php", "path": "FCPATH\\vendor\\symfony\\polyfill-php81\\bootstrap.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-ctype\\bootstrap80.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap80.php"}, {"name": "bootstrap80.php", "path": "FCPATH\\vendor\\symfony\\polyfill-mbstring\\bootstrap80.php"}, {"name": "dashboard.php", "path": "APPPATH\\Views\\admindash\\dashboard.php"}, {"name": "deep_copy.php", "path": "FCPATH\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php"}, {"name": "development.php", "path": "APPPATH\\Config\\Boot\\development.php"}, {"name": "function.php", "path": "FCPATH\\vendor\\symfony\\deprecation-contracts\\function.php"}, {"name": "functions.php", "path": "FCPATH\\vendor\\symfony\\string\\Resources\\functions.php"}, {"name": "index.php", "path": "FCPATH\\index.php"}, {"name": "info_helper.php", "path": "APPPATH\\Helpers\\info_helper.php"}, {"name": "init.php", "path": "FCPATH\\vendor\\kint-php\\kint\\init.php"}, {"name": "init_helpers.php", "path": "FCPATH\\vendor\\kint-php\\kint\\init_helpers.php"}, {"name": "installed.php", "path": "FCPATH\\vendor\\composer\\installed.php"}, {"name": "platform_check.php", "path": "FCPATH\\vendor\\composer\\platform_check.php"}, {"name": "usersModel.php", "path": "APPPATH\\Models\\usersModel.php"}]}, "badgeValue": 168, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Admindash", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Home::login"}, {"method": "GET", "route": "logout", "handler": "\\App\\Controllers\\Home::logout"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Home::about"}, {"method": "GET", "route": "findme/(.*)", "handler": "\\App\\Controllers\\Home::findme/$1"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\Admindash::index"}, {"method": "GET", "route": "employees", "handler": "\\App\\Controllers\\Employees::index"}, {"method": "GET", "route": "edit_employees/(.*)", "handler": "\\App\\Controllers\\Employees::edit_employees/$1"}, {"method": "GET", "route": "profile_employees/(.*)", "handler": "\\App\\Controllers\\Employees::profile_employees/$1"}, {"method": "GET", "route": "emp_dashboard", "handler": "\\App\\Controllers\\Portal::index"}, {"method": "GET", "route": "emp_open_activity/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_activity/$1"}, {"method": "GET", "route": "emp_open_skillcomp/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skillcomp/$1"}, {"method": "GET", "route": "emp_open_feedbacks/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_feedbacks/$1"}, {"method": "GET", "route": "emp_open_feedback_groups/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_feedback_groups/$1"}, {"method": "GET", "route": "emp_open_skills_comp/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skills_comp/$1"}, {"method": "GET", "route": "emp_open_skills_comp_groups/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skills_comp_groups/$1"}, {"method": "GET", "route": "emp_open_skills_comp_employees/(.*)", "handler": "\\App\\Controllers\\Portal::emp_open_skills_comp_employees/$1"}, {"method": "GET", "route": "plans_manager", "handler": "\\App\\Controllers\\Plans::index"}, {"method": "GET", "route": "view_plans/(.*)", "handler": "\\App\\Controllers\\Plans::view_plans/$1"}, {"method": "GET", "route": "align_plans/(.*)", "handler": "\\App\\Controllers\\Plans::align_plans/$1"}, {"method": "GET", "route": "view_groups/(.*)", "handler": "\\App\\Controllers\\Plans::view_groups/$1"}, {"method": "GET", "route": "view_strategies/(.*)", "handler": "\\App\\Controllers\\Plans::view_strategies/$1"}, {"method": "GET", "route": "view_programs/(.*)", "handler": "\\App\\Controllers\\Plans::view_programs/$1"}, {"method": "GET", "route": "assessments_manager", "handler": "\\App\\Controllers\\Assessments::index"}, {"method": "GET", "route": "view_assess_exercises/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_exercises/$1"}, {"method": "GET", "route": "open_assess_exercise/(.*)", "handler": "\\App\\Controllers\\Assessments::open_assess_exercise/$1"}, {"method": "GET", "route": "view_assess_items_groups/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_items_groups/$1"}, {"method": "GET", "route": "view_assess_items/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_items/$1"}, {"method": "GET", "route": "open_assess_score_group/(.*)", "handler": "\\App\\Controllers\\Assessments::open_assess_score_group/$1"}, {"method": "GET", "route": "open_assess_score_items/(.*)", "handler": "\\App\\Controllers\\Assessments::open_assess_score_items/$1"}, {"method": "GET", "route": "view_assess_report_groupings/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_report_groupings/$1"}, {"method": "GET", "route": "view_assess_report_items/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_report_items/$1"}, {"method": "GET", "route": "view_assess_reports/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_reports/$1"}, {"method": "GET", "route": "view_assess_reports_summary_tables/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_reports_summary_tables/$1"}, {"method": "GET", "route": "view_assess_reports_raw_scores/(.*)", "handler": "\\App\\Controllers\\Assessments::view_assess_reports_raw_scores/$1"}, {"method": "GET", "route": "activities", "handler": "\\App\\Controllers\\Activities::index"}, {"method": "GET", "route": "new_activity", "handler": "\\App\\Controllers\\Activities::new_activity"}, {"method": "GET", "route": "open_activity/(.*)", "handler": "\\App\\Controllers\\Activities::open_activity/$1"}, {"method": "GET", "route": "open_qualitative_unpack/(.*)", "handler": "\\App\\Controllers\\Activities::open_qualitative_unpack/$1"}, {"method": "GET", "route": "open_qualitative_item/(.*)", "handler": "\\App\\Controllers\\Activities::open_qualitative_item/$1"}, {"method": "GET", "route": "reports_qualitative_dashboard/(.*)", "handler": "\\App\\Controllers\\Reports::reports_qualitative_dashboard/$1"}, {"method": "GET", "route": "da<PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::index"}, {"method": "GET", "route": "dlogout", "handler": "\\App\\Controllers\\Dakoii::logout"}, {"method": "GET", "route": "ddash", "handler": "\\App\\Controllers\\Dakoii::ddash"}, {"method": "GET", "route": "dopen_org/(.*)", "handler": "\\App\\Controllers\\Dakoii::open_org/$1"}, {"method": "GET", "route": "dlist_org", "handler": "\\App\\Controllers\\Dakoii::list_org"}, {"method": "GET", "route": "testa", "handler": "\\App\\Controllers\\Test::index"}, {"method": "GET", "route": "ajax", "handler": "\\App\\Controllers\\Test::ajax"}, {"method": "GET", "route": "ajaxform", "handler": "\\App\\Controllers\\Test::ajaxform"}, {"method": "GET", "route": "testmap", "handler": "\\App\\Controllers\\Test::testmap"}, {"method": "POST", "route": "login", "handler": "\\App\\Controllers\\Home::login"}, {"method": "POST", "route": "dologin", "handler": "\\App\\Controllers\\Home::dologin"}, {"method": "POST", "route": "gofindme", "handler": "\\App\\Controllers\\Home::gofindme"}, {"method": "POST", "route": "open_profile", "handler": "\\App\\Controllers\\Home::open_profile"}, {"method": "POST", "route": "create_employees", "handler": "\\App\\Controllers\\Employees::create_employees"}, {"method": "POST", "route": "update_employees", "handler": "\\App\\Controllers\\Employees::update_employees"}, {"method": "POST", "route": "create_plans", "handler": "\\App\\Controllers\\Plans::create_plans"}, {"method": "POST", "route": "update_plans", "handler": "\\App\\Controllers\\Plans::update_plans"}, {"method": "POST", "route": "delete_plans", "handler": "\\App\\Controllers\\Plans::delete_plans"}, {"method": "POST", "route": "create_plan_groups", "handler": "\\App\\Controllers\\Plans::create_plan_groups"}, {"method": "POST", "route": "update_plan_groups", "handler": "\\App\\Controllers\\Plans::update_plan_groups"}, {"method": "POST", "route": "delete_plan_groups", "handler": "\\App\\Controllers\\Plans::delete_plan_groups"}, {"method": "POST", "route": "create_strategies", "handler": "\\App\\Controllers\\Plans::create_strategies"}, {"method": "POST", "route": "update_strategies", "handler": "\\App\\Controllers\\Plans::update_strategies"}, {"method": "POST", "route": "delete_strategies", "handler": "\\App\\Controllers\\Plans::delete_strategies"}, {"method": "POST", "route": "create_programs", "handler": "\\App\\Controllers\\Plans::create_programs"}, {"method": "POST", "route": "update_programs", "handler": "\\App\\Controllers\\Plans::update_programs"}, {"method": "POST", "route": "delete_programs", "handler": "\\App\\Controllers\\Plans::delete_programs"}, {"method": "POST", "route": "create_pro_act", "handler": "\\App\\Controllers\\Plans::create_pro_act"}, {"method": "POST", "route": "update_pro_act", "handler": "\\App\\Controllers\\Plans::update_pro_act"}, {"method": "POST", "route": "create_assess_plan", "handler": "\\App\\Controllers\\Assessments::create_assess_plan"}, {"method": "POST", "route": "update_assess_plan", "handler": "\\App\\Controllers\\Assessments::update_assess_plan"}, {"method": "POST", "route": "delete_assess_plan", "handler": "\\App\\Controllers\\Assessments::delete_assess_plan"}, {"method": "POST", "route": "create_assessment_exercise", "handler": "\\App\\Controllers\\Assessments::create_assessment_exercise"}, {"method": "POST", "route": "update_assessment_exercise", "handler": "\\App\\Controllers\\Assessments::update_assessment_exercise"}, {"method": "POST", "route": "delete_assessment_exercise", "handler": "\\App\\Controllers\\Assessments::delete_assessment_exercise"}, {"method": "POST", "route": "create_assess_items_group", "handler": "\\App\\Controllers\\Assessments::create_assess_items_group"}, {"method": "POST", "route": "update_assess_items_group", "handler": "\\App\\Controllers\\Assessments::update_assess_items_group"}, {"method": "POST", "route": "delete_assess_items_group", "handler": "\\App\\Controllers\\Assessments::delete_assess_items_group"}, {"method": "POST", "route": "import_assess_items_groups", "handler": "\\App\\Controllers\\Assessments::import_assess_items_groups"}, {"method": "POST", "route": "create_assess_report_group", "handler": "\\App\\Controllers\\Assessments::create_assess_report_group"}, {"method": "POST", "route": "update_assess_report_group", "handler": "\\App\\Controllers\\Assessments::update_assess_report_group"}, {"method": "POST", "route": "delete_assess_report_group", "handler": "\\App\\Controllers\\Assessments::delete_assess_report_group"}, {"method": "POST", "route": "import_assess_items", "handler": "\\App\\Controllers\\Assessments::import_assess_items"}, {"method": "POST", "route": "create_assess_items", "handler": "\\App\\Controllers\\Assessments::create_assess_items"}, {"method": "POST", "route": "update_assess_items", "handler": "\\App\\Controllers\\Assessments::update_assess_items"}, {"method": "POST", "route": "delete_assess_items", "handler": "\\App\\Controllers\\Assessments::delete_assess_items"}, {"method": "POST", "route": "create_assess_report_items", "handler": "\\App\\Controllers\\Assessments::create_assess_report_items"}, {"method": "POST", "route": "delete_assess_report_items", "handler": "\\App\\Controllers\\Assessments::delete_assess_report_items"}, {"method": "POST", "route": "enter_score_items", "handler": "\\App\\Controllers\\Assessments::enter_score_items"}, {"method": "POST", "route": "update_target_rates", "handler": "\\App\\Controllers\\Assessments::update_target_rates"}, {"method": "POST", "route": "import_qualitative_data", "handler": "\\App\\Controllers\\Activities::import_qualitative_data"}, {"method": "POST", "route": "create_qualitative_item", "handler": "\\App\\Controllers\\Activities::create_qualitative_item"}, {"method": "POST", "route": "update_qualitative_item", "handler": "\\App\\Controllers\\Activities::update_qualitative_item"}, {"method": "POST", "route": "delete_qualitative_item", "handler": "\\App\\Controllers\\Activities::delete_qualitative_item"}, {"method": "POST", "route": "bg_qualitative_color", "handler": "\\App\\Controllers\\Activities::bg_qualitative_color"}, {"method": "POST", "route": "qualitative_for_score", "handler": "\\App\\Controllers\\Activities::qualitative_for_score"}, {"method": "POST", "route": "download_qualitative_data", "handler": "\\App\\Controllers\\Activities::download_qualitative_data"}, {"method": "POST", "route": "import_qualitative_score", "handler": "\\App\\Controllers\\Activities::import_qualitative_score"}, {"method": "POST", "route": "create_activity", "handler": "\\App\\Controllers\\Activities::create_activity"}, {"method": "POST", "route": "update_activity", "handler": "\\App\\Controllers\\Activities::update_activity"}, {"method": "POST", "route": "update_activity_status", "handler": "\\App\\Controllers\\Activities::update_activity_status"}, {"method": "POST", "route": "dlogin", "handler": "\\App\\Controllers\\Dakoii::login"}, {"method": "POST", "route": "dad<PERSON>g", "handler": "\\App\\Controllers\\Dakoii::addorg"}, {"method": "POST", "route": "deditorg", "handler": "\\App\\Controllers\\Dakoii::editorg"}, {"method": "POST", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::adduser"}, {"method": "POST", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\Dakoii::create_admin"}, {"method": "POST", "route": "ajax", "handler": "\\App\\Controllers\\Test::ajax"}, {"method": "POST", "route": "ajaxform", "handler": "\\App\\Controllers\\Test::ajaxform"}]}, "badgeValue": 50, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "8.68", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "1.02", "count": 23}}}, "badgeValue": 24, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.622683, "duration": 0.008682966232299805}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.80217, "duration": 5.1021575927734375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.812207, "duration": 3.0994415283203125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.813102, "duration": 5.888938903808594e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.822495, "duration": 5.0067901611328125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.825123, "duration": 4.1961669921875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.8262, "duration": 4.1961669921875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.85104, "duration": 7.009506225585938e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.852298, "duration": 5.0067901611328125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.853294, "duration": 4.7206878662109375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.856971, "duration": 2.193450927734375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.857706, "duration": 4.482269287109375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.858693, "duration": 6.723403930664062e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.859626, "duration": 2.6941299438476562e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.860427, "duration": 4.7206878662109375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.861522, "duration": 4.410743713378906e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.862516, "duration": 3.218650817871094e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.863653, "duration": 5.1021575927734375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.865086, "duration": 5.602836608886719e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.866028, "duration": 4.1961669921875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.866987, "duration": 3.910064697265625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.868141, "duration": 4.696846008300781e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.869622, "duration": 2.6941299438476562e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.870882, "duration": 2.7894973754882812e-05}]}], "vars": {"varData": {"View Data": {"title": "Dashboard", "menu": "dashboard", "activePlans": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 1</dt></dl></div>", "completedAssessments": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 0</dt></dl></div>", "pendingReviews": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 0</dt></dl></div>", "totalItems": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 58</dt></dl></div>", "criticalItems": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 42</dt></dl></div>", "activeExercises": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (3)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (3)</li><li>Contents (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>ucode</th><th>orgcode</th><th>plan_id</th><th>code</th><th>title</th><th>date_from</th><th>date_to</th><th>bg_color</th><th>max_rate</th><th>min_rate</th><th>status</th><th>status_at</th><th>status_by</th><th>created_at</th><th>updated_at</th><th>create_by</th><th>update_by</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">12</td><td title=\"string (28)\">ASSEX66108cf7abee91712360695</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (8)\">ASX23451</td><td title=\"string (3)\">Wan</td><td title=\"string (10)\">2024-04-03</td><td title=\"string (10)\">2024-04-19</td><td title=\"string (0)\"></td><td title=\"string (2)\">10</td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-06 09:44:55</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-06 09:44:55</td><td title=\"string (19)\">2024-04-07 22:33:47</td><td title=\"string (5)\">Minad</td><td title=\"string (5)\">Minad</td></tr><tr><th>1</th><td title=\"string (2)\">14</td><td title=\"string (28)\">ASSEX66108d272dd501712360743</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (8)\">ASX23453</td><td title=\"string (14)\">Test Event One</td><td title=\"string (10)\">2024-04-04</td><td title=\"string (10)\">2024-04-10</td><td title=\"string (0)\"></td><td title=\"string (1)\">0</td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-06 09:45:43</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-06 09:45:43</td><td title=\"string (19)\">2024-04-06 09:45:43</td><td title=\"string (5)\">Minad</td><td title=\"string (0)\"></td></tr><tr><th>2</th><td title=\"string (2)\">15</td><td title=\"string (28)\">ASSEX66108dae0cdd61712360878</td><td title=\"string (4)\">2345</td><td title=\"string (1)\">2</td><td title=\"string (8)\">ASX23454</td><td title=\"string (22)\">ESPIPDP Review Scoring</td><td title=\"string (10)\">2024-04-03</td><td title=\"string (10)\">2024-04-11</td><td title=\"string (0)\"></td><td title=\"string (1)\">5</td><td title=\"string (1)\">0</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2024-04-06 09:47:58</td><td title=\"string (5)\">Minad</td><td title=\"string (19)\">2024-04-06 09:47:58</td><td title=\"string (19)\">2025-05-18 21:29:05</td><td title=\"string (5)\">Minad</td><td title=\"string (5)\">Minad</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (18)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSEX66108cf7abee91712360695\"<div class=\"access-path\">$value[0]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[0]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[0]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"ASX23451\"<div class=\"access-path\">$value[0]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (3) \"Wan\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_from</dfn> =&gt; <var>string</var> (10) \"2024-04-03\"<div class=\"access-path\">$value[0]['date_from']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_to</dfn> =&gt; <var>string</var> (10) \"2024-04-19\"<div class=\"access-path\">$value[0]['date_to']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>max_rate</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[0]['max_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>min_rate</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[0]['min_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:44:55\"<div class=\"access-path\">$value[0]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:44:55\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:33:47\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (18)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSEX66108d272dd501712360743\"<div class=\"access-path\">$value[1]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[1]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[1]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"ASX23453\"<div class=\"access-path\">$value[1]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (14) \"Test Event One\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_from</dfn> =&gt; <var>string</var> (10) \"2024-04-04\"<div class=\"access-path\">$value[1]['date_from']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_to</dfn> =&gt; <var>string</var> (10) \"2024-04-10\"<div class=\"access-path\">$value[1]['date_to']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>max_rate</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]['max_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>min_rate</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]['min_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:45:43\"<div class=\"access-path\">$value[1]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:45:43\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:45:43\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (18)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSEX66108dae0cdd61712360878\"<div class=\"access-path\">$value[2]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[2]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[2]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"ASX23454\"<div class=\"access-path\">$value[2]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (22) \"ESPIPDP Review Scoring\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_from</dfn> =&gt; <var>string</var> (10) \"2024-04-03\"<div class=\"access-path\">$value[2]['date_from']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_to</dfn> =&gt; <var>string</var> (10) \"2024-04-11\"<div class=\"access-path\">$value[2]['date_to']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>max_rate</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[2]['max_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>min_rate</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[2]['min_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:47:58\"<div class=\"access-path\">$value[2]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:47:58\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:29:05\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['update_by']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "groupLabels": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (54)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>0</dfn> =&gt; <var>string</var> (26) \" Agriculture and Livestock\"<div class=\"access-path\">$value[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>1</dfn> =&gt; <var>string</var> (21) \" Audit and Compliance\"<div class=\"access-path\">$value[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>2</dfn> =&gt; <var>string</var> (43) \" Climate Change, Environment &amp; Carbon Trade\"<div class=\"access-path\">$value[2]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>3</dfn> =&gt; <var>string</var> (35) \" Community Development and Churches\"<div class=\"access-path\">$value[3]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>4</dfn> =&gt; <var>string</var> (17) \" Culture and Arts\"<div class=\"access-path\">$value[4]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>5</dfn> =&gt; <var>string</var> (25) \" Disaster Risk Management\"<div class=\"access-path\">$value[5]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>6</dfn> =&gt; <var>string</var> (20) \" Financial Inclusion\"<div class=\"access-path\">$value[6]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>7</dfn> =&gt; <var>string</var> (21) \" Financial Management\"<div class=\"access-path\">$value[7]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>8</dfn> =&gt; <var>string</var> (10) \" Fisheries\"<div class=\"access-path\">$value[8]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>9</dfn> =&gt; <var>string</var> (26) \" Forestry and Biodiversity\"<div class=\"access-path\">$value[9]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>10</dfn> =&gt; <var>string</var> (7) \" Gender\"<div class=\"access-path\">$value[10]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>11</dfn> =&gt; <var>string</var> (25) \" HIV Aids and Social Ills\"<div class=\"access-path\">$value[11]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>12</dfn> =&gt; <var>string</var> (27) \" Human Resource Development\"<div class=\"access-path\">$value[12]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>13</dfn> =&gt; <var>string</var> (7) \" Mining\"<div class=\"access-path\">$value[13]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>14</dfn> =&gt; <var>string</var> (53) \" Monitoring and Evaluation of programmes and projects\"<div class=\"access-path\">$value[14]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>15</dfn> =&gt; <var>string</var> (29) \" Small and Medium Enterprises\"<div class=\"access-path\">$value[15]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>16</dfn> =&gt; <var>string</var> (7) \" Sports\"<div class=\"access-path\">$value[16]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>17</dfn> =&gt; <var>string</var> (8) \" Tourism\"<div class=\"access-path\">$value[17]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>18</dfn> =&gt; <var>string</var> (39) \" Vulnerable and Disadvantage Population\"<div class=\"access-path\">$value[18]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>19</dfn> =&gt; <var>string</var> (6) \" Youth\"<div class=\"access-path\">$value[19]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>20</dfn> =&gt; <var>string</var> (32) \"5.1.6 Forestry and Biodiversity \"<div class=\"access-path\">$value[20]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>21</dfn> =&gt; <var>string</var> (12) \"5.1.7 Mining\"<div class=\"access-path\">$value[21]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>22</dfn> =&gt; <var>string</var> (25) \"Agriculture and lifestock\"<div class=\"access-path\">$value[22]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>23</dfn> =&gt; <var>string</var> (13) \"Air Transport\"<div class=\"access-path\">$value[23]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>24</dfn> =&gt; <var>string</var> (13) \"Business Arms\"<div class=\"access-path\">$value[24]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>25</dfn> =&gt; <var>string</var> (8) \"Commerce\"<div class=\"access-path\">$value[25]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>26</dfn> =&gt; <var>string</var> (9) \"Education\"<div class=\"access-path\">$value[26]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>27</dfn> =&gt; <var>string</var> (6) \"Energy\"<div class=\"access-path\">$value[27]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>28</dfn> =&gt; <var>string</var> (10) \"Fisheries \"<div class=\"access-path\">$value[28]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>29</dfn> =&gt; <var>string</var> (8) \"Forestry\"<div class=\"access-path\">$value[29]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>30</dfn> =&gt; <var>string</var> (10) \"Governance\"<div class=\"access-path\">$value[30]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>31</dfn> =&gt; <var>string</var> (20) \"Health and Nutrition\"<div class=\"access-path\">$value[31]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>32</dfn> =&gt; <var>string</var> (50) \"Higher Education, Research, Science and Technology\"<div class=\"access-path\">$value[32]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>33</dfn> =&gt; <var>string</var> (29) \"Information and Communication\"<div class=\"access-path\">$value[33]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>34</dfn> =&gt; <var>string</var> (27) \"Land Access and Development\"<div class=\"access-path\">$value[34]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>35</dfn> =&gt; <var>string</var> (14) \"Land Transport\"<div class=\"access-path\">$value[35]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>36</dfn> =&gt; <var>string</var> (15) \"Law and Justice\"<div class=\"access-path\">$value[36]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>37</dfn> =&gt; <var>string</var> (26) \"Marine and Water Transport\"<div class=\"access-path\">$value[37]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>38</dfn> =&gt; <var>string</var> (10) \"Population\"<div class=\"access-path\">$value[38]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>39</dfn> =&gt; <var>string</var> (21) \"Public Administration\"<div class=\"access-path\">$value[39]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>40</dfn> =&gt; <var>string</var> (12) \"Sector Eight\"<div class=\"access-path\">$value[40]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>41</dfn> =&gt; <var>string</var> (11) \"Sector Five\"<div class=\"access-path\">$value[41]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>42</dfn> =&gt; <var>string</var> (11) \"Sector Four\"<div class=\"access-path\">$value[42]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>43</dfn> =&gt; <var>string</var> (10) \"Sector One\"<div class=\"access-path\">$value[43]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>44</dfn> =&gt; <var>string</var> (12) \"Sector Seven\"<div class=\"access-path\">$value[44]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>45</dfn> =&gt; <var>string</var> (10) \"Sector Six\"<div class=\"access-path\">$value[45]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>46</dfn> =&gt; <var>string</var> (12) \"Sector Three\"<div class=\"access-path\">$value[46]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>47</dfn> =&gt; <var>string</var> (10) \"Sector Two\"<div class=\"access-path\">$value[47]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>48</dfn> =&gt; <var>string</var> (20) \"Sector: Business Arm\"<div class=\"access-path\">$value[48]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>49</dfn> =&gt; <var>string</var> (32) \"SECTOR:5.1.9 Financial Inclusion\"<div class=\"access-path\">$value[49]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>50</dfn> =&gt; <var>string</var> (28) \"Small and Medium Enterprises\"<div class=\"access-path\">$value[50]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>51</dfn> =&gt; <var>string</var> (7) \"Tourism\"<div class=\"access-path\">$value[51]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>52</dfn> =&gt; <var>string</var> (12) \"Urbanisation\"<div class=\"access-path\">$value[52]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>53</dfn> =&gt; <var>string</var> (28) \"Water Sanitation and Hygiene\"<div class=\"access-path\">$value[53]</div></dt></dl></dd></dl></div>", "groupCounts": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (54)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>0</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>1</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>2</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>3</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>4</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>5</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>6</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>7</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[7]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>8</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[8]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>9</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[9]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>10</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[10]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>11</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[11]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>12</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[12]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>13</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[13]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>14</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[14]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>15</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[15]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>16</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[16]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>17</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[17]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>18</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[18]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>19</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[19]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>20</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[20]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>21</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[21]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>22</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[22]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>23</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[23]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>24</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[24]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>25</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[25]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>26</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[26]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>27</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[27]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>28</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[28]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>29</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[29]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>30</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[30]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>31</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[31]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>32</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[32]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>33</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[33]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>34</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[34]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>35</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[35]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>36</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[36]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>37</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[37]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>38</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[38]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>39</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[39]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>40</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[40]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>41</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[41]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>42</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[42]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>43</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[43]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>44</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[44]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>45</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[45]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>46</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[46]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>47</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[47]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>48</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[48]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>49</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[49]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>50</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[50]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>51</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[51]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>52</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[52]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>53</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[53]</div></dt></dl></dd></dl></div>", "groupColors": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (6)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>string</var> (23) \"rgba(60, 141, 188, 0.8)\"<div class=\"kint-color-preview\"><div style=\"background:rgba(60, 141, 188, 0.8)\"></div></div><div class=\"access-path\">$value[0]</div></dt><dd><pre><dfn>#3C8DBC</dfn>\n<dfn>#3C8DBCCC</dfn>\n<dfn>rgba(60, 141, 188, 0.8)</dfn>\n<dfn>hsla(202, 51%, 48%, 0.8)</dfn>\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>string</var> (24) \"rgba(210, 214, 222, 0.8)\"<div class=\"kint-color-preview\"><div style=\"background:rgba(210, 214, 222, 0.8)\"></div></div><div class=\"access-path\">$value[1]</div></dt><dd><pre><dfn>#D2D6DE</dfn>\n<dfn>#D2D6DECC</dfn>\n<dfn>rgba(210, 214, 222, 0.8)</dfn>\n<dfn>hsla(219, 15%, 84%, 0.8)</dfn>\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>string</var> (22) \"rgba(0, 192, 239, 0.8)\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 192, 239, 0.8)\"></div></div><div class=\"access-path\">$value[2]</div></dt><dd><pre><dfn>#00C0EF</dfn>\n<dfn>#00C0EFCC</dfn>\n<dfn>rgba(0, 192, 239, 0.8)</dfn>\n<dfn>hsla(191, 100%, 46%, 0.8)</dfn>\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>3</dfn> =&gt; <var>string</var> (22) \"rgba(76, 175, 80, 0.8)\"<div class=\"kint-color-preview\"><div style=\"background:rgba(76, 175, 80, 0.8)\"></div></div><div class=\"access-path\">$value[3]</div></dt><dd><pre><dfn>#4CAF50</dfn>\n<dfn>#4CAF50CC</dfn>\n<dfn>rgba(76, 175, 80, 0.8)</dfn>\n<dfn>hsla(122, 39%, 49%, 0.8)</dfn>\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>4</dfn> =&gt; <var>string</var> (22) \"rgba(255, 152, 0, 0.8)\"<div class=\"kint-color-preview\"><div style=\"background:rgba(255, 152, 0, 0.8)\"></div></div><div class=\"access-path\">$value[4]</div></dt><dd><pre><dfn>#FF9800</dfn>\n<dfn>#FF9800CC</dfn>\n<dfn>rgba(255, 152, 0, 0.8)</dfn>\n<dfn>hsla(35, 100%, 50%, 0.8)</dfn>\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>5</dfn> =&gt; <var>string</var> (22) \"rgba(233, 30, 99, 0.8)\"<div class=\"kint-color-preview\"><div style=\"background:rgba(233, 30, 99, 0.8)\"></div></div><div class=\"access-path\">$value[5]</div></dt><dd><pre><dfn>#E91E63</dfn>\n<dfn>#E91E63CC</dfn>\n<dfn>rgba(233, 30, 99, 0.8)</dfn>\n<dfn>hsla(339, 82%, 51%, 0.8)</dfn>\n</pre></dd></dl></dd></dl></div>", "totalGroups": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 54</dt></dl></div>", "assessmentGroups": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (54)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (54)</li><li>Contents (54)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>title</th><th>count</th></tr></thead><tbody><tr><th>0</th><td title=\"string (26)\"> Agriculture and Livestock</td><td title=\"string (1)\">1</td></tr><tr><th>1</th><td title=\"string (21)\"> Audit and Compliance</td><td title=\"string (1)\">1</td></tr><tr><th>2</th><td title=\"string (43)\"> Climate Change, Environment &amp; Carbon Trade</td><td title=\"string (1)\">1</td></tr><tr><th>3</th><td title=\"string (35)\"> Community Development and Churches</td><td title=\"string (1)\">1</td></tr><tr><th>4</th><td title=\"string (17)\"> Culture and Arts</td><td title=\"string (1)\">1</td></tr><tr><th>5</th><td title=\"string (25)\"> Disaster Risk Management</td><td title=\"string (1)\">1</td></tr><tr><th>6</th><td title=\"string (20)\"> Financial Inclusion</td><td title=\"string (1)\">1</td></tr><tr><th>7</th><td title=\"string (21)\"> Financial Management</td><td title=\"string (1)\">1</td></tr><tr><th>8</th><td title=\"string (10)\"> Fisheries</td><td title=\"string (1)\">1</td></tr><tr><th>9</th><td title=\"string (26)\"> Forestry and Biodiversity</td><td title=\"string (1)\">1</td></tr><tr><th>10</th><td title=\"string (7)\"> Gender</td><td title=\"string (1)\">1</td></tr><tr><th>11</th><td title=\"string (25)\"> HIV Aids and Social Ills</td><td title=\"string (1)\">1</td></tr><tr><th>12</th><td title=\"string (27)\"> Human Resource Development</td><td title=\"string (1)\">1</td></tr><tr><th>13</th><td title=\"string (7)\"> Mining</td><td title=\"string (1)\">1</td></tr><tr><th>14</th><td title=\"string (53)\"> Monitoring and Evaluation of programmes and projects</td><td title=\"string (1)\">1</td></tr><tr><th>15</th><td title=\"string (29)\"> Small and Medium Enterprises</td><td title=\"string (1)\">1</td></tr><tr><th>16</th><td title=\"string (7)\"> Sports</td><td title=\"string (1)\">1</td></tr><tr><th>17</th><td title=\"string (8)\"> Tourism</td><td title=\"string (1)\">1</td></tr><tr><th>18</th><td title=\"string (39)\"> Vulnerable and Disadvantage Population</td><td title=\"string (1)\">1</td></tr><tr><th>19</th><td title=\"string (6)\"> Youth</td><td title=\"string (1)\">1</td></tr><tr><th>20</th><td title=\"string (32)\">5.1.6 Forestry and Biodiversity </td><td title=\"string (1)\">1</td></tr><tr><th>21</th><td title=\"string (12)\">5.1.7 Mining</td><td title=\"string (1)\">1</td></tr><tr><th>22</th><td title=\"string (25)\">Agriculture and lifestock</td><td title=\"string (1)\">1</td></tr><tr><th>23</th><td title=\"string (13)\">Air Transport</td><td title=\"string (1)\">1</td></tr><tr><th>24</th><td title=\"string (13)\">Business Arms</td><td title=\"string (1)\">1</td></tr><tr><th>25</th><td title=\"string (8)\">Commerce</td><td title=\"string (1)\">1</td></tr><tr><th>26</th><td title=\"string (9)\">Education</td><td title=\"string (1)\">1</td></tr><tr><th>27</th><td title=\"string (6)\">Energy</td><td title=\"string (1)\">1</td></tr><tr><th>28</th><td title=\"string (10)\">Fisheries </td><td title=\"string (1)\">1</td></tr><tr><th>29</th><td title=\"string (8)\">Forestry</td><td title=\"string (1)\">1</td></tr><tr><th>30</th><td title=\"string (10)\">Governance</td><td title=\"string (1)\">1</td></tr><tr><th>31</th><td title=\"string (20)\">Health and Nutrition</td><td title=\"string (1)\">1</td></tr><tr><th>32</th><td title=\"string (50)\">Higher Education, Research, Science and Technology</td><td title=\"string (1)\">1</td></tr><tr><th>33</th><td title=\"string (29)\">Information and Communication</td><td title=\"string (1)\">1</td></tr><tr><th>34</th><td title=\"string (27)\">Land Access and Development</td><td title=\"string (1)\">1</td></tr><tr><th>35</th><td title=\"string (14)\">Land Transport</td><td title=\"string (1)\">1</td></tr><tr><th>36</th><td title=\"string (15)\">Law and Justice</td><td title=\"string (1)\">1</td></tr><tr><th>37</th><td title=\"string (26)\">Marine and Water Transport</td><td title=\"string (1)\">1</td></tr><tr><th>38</th><td title=\"string (10)\">Population</td><td title=\"string (1)\">1</td></tr><tr><th>39</th><td title=\"string (21)\">Public Administration</td><td title=\"string (1)\">1</td></tr><tr><th>40</th><td title=\"string (12)\">Sector Eight</td><td title=\"string (1)\">1</td></tr><tr><th>41</th><td title=\"string (11)\">Sector Five</td><td title=\"string (1)\">1</td></tr><tr><th>42</th><td title=\"string (11)\">Sector Four</td><td title=\"string (1)\">1</td></tr><tr><th>43</th><td title=\"string (10)\">Sector One</td><td title=\"string (1)\">1</td></tr><tr><th>44</th><td title=\"string (12)\">Sector Seven</td><td title=\"string (1)\">1</td></tr><tr><th>45</th><td title=\"string (10)\">Sector Six</td><td title=\"string (1)\">1</td></tr><tr><th>46</th><td title=\"string (12)\">Sector Three</td><td title=\"string (1)\">1</td></tr><tr><th>47</th><td title=\"string (10)\">Sector Two</td><td title=\"string (1)\">1</td></tr><tr><th>48</th><td title=\"string (20)\">Sector: Business Arm</td><td title=\"string (1)\">1</td></tr><tr><th>49</th><td title=\"string (32)\">SECTOR:5.1.9 Financial Inclusion</td><td title=\"string (1)\">1</td></tr><tr><th>50</th><td title=\"string (28)\">Small and Medium Enterprises</td><td title=\"string (1)\">1</td></tr><tr><th>51</th><td title=\"string (7)\">Tourism</td><td title=\"string (1)\">1</td></tr><tr><th>52</th><td title=\"string (12)\">Urbanisation</td><td title=\"string (1)\">1</td></tr><tr><th>53</th><td title=\"string (28)\">Water Sanitation and Hygiene</td><td title=\"string (1)\">1</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (26) \" Agriculture and Livestock\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (21) \" Audit and Compliance\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (43) \" Climate Change, Environment &amp; Carbon Trade\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (35) \" Community Development and Churches\"<div class=\"access-path\">$value[3]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (17) \" Culture and Arts\"<div class=\"access-path\">$value[4]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (25) \" Disaster Risk Management\"<div class=\"access-path\">$value[5]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (20) \" Financial Inclusion\"<div class=\"access-path\">$value[6]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (21) \" Financial Management\"<div class=\"access-path\">$value[7]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[7]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (10) \" Fisheries\"<div class=\"access-path\">$value[8]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[8]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (26) \" Forestry and Biodiversity\"<div class=\"access-path\">$value[9]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[9]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>10</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[10]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (7) \" Gender\"<div class=\"access-path\">$value[10]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[10]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>11</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[11]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (25) \" HIV Aids and Social Ills\"<div class=\"access-path\">$value[11]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[11]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>12</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[12]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (27) \" Human Resource Development\"<div class=\"access-path\">$value[12]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[12]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>13</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[13]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (7) \" Mining\"<div class=\"access-path\">$value[13]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[13]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>14</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[14]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (53) \" Monitoring and Evaluation of programmes and projects\"<div class=\"access-path\">$value[14]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[14]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>15</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[15]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (29) \" Small and Medium Enterprises\"<div class=\"access-path\">$value[15]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[15]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>16</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[16]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (7) \" Sports\"<div class=\"access-path\">$value[16]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[16]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>17</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[17]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (8) \" Tourism\"<div class=\"access-path\">$value[17]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[17]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>18</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[18]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (39) \" Vulnerable and Disadvantage Population\"<div class=\"access-path\">$value[18]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[18]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>19</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[19]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (6) \" Youth\"<div class=\"access-path\">$value[19]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[19]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>20</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[20]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (32) \"5.1.6 Forestry and Biodiversity \"<div class=\"access-path\">$value[20]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[20]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>21</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[21]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (12) \"5.1.7 Mining\"<div class=\"access-path\">$value[21]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[21]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>22</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[22]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (25) \"Agriculture and lifestock\"<div class=\"access-path\">$value[22]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[22]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>23</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[23]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (13) \"Air Transport\"<div class=\"access-path\">$value[23]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[23]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>24</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[24]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (13) \"Business Arms\"<div class=\"access-path\">$value[24]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[24]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>25</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[25]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (8) \"Commerce\"<div class=\"access-path\">$value[25]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[25]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>26</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[26]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (9) \"Education\"<div class=\"access-path\">$value[26]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[26]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>27</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[27]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (6) \"Energy\"<div class=\"access-path\">$value[27]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[27]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>28</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[28]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (10) \"Fisheries \"<div class=\"access-path\">$value[28]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[28]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>29</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[29]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (8) \"Forestry\"<div class=\"access-path\">$value[29]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[29]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>30</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[30]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (10) \"Governance\"<div class=\"access-path\">$value[30]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[30]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>31</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[31]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (20) \"Health and Nutrition\"<div class=\"access-path\">$value[31]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[31]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>32</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[32]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (50) \"Higher Education, Research, Science and Technology\"<div class=\"access-path\">$value[32]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[32]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>33</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[33]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (29) \"Information and Communication\"<div class=\"access-path\">$value[33]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[33]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>34</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[34]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (27) \"Land Access and Development\"<div class=\"access-path\">$value[34]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[34]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>35</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[35]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (14) \"Land Transport\"<div class=\"access-path\">$value[35]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[35]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>36</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[36]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (15) \"Law and Justice\"<div class=\"access-path\">$value[36]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[36]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>37</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[37]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (26) \"Marine and Water Transport\"<div class=\"access-path\">$value[37]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[37]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>38</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[38]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (10) \"Population\"<div class=\"access-path\">$value[38]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[38]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>39</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[39]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (21) \"Public Administration\"<div class=\"access-path\">$value[39]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[39]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>40</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[40]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (12) \"Sector Eight\"<div class=\"access-path\">$value[40]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[40]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>41</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[41]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (11) \"Sector Five\"<div class=\"access-path\">$value[41]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[41]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>42</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[42]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (11) \"Sector Four\"<div class=\"access-path\">$value[42]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[42]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>43</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[43]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (10) \"Sector One\"<div class=\"access-path\">$value[43]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[43]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>44</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[44]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (12) \"Sector Seven\"<div class=\"access-path\">$value[44]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[44]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>45</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[45]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (10) \"Sector Six\"<div class=\"access-path\">$value[45]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[45]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>46</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[46]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (12) \"Sector Three\"<div class=\"access-path\">$value[46]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[46]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>47</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[47]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (10) \"Sector Two\"<div class=\"access-path\">$value[47]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[47]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>48</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[48]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (20) \"Sector: Business Arm\"<div class=\"access-path\">$value[48]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[48]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>49</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[49]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (32) \"SECTOR:5.1.9 Financial Inclusion\"<div class=\"access-path\">$value[49]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[49]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>50</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[50]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (28) \"Small and Medium Enterprises\"<div class=\"access-path\">$value[50]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[50]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>51</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[51]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (7) \"Tourism\"<div class=\"access-path\">$value[51]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[51]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>52</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[52]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (12) \"Urbanisation\"<div class=\"access-path\">$value[52]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[52]['count']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>53</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[53]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (28) \"Water Sanitation and Hygiene\"<div class=\"access-path\">$value[53]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>count</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[53]['count']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "recentActivity": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (4)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (18)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSEX66108dae0cdd61712360878\"<div class=\"access-path\">$value[0]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[0]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[0]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"ASX23454\"<div class=\"access-path\">$value[0]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (22) \"ESPIPDP Review Scoring\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_from</dfn> =&gt; <var>string</var> (10) \"2024-04-03\"<div class=\"access-path\">$value[0]['date_from']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_to</dfn> =&gt; <var>string</var> (10) \"2024-04-11\"<div class=\"access-path\">$value[0]['date_to']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>max_rate</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[0]['max_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>min_rate</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[0]['min_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:47:58\"<div class=\"access-path\">$value[0]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:47:58\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 21:29:05\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[0]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (18)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSEX66108d272dd501712360743\"<div class=\"access-path\">$value[1]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[1]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[1]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"ASX23453\"<div class=\"access-path\">$value[1]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (14) \"Test Event One\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_from</dfn> =&gt; <var>string</var> (10) \"2024-04-04\"<div class=\"access-path\">$value[1]['date_from']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_to</dfn> =&gt; <var>string</var> (10) \"2024-04-10\"<div class=\"access-path\">$value[1]['date_to']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>max_rate</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]['max_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>min_rate</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]['min_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:45:43\"<div class=\"access-path\">$value[1]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:45:43\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:45:43\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[1]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (18)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASSEX66108cf7abee91712360695\"<div class=\"access-path\">$value[2]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[2]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[2]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (8) \"ASX23451\"<div class=\"access-path\">$value[2]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (3) \"Wan\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_from</dfn> =&gt; <var>string</var> (10) \"2024-04-03\"<div class=\"access-path\">$value[2]['date_from']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_to</dfn> =&gt; <var>string</var> (10) \"2024-04-19\"<div class=\"access-path\">$value[2]['date_to']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>max_rate</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[2]['max_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>min_rate</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[2]['min_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:44:55\"<div class=\"access-path\">$value[2]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:44:55\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2024-04-07 22:33:47\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[2]['update_by']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><nav></nav><dfn>ucode</dfn> =&gt; <var>string</var> (28) \"ASPLN6610835fbfa491712358239\"<div class=\"access-path\">$value[3]['ucode']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Base64</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>base64_decode(ucode)</dfn> <var>binary string</var> (21)<div class=\"access-path\">base64_decode($value[3]['ucode'])</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>orgcode</dfn> =&gt; <var>string</var> (4) \"2345\"<div class=\"access-path\">$value[3]['orgcode']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>code</dfn> =&gt; <var>string</var> (11) \"ESPIPDP2028\"<div class=\"access-path\">$value[3]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>title</dfn> =&gt; <var>string</var> (59) \"East Sepik Province Integrated Development Plan 2018 - 2028\"<div class=\"access-path\">$value[3]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_from</dfn> =&gt; <var>string</var> (10) \"2018-12-12\"<div class=\"access-path\">$value[3]['date_from']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>date_to</dfn> =&gt; <var>string</var> (10) \"2028-12-12\"<div class=\"access-path\">$value[3]['date_to']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>bg_color</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['bg_color']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:03:59\"<div class=\"access-path\">$value[3]['status_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>status_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['status_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2024-04-06 09:03:59\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-04-15 12:36:41\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>create_by</dfn> =&gt; <var>string</var> (5) \"Minad\"<div class=\"access-path\">$value[3]['create_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>update_by</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['update_by']</div></dt></dl></dd></dl></dd></dl></div>", "months": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (6)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>0</dfn> =&gt; <var>string</var> (3) \"Dec\"<div class=\"access-path\">$value[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>1</dfn> =&gt; <var>string</var> (3) \"Jan\"<div class=\"access-path\">$value[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>2</dfn> =&gt; <var>string</var> (3) \"Mar\"<div class=\"access-path\">$value[2]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>3</dfn> =&gt; <var>string</var> (3) \"Mar\"<div class=\"access-path\">$value[3]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>4</dfn> =&gt; <var>string</var> (3) \"Apr\"<div class=\"access-path\">$value[4]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>5</dfn> =&gt; <var>string</var> (3) \"May\"<div class=\"access-path\">$value[5]</div></dt></dl></dd></dl></div>", "completionRates": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (6)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>0</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>1</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>2</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[2]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>3</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[3]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>4</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[4]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>5</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[5]</div></dt></dl></dd></dl></div>", "targetRates": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-popup-trigger\" title=\"Open in new window\">&boxbox;</span><span class=\"kint-search-trigger\" title=\"Show search box\">&telrec;</span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (6)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>0</dfn> =&gt; <var>integer</var> 90<div class=\"access-path\">$value[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>1</dfn> =&gt; <var>integer</var> 85<div class=\"access-path\">$value[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>2</dfn> =&gt; <var>integer</var> 80<div class=\"access-path\">$value[2]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>3</dfn> =&gt; <var>integer</var> 75<div class=\"access-path\">$value[3]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>4</dfn> =&gt; <var>integer</var> 70<div class=\"access-path\">$value[4]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\">&rlarr;</span><dfn>5</dfn> =&gt; <var>integer</var> 65<div class=\"access-path\">$value[5]</div></dt></dl></dd></dl></div>", "averageScore": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>double</var> 2</dt></dl></div>", "upcomingExercises": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>array</var> (0)</dt></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>1748563328</pre>", "_ci_previous_url": "http://localhost/part/index.php/login", "username": "minad", "name": "<PERSON><PERSON>", "role": "admin", "status": "1", "orgname": "Morobe Provincial Administration", "orglogo": "http://localhost/ocna/public/uploads/org_logo/2345_1681386431.gif", "orgcode": "2345", "orgcountry": "PG", "orgprov": "12"}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Cache-Control": "max-age=0", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Sec-Ch-Ua": "&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Referer": "http://localhost/part/login", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9", "Cookie": "ci_session=5a84e94d4b5953607f864ecd293598da; csrf_cookie_name=02935e8a55c8a671a52c9b02e1b4dd08"}, "cookies": {"ci_session": "5a84e94d4b5953607f864ecd293598da", "csrf_cookie_name": "02935e8a55c8a671a52c9b02e1b4dd08"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.3.2", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/part", "timezone": "UTC", "locale": "en", "cspEnabled": false}}