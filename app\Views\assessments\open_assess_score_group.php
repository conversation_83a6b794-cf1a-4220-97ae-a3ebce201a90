<?= $this->extend("templates/adminlte/admindash"); ?>
<?= $this->section('content'); ?>



<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">

            <div class="col-sm-6">
                <h1> <i class="fas fa-clipboard-check"></i> Score Groups</h1>
                <h5 class="m-0"></h5>

            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item">Dashboard</li>
                    <li class="breadcrumb-item ">Assess Manager</li>
                    <li class="breadcrumb-item ">Exercise</li>
                    <li class="breadcrumb-item active">Scoreboard</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->

    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<section class=" container-fluid">
    <div class="row">
        <div class="col-md-12">
            <a href="<?= base_url() ?>open_assess_exercise/<?= $assex['ucode'] ?>" class="btn btn-outline-dark "> <i class="fa fa-reply" aria-hidden="true"></i> Back</a>

        </div>
    </div>
    <!-- ./col -->

    <div class="row pt-2">

        <div class="col-md-12 mb-2">
            <ul class="list-group">
                <!--tips: add .list-group-flush to the .list-group to remove some borders and rounded corners-->
                <li class="list-group-item "><b class=" float-left">Plan: </b> <span class=" float-right"> <?= $plan['code'] ?>. <?= $plan['title'] ?> </span> </li>
                <li class="list-group-item "><b>Exercise:</b> <span class=" float-right"><?= $assex['code'] ?>. <?= $assex['title'] ?></span> </li>
            </ul>
        </div>

        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-warning">
                    Item Groups
                </div>
                <div class="card-body p-0 table-responsive">
                    <table class="table text-nowrap">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Title</th>
                                <th>Count/Sum</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($item_groups as $row) : ?>
                                <tr>
                                    <td scope="row"><?= $row['code'] ?></td>
                                    <td><?= $row['title'] ?></td>
                                    <td>
                                        <?php
                                        $count = $score_sum = 0;
                                        foreach ($items as $item) {
                                            if ($item['assess_items_groups_id'] == $row['id']) {
                                                $score_sum += $item['score'];
                                                $count += 1;
                                            }
                                        }
                                        echo $count ."/". $score_sum;
                                        ?>
                                    </td>
                                    <td>
                                        <a href="<?= base_url() ?>open_assess_score_items/<?= $row['ucode'] ?>" class="btn btn-block btn-primary btn-sm"> <i class="fa fa-arrow-right" aria-hidden="true"></i> </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>
    <!-- ./row -->

</section>

</body>


<?= $this->endSection() ?>