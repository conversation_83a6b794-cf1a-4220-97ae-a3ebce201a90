<?php

//========================= fns displaying different types of charts =========================

function chart_radar($chartName, $chartLabels, $chartDataTargets, $chartDataAchieved)
{
?>
    <html>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

    <script>
        var ctx = document.getElementById("<?= $chartName ?>").getContext('2d');
        var myRadarChart = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: [<?= $chartLabels ?>],
                datasets: [{
                    label: 'Target',
                    data: [<?= $chartDataTargets ?>],
                    fill: true,
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgb(255, 99, 132)',
                    pointBackgroundColor: 'rgb(255, 99, 132)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgb(255, 99, 132)'
                }, {
                    label: 'Achieved',
                    data: [<?= $chartDataAchieved ?>],
                    fill: true,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgb(54, 162, 235)',
                    pointBackgroundColor: 'rgb(54, 162, 235)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgb(54, 162, 235)'
                }]
            },
            options: {
                elements: {
                    line: {
                        borderWidth: 3
                    }
                }
            },
        });
    </script>


    </html>
<?php
}


function chart_pie($chartName, $data_achieved, $data_toAchieve)
{
?>
    <html>
    <!-- Include D3.js library -->
    <script src="https://d3js.org/d3.v7.min.js"></script>

    <!-- <svg id="pieChart"></svg> -->

    <script>
        // Assuming $chartName gets replaced by the actual ID in your server-side script
        var chartContainer = d3.select("#<?= $chartName ?>");

        // Get the dimensions of the container
        var boundingRect = chartContainer.node().getBoundingClientRect();
        var width = boundingRect.width;
        var height = boundingRect.height; // Adjust height as necessary
        var radius = Math.min(width, height) / 2;

        // Continue with your setup
        var customColors = ['rgb(255, 99, 132)', 'rgb(54, 162, 235)'];
        var color = d3.scaleOrdinal(customColors);

        // Create SVG element dynamically based on container size
        var svg = chartContainer.append("svg")
            .attr("width", width)
            .attr("height", height)
            .append("g")
            .attr("transform", "translate(" + width / 2 + "," + height / 2 + ")");

        // Assuming data is already set up
        var data = [{
                label: 'To Achieve',
                value: <?= $data_toAchieve ?>
            },
            {
                label: 'Achieved',
                value: <?= $data_achieved ?>
            },
        ];

        // Rest of the script follows as before
        var arc = d3.arc()
            .innerRadius(0)
            .outerRadius(radius);

        var pie = d3.pie()
            .sort(null)
            .value(function(d) {
                return d.value;
            });

        var arcs = svg.selectAll("arc")
            .data(pie(data))
            .enter()
            .append("g")
            .attr("class", "arc");

        arcs.append("path")
            .attr("d", arc)
            .attr("fill", function(d, i) {
                return color(i);
            });

        // Adjust the text positioning and visibility based on slice size if necessary
        arcs.append("text")
            .attr("transform", function(d) {
                var centroid = arc.centroid(d);
                return "translate(" + centroid + ")";
            })
            .attr("text-anchor", "middle")
            .attr("font-size", "12px")
            .attr("fill", "#fff")
            .text(function(d) {
                return " (" + Math.round(d.data.value / d3.sum(data, function(d) {
                    return d.value;
                }) * 100) + "%)";
            });
    </script>

    </html>
<?php
}

function chart_stacked_bar($chartName, $chart_labels, $chart_data_achieved, $chart_data_gaps)
{
?>
    <html>
    <!-- <canvas id="stackedChartID"></canvas> -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        // Get the drawing context on the canvas
        var myContext = document.getElementById("<?= $chartName ?>").getContext('2d');
        var myChart = new Chart(myContext, {
            type: 'bar',
            data: {
                labels: [<?= $chart_labels ?>],
                datasets: [{
                    label: 'Achieved',
                    backgroundColor: "rgb(54, 162, 235)",
                    data: [<?= $chart_data_achieved ?>],
                }, {
                    label: 'Yet To Achieve',
                    backgroundColor: "rgb(255, 99, 132)",
                    data: [<?= $chart_data_gaps ?>],
                }],
            },
            options: {
                plugins: {
                    title: {
                        display: false,
                        text: 'Stacked Bar '
                    },
                },
                scales: {
                    x: {
                        stacked: true,
                    },
                    y: {
                        stacked: true
                    }
                }
            }
        });
    </script>

    </html>
<?php
}

function copychart_toimage($elementID, $btnCopyID)
{
?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.3.2/html2canvas.min.js"></script>

    <script>
        document.getElementById('<?= $btnCopyID ?>').addEventListener('click', function() {
            html2canvas(document.getElementById('<?= $elementID ?>')).then(function(canvas) {
                canvas.toBlob(function(blob) {
                    navigator.clipboard.write([new ClipboardItem({
                        'image/png': blob
                    })]).then(function() {
                        toastr.success('Copied to clipboard!');
                    }, function(error) {
                        console.error('Copying to clipboard failed:', error);
                        toastr.error('Failed to clipboard.');
                    });
                }, 'image/png');
            });
        });
    </script>

<?php
}

function export_table_toexcel($tableID, $filename, $btnExportID)
{
?>
    <!-- Required libraries for exporting to XLSX format -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.1/xlsx.full.min.js"></script>
    <script>
        // Function to export table data to XLSX file
        function exportToXLSX(filename) {
            let wb = XLSX.utils.table_to_book(document.getElementById('<?= $tableID ?>'), {
                sheet: "SheetJS"
            });
            XLSX.writeFile(wb, filename);
        }

        document.getElementById('<?= $btnExportID ?>').addEventListener('click', function() {
            exportToXLSX('<?= $filename ?>.xlsx');
        });
    </script>
<?php
}
