<?php

namespace App\Models;

use CodeIgniter\Model;

class assess_items_GroupsModel extends Model
{
    protected $table      = 'assess_items_groups';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'ucode', 'orgcode','plan_id','assess_exercise_id', 'code', 'title', 'bg_color','status','status_by','status_at',
        'create_by', 'update_by'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;
}
